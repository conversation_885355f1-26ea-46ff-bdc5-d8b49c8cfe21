{"mappings": ";;;;;;AAAA;;;;;;;;;;CAUC;;;;;AAyBM,IAAI,0DAAmB,CAAA,GAAA,YAAI,EAAE,aAAa,CAA+B;AAEhF,SAAS,0CAAoB,GAAuC;IAClE,IAAI,UAAU,CAAA,GAAA,iBAAS,EAAE,8CAAqB,CAAC;IAC/C,CAAA,GAAA,iBAAS,EAAE,SAAS;IAEpB,2BAA2B;IAC3B,IAAI,EAAC,KAAK,CAAC,EAAE,GAAG,YAAW,GAAG;IAC9B,OAAO;AACT;AAKO,MAAM,yDAAoB,CAAA,GAAA,YAAI,EAAE,UAAU,CAAC,SAAS,kBAAkB,KAA6B,EAAE,GAAmC;IAC7I,IAAI,YAAC,QAAQ,EAAE,GAAG,YAAW,GAAG;IAChC,IAAI,SAAS,CAAA,GAAA,mBAAW,EAAE;IAC1B,IAAI,UAAU;QACZ,GAAG,UAAU;QACb,KAAK;IACP;IAEA,qBACE,gCAAC,0CAAiB,QAAQ;QAAC,OAAO;OAC/B;AAGP;AAUO,SAAS,0CAA4D,KAA0B,EAAE,MAA0C;IAChJ,IAAI,cAAC,UAAU,EAAC,GAAG,CAAA,GAAA,yCAAO,EAAE;IAC5B,IAAI,iBAAC,aAAa,EAAC,GAAG,CAAA,GAAA,yCAAU,EAAE;IAClC,IAAI,eAAe,CAAA,GAAA,iBAAS,EAAE,YAAY;IAC1C,IAAI,WAAW,0CAAoB;IACnC,IAAI,mBAAmB,MAAM,UAAU,GAAG,CAAC,IAAI;IAC/C,IAAI,eAAe,CAAA,GAAA,aAAK,EAAE,MAAM,SAAS;IAEzC,CAAA,GAAA,gBAAQ,EAAE;QACR,IAAI,aAAa,OAAO,IAAI,OAAO,OAAO,EACxC,CAAA,GAAA,yCAAU,EAAE,OAAO,OAAO;QAE5B,aAAa,OAAO,GAAG;IACzB,GAAG;QAAC;KAAO;IAEX,kFAAkF;IAClF,IAAI,WAA+B,MAAM,mBAAmB,GAAG,KAAK;IACpE,IAAI,MAAM,UAAU,EAClB,WAAW;IAGb,OAAO;QACL,gBAAgB,CAAA,GAAA,iBAAS,EACvB;YACE,GAAG,YAAY;sBACf;QACF,GACA;IAEJ;AACF;AAMO,MAAM,0DAAY,CAAA,GAAA,iBAAS,EAAE,CAAC,YAAC,QAAQ,EAAE,GAAG,OAA+B,EAAE;IAClF,MAAM,CAAA,GAAA,mBAAW,EAAE;IACnB,IAAI,kBAAC,cAAc,EAAC,GAAG,0CAAa,OAAO;IAC3C,IAAI,QAAQ,CAAA,GAAA,YAAI,EAAE,QAAQ,CAAC,IAAI,CAAC;IAEhC,CAAA,GAAA,gBAAQ,EAAE;QACR,IAAI,QAAQ,GAAG,CAAC,QAAQ,KAAK,cAC3B;QAGF,IAAI,KAAK,IAAI,OAAO;QACpB,IAAI,CAAC,MAAM,CAAE,CAAA,cAAc,CAAA,GAAA,qBAAa,EAAE,IAAI,OAAO,AAAD,GAAI;YACtD,QAAQ,KAAK,CAAC;YACd;QACF;QAEA,IAAI,CAAC,MAAM,UAAU,IAAI,CAAC,CAAA,GAAA,kBAAU,EAAE,KAAK;YACzC,QAAQ,IAAI,CAAC;YACb;QACF;QAEA,IACE,GAAG,SAAS,KAAK,YACjB,GAAG,SAAS,KAAK,WACjB,GAAG,SAAS,KAAK,YACjB,GAAG,SAAS,KAAK,cACjB,GAAG,SAAS,KAAK,OACjB,GAAG,SAAS,KAAK,UACjB,GAAG,SAAS,KAAK,aACjB,GAAG,SAAS,KAAK,SACjB,GAAG,SAAS,KAAK,OACjB;YACA,IAAI,OAAO,GAAG,YAAY,CAAC;YAC3B,IAAI,CAAC,MACH,QAAQ,IAAI,CAAC;iBACR,IACL,2CAA2C;YAC3C,SAAS,iBACT,SAAS,YACT,SAAS,cACT,SAAS,cACT,SAAS,cACT,SAAS,UACT,SAAS,cACT,SAAS,sBACT,SAAS,mBACT,SAAS,YACT,SAAS,WACT,SAAS,eACT,SAAS,eACT,SAAS,YACT,SAAS,gBACT,SAAS,YACT,SAAS,SACT,SAAS,cACT,SAAS,aACT,SAAS,cACT,oDAAoD;YACpD,SAAS,SACT,SAAS,WACT,SAAS,eAET,QAAQ,IAAI,CAAC,CAAC,2DAA2D,EAAE,KAAK,EAAE,CAAC;QAEvF;IACF,GAAG;QAAC;QAAK,MAAM,UAAU;KAAC;IAE1B,aAAa;IACb,IAAI,WAAW,SAAS,CAAA,GAAA,YAAI,EAAE,OAAO,EAAE,MAAM,KAAK,MAAM,GAAG,GAAG,MAAM,KAAK,CAAC,GAAG;IAE7E,qBAAO,CAAA,GAAA,YAAI,EAAE,YAAY,CACvB,OACA;QACE,GAAG,CAAA,GAAA,iBAAS,EAAE,gBAAgB,MAAM,KAAK,CAAC;QAC1C,aAAa;QACb,KAAK,CAAA,GAAA,gBAAQ,EAAE,UAAU;IAC3B;AAEJ", "sources": ["packages/@react-aria/interactions/src/useFocusable.tsx"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {DOMAttributes, FocusableDOMProps, FocusableElement, FocusableProps, RefObject} from '@react-types/shared';\nimport {focusSafely} from './';\nimport {getOwnerWindow, isFocusable, mergeProps, mergeRefs, useObjectRef, useSyncRef} from '@react-aria/utils';\nimport React, {ForwardedRef, forwardRef, MutableRefObject, ReactElement, ReactNode, useContext, useEffect, useRef} from 'react';\nimport {useFocus} from './useFocus';\nimport {useKeyboard} from './useKeyboard';\n\nexport interface FocusableOptions<T = FocusableElement> extends FocusableProps<T>, FocusableDOMProps {\n  /** Whether focus should be disabled. */\n  isDisabled?: boolean\n}\n\nexport interface FocusableProviderProps extends DOMAttributes {\n  /** The child element to provide DOM props to. */\n  children?: ReactNode\n}\n\ninterface FocusableContextValue extends FocusableProviderProps {\n  ref?: MutableRefObject<FocusableElement | null>\n}\n\n// Exported for @react-aria/collections, which forwards this context.\n/** @private */\nexport let FocusableContext = React.createContext<FocusableContextValue | null>(null);\n\nfunction useFocusableContext(ref: RefObject<FocusableElement | null>): FocusableContextValue {\n  let context = useContext(FocusableContext) || {};\n  useSyncRef(context, ref);\n\n  // eslint-disable-next-line\n  let {ref: _, ...otherProps} = context;\n  return otherProps;\n}\n\n/**\n * Provides DOM props to the nearest focusable child.\n */\nexport const FocusableProvider = React.forwardRef(function FocusableProvider(props: FocusableProviderProps, ref: ForwardedRef<FocusableElement>) {\n  let {children, ...otherProps} = props;\n  let objRef = useObjectRef(ref);\n  let context = {\n    ...otherProps,\n    ref: objRef\n  };\n\n  return (\n    <FocusableContext.Provider value={context}>\n      {children}\n    </FocusableContext.Provider>\n  );\n});\n\nexport interface FocusableAria {\n  /** Props for the focusable element. */\n  focusableProps: DOMAttributes\n}\n\n/**\n * Used to make an element focusable and capable of auto focus.\n */\nexport function useFocusable<T extends FocusableElement = FocusableElement>(props: FocusableOptions<T>, domRef: RefObject<FocusableElement | null>): FocusableAria {\n  let {focusProps} = useFocus(props);\n  let {keyboardProps} = useKeyboard(props);\n  let interactions = mergeProps(focusProps, keyboardProps);\n  let domProps = useFocusableContext(domRef);\n  let interactionProps = props.isDisabled ? {} : domProps;\n  let autoFocusRef = useRef(props.autoFocus);\n\n  useEffect(() => {\n    if (autoFocusRef.current && domRef.current) {\n      focusSafely(domRef.current);\n    }\n    autoFocusRef.current = false;\n  }, [domRef]);\n\n  // Always set a tabIndex so that Safari allows focusing native buttons and inputs.\n  let tabIndex: number | undefined = props.excludeFromTabOrder ? -1 : 0;\n  if (props.isDisabled) {\n    tabIndex = undefined;\n  }\n\n  return {\n    focusableProps: mergeProps(\n      {\n        ...interactions,\n        tabIndex\n      },\n      interactionProps\n    )\n  };\n}\n\nexport interface FocusableComponentProps extends FocusableOptions {\n  children: ReactElement<DOMAttributes, string>\n}\n\nexport const Focusable = forwardRef(({children, ...props}: FocusableComponentProps, ref: ForwardedRef<FocusableElement>) => {\n  ref = useObjectRef(ref);\n  let {focusableProps} = useFocusable(props, ref);\n  let child = React.Children.only(children);\n\n  useEffect(() => {\n    if (process.env.NODE_ENV === 'production') {\n      return;\n    }\n\n    let el = ref.current;\n    if (!el || !(el instanceof getOwnerWindow(el).Element)) {\n      console.error('<Focusable> child must forward its ref to a DOM element.');\n      return;\n    }\n\n    if (!props.isDisabled && !isFocusable(el)) {\n      console.warn('<Focusable> child must be focusable. Please ensure the tabIndex prop is passed through.');\n      return;\n    }\n\n    if (\n      el.localName !== 'button' &&\n      el.localName !== 'input' &&\n      el.localName !== 'select' &&\n      el.localName !== 'textarea' &&\n      el.localName !== 'a' &&\n      el.localName !== 'area' &&\n      el.localName !== 'summary' &&\n      el.localName !== 'img' &&\n      el.localName !== 'svg'\n    ) {\n      let role = el.getAttribute('role');\n      if (!role) {\n        console.warn('<Focusable> child must have an interactive ARIA role.');\n      } else if (\n        // https://w3c.github.io/aria/#widget_roles\n        role !== 'application' &&\n        role !== 'button' &&\n        role !== 'checkbox' &&\n        role !== 'combobox' &&\n        role !== 'gridcell' &&\n        role !== 'link' &&\n        role !== 'menuitem' &&\n        role !== 'menuitemcheckbox' &&\n        role !== 'menuitemradio' &&\n        role !== 'option' &&\n        role !== 'radio' &&\n        role !== 'searchbox' &&\n        role !== 'separator' &&\n        role !== 'slider' &&\n        role !== 'spinbutton' &&\n        role !== 'switch' &&\n        role !== 'tab' &&\n        role !== 'tabpanel' &&\n        role !== 'textbox' &&\n        role !== 'treeitem' &&\n        // aria-describedby is also announced on these roles\n        role !== 'img' &&\n        role !== 'meter' &&\n        role !== 'progressbar'\n      ) {\n        console.warn(`<Focusable> child must have an interactive ARIA role. Got \"${role}\".`);\n      }\n    }\n  }, [ref, props.isDisabled]);\n\n  // @ts-ignore\n  let childRef = parseInt(React.version, 10) < 19 ? child.ref : child.props.ref;\n\n  return React.cloneElement(\n    child,\n    {\n      ...mergeProps(focusableProps, child.props),\n      // @ts-ignore\n      ref: mergeRefs(childRef, ref)\n    }\n  );\n});\n"], "names": [], "version": 3, "file": "useFocusable.module.js.map"}