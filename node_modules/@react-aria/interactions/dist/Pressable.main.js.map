{"mappings": ";;;;;;;;;;;;;;;AAAA;;;;;;;;;;CAUC;;;;AAYM,MAAM,0DAAY,CAAA,GAAA,sCAAI,EAAE,UAAU,CAAC,CAAC,YAAC,QAAQ,EAAE,GAAG,OAAsB,EAAE;IAC/E,MAAM,CAAA,GAAA,kCAAW,EAAE;IACnB,IAAI,cAAC,UAAU,EAAC,GAAG,CAAA,GAAA,kCAAO,EAAE;QAAC,GAAG,KAAK;aAAE;IAAG;IAC1C,IAAI,kBAAC,cAAc,EAAC,GAAG,CAAA,GAAA,sCAAW,EAAE,OAAO;IAC3C,IAAI,QAAQ,CAAA,GAAA,sCAAI,EAAE,QAAQ,CAAC,IAAI,CAAC;IAEhC,CAAA,GAAA,sBAAQ,EAAE;QACR,IAAI,QAAQ,GAAG,CAAC,QAAQ,KAAK,cAC3B;QAGF,IAAI,KAAK,IAAI,OAAO;QACpB,IAAI,CAAC,MAAM,CAAE,CAAA,cAAc,CAAA,GAAA,oCAAa,EAAE,IAAI,OAAO,AAAD,GAAI;YACtD,QAAQ,KAAK,CAAC;YACd;QACF;QAEA,IAAI,CAAC,MAAM,UAAU,IAAI,CAAC,CAAA,GAAA,iCAAU,EAAE,KAAK;YACzC,QAAQ,IAAI,CAAC;YACb;QACF;QAEA,IACE,GAAG,SAAS,KAAK,YACjB,GAAG,SAAS,KAAK,WACjB,GAAG,SAAS,KAAK,YACjB,GAAG,SAAS,KAAK,cACjB,GAAG,SAAS,KAAK,OACjB,GAAG,SAAS,KAAK,UACjB,GAAG,SAAS,KAAK,WACjB;YACA,IAAI,OAAO,GAAG,YAAY,CAAC;YAC3B,IAAI,CAAC,MACH,QAAQ,IAAI,CAAC;iBACR,IACL,2CAA2C;YAC3C,SAAS,iBACT,SAAS,YACT,SAAS,cACT,SAAS,cACT,SAAS,cACT,SAAS,UACT,SAAS,cACT,SAAS,sBACT,SAAS,mBACT,SAAS,YACT,SAAS,WACT,SAAS,eACT,SAAS,eACT,SAAS,YACT,SAAS,gBACT,SAAS,YACT,SAAS,SACT,SAAS,aACT,SAAS,YAET,QAAQ,IAAI,CAAC,CAAC,2DAA2D,EAAE,KAAK,EAAE,CAAC;QAEvF;IACF,GAAG;QAAC;QAAK,MAAM,UAAU;KAAC;IAE1B,aAAa;IACb,IAAI,WAAW,SAAS,CAAA,GAAA,sCAAI,EAAE,OAAO,EAAE,MAAM,KAAK,MAAM,GAAG,GAAG,MAAM,KAAK,CAAC,GAAG;IAE7E,qBAAO,CAAA,GAAA,sCAAI,EAAE,YAAY,CACvB,OACA;QACE,GAAG,CAAA,GAAA,gCAAS,EAAE,YAAY,gBAAgB,MAAM,KAAK,CAAC;QACtD,aAAa;QACb,KAAK,CAAA,GAAA,+BAAQ,EAAE,UAAU;IAC3B;AAEJ", "sources": ["packages/@react-aria/interactions/src/Pressable.tsx"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {DOMAttributes, FocusableElement} from '@react-types/shared';\nimport {getOwnerWindow, isFocusable, mergeProps, mergeRefs, useObjectRef} from '@react-aria/utils';\nimport {PressProps, usePress} from './usePress';\nimport React, {ForwardedRef, ReactElement, useEffect} from 'react';\nimport {useFocusable} from './useFocusable';\n\ninterface PressableProps extends PressProps {\n  children: ReactElement<DOMAttributes, string>\n}\n\nexport const Pressable = React.forwardRef(({children, ...props}: PressableProps, ref: ForwardedRef<FocusableElement>) => {\n  ref = useObjectRef(ref);\n  let {pressProps} = usePress({...props, ref});\n  let {focusableProps} = useFocusable(props, ref);\n  let child = React.Children.only(children);\n\n  useEffect(() => {\n    if (process.env.NODE_ENV === 'production') {\n      return;\n    }\n\n    let el = ref.current;\n    if (!el || !(el instanceof getOwnerWindow(el).Element)) {\n      console.error('<Pressable> child must forward its ref to a DOM element.');\n      return;\n    }\n\n    if (!props.isDisabled && !isFocusable(el)) {\n      console.warn('<Pressable> child must be focusable. Please ensure the tabIndex prop is passed through.');\n      return;\n    }\n\n    if (\n      el.localName !== 'button' &&\n      el.localName !== 'input' &&\n      el.localName !== 'select' &&\n      el.localName !== 'textarea' &&\n      el.localName !== 'a' &&\n      el.localName !== 'area' &&\n      el.localName !== 'summary'\n    ) {\n      let role = el.getAttribute('role');\n      if (!role) {\n        console.warn('<Pressable> child must have an interactive ARIA role.');\n      } else if (\n        // https://w3c.github.io/aria/#widget_roles\n        role !== 'application' &&\n        role !== 'button' &&\n        role !== 'checkbox' &&\n        role !== 'combobox' &&\n        role !== 'gridcell' &&\n        role !== 'link' &&\n        role !== 'menuitem' &&\n        role !== 'menuitemcheckbox' &&\n        role !== 'menuitemradio' &&\n        role !== 'option' &&\n        role !== 'radio' &&\n        role !== 'searchbox' &&\n        role !== 'separator' &&\n        role !== 'slider' &&\n        role !== 'spinbutton' &&\n        role !== 'switch' &&\n        role !== 'tab' &&\n        role !== 'textbox' &&\n        role !== 'treeitem'\n      ) {\n        console.warn(`<Pressable> child must have an interactive ARIA role. Got \"${role}\".`);\n      }\n    }\n  }, [ref, props.isDisabled]);\n\n  // @ts-ignore\n  let childRef = parseInt(React.version, 10) < 19 ? child.ref : child.props.ref;\n\n  return React.cloneElement(\n    child,\n    {\n      ...mergeProps(pressProps, focusableProps, child.props),\n      // @ts-ignore\n      ref: mergeRefs(childRef, ref)\n    }\n  );\n});\n"], "names": [], "version": 3, "file": "Pressable.main.js.map"}