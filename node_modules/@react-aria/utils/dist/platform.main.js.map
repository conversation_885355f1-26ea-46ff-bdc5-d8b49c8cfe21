{"mappings": ";;;;;;;;;;;;;;AAAA;;;;;;;;;;CAUC,GAED,SAAS,oCAAc,EAAU;QAK7B;IAJF,IAAI,OAAO,WAAW,eAAe,OAAO,SAAS,IAAI,MACvD,OAAO;IAET,OAAO,EACL,kCAAA,OAAO,SAAS,CAAC,gBAAgB,cAAjC,sDAAA,gCAAmC,MAAM,CAAC,IAAI,CAAC,CAAC,QAA4C,GAAG,IAAI,CAAC,MAAM,KAAK,OAEjH,GAAG,IAAI,CAAC,OAAO,SAAS,CAAC,SAAS;AACpC;AAEA,SAAS,mCAAa,EAAU;QAElB;IADZ,OAAO,OAAO,WAAW,eAAe,OAAO,SAAS,IAAI,OACxD,GAAG,IAAI,CAAC,EAAA,kCAAA,OAAO,SAAS,CAAC,gBAAgB,cAAjC,sDAAA,gCAAmC,QAAQ,KAAI,OAAO,SAAS,CAAC,QAAQ,IAChF;AACN;AAEA,SAAS,6BAAO,EAAiB;IAC/B,IAAI,QAAQ,GAAG,CAAC,QAAQ,KAAK,QAC3B,OAAO;IAGT,IAAI,MAAsB;IAC1B,OAAO;QACL,IAAI,OAAO,MACT,MAAM;QAER,OAAO;IACT;AACF;AAEO,MAAM,4CAAQ,6BAAO;IAC1B,OAAO,mCAAa;AACtB;AAEO,MAAM,2CAAW,6BAAO;IAC7B,OAAO,mCAAa;AACtB;AAEO,MAAM,4CAAS,6BAAO;IAC3B,OAAO,mCAAa,aAClB,yFAAyF;IACxF,+CAAW,UAAU,cAAc,GAAG;AAC3C;AAEO,MAAM,4CAAQ,6BAAO;IAC1B,OAAO,8CAAc;AACvB;AAEO,MAAM,4CAAgB,6BAAO;IAClC,OAAO,+CAAW;AACpB;AAEO,MAAM,4CAAW,6BAAO;IAC7B,OAAO,oCAAc,mBAAmB,CAAC;AAC3C;AAEO,MAAM,4CAAW,6BAAO;IAC7B,OAAO,oCAAc;AACvB;AAEO,MAAM,4CAAY,6BAAO;IAC9B,OAAO,oCAAc;AACvB;AAEO,MAAM,4CAAY,6BAAO;IAC9B,OAAO,oCAAc;AACvB", "sources": ["packages/@react-aria/utils/src/platform.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nfunction testUserAgent(re: RegExp) {\n  if (typeof window === 'undefined' || window.navigator == null) {\n    return false;\n  }\n  return (\n    window.navigator['userAgentData']?.brands.some((brand: {brand: string, version: string}) => re.test(brand.brand))\n  ) ||\n  re.test(window.navigator.userAgent);\n}\n\nfunction testPlatform(re: RegExp) {\n  return typeof window !== 'undefined' && window.navigator != null\n    ? re.test(window.navigator['userAgentData']?.platform || window.navigator.platform)\n    : false;\n}\n\nfunction cached(fn: () => boolean) {\n  if (process.env.NODE_ENV === 'test') {\n    return fn;\n  }\n  \n  let res: boolean | null = null;\n  return () => {\n    if (res == null) {\n      res = fn();\n    }\n    return res;\n  };\n}\n\nexport const isMac = cached(function () {\n  return testPlatform(/^Mac/i);\n});\n\nexport const isIPhone = cached(function () {\n  return testPlatform(/^iPhone/i);\n});\n\nexport const isIPad = cached(function () {\n  return testPlatform(/^iPad/i) ||\n    // iPadOS 13 lies and says it's a Mac, but we can distinguish by detecting touch support.\n    (isMac() && navigator.maxTouchPoints > 1);\n});\n\nexport const isIOS = cached(function () {\n  return isIPhone() || isIPad();\n});\n\nexport const isAppleDevice = cached(function () {\n  return isMac() || isIOS();\n});\n\nexport const isWebKit = cached(function () {\n  return testUserAgent(/AppleWebKit/i) && !isChrome();\n});\n\nexport const isChrome = cached(function () {\n  return testUserAgent(/Chrome/i);\n});\n\nexport const isAndroid = cached(function () {\n  return testUserAgent(/Android/i);\n});\n\nexport const isFirefox = cached(function () {\n  return testUserAgent(/Firefox/i);\n});\n"], "names": [], "version": 3, "file": "platform.main.js.map"}