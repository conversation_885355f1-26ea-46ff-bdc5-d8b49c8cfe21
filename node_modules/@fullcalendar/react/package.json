{"name": "@fullcalendar/react", "version": "6.1.17", "title": "FullCalendar React Component", "description": "The official React Component for FullCalendar", "keywords": ["calendar", "event", "full-sized", "fullcalendar", "react"], "homepage": "https://fullcalendar.io/docs/react", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/fullcalendar/fullcalendar-react.git"}, "peerDependencies": {"@fullcalendar/core": "~6.1.17", "react": "^16.7.0 || ^17 || ^18 || ^19", "react-dom": "^16.7.0 || ^17 || ^18 || ^19"}, "devDependencies": {"@babel/core": "^7.0.0", "@babel/preset-env": "^7.9.0", "@babel/preset-react": "^7.9.4", "@fullcalendar/adaptive": "~6.1.17", "@fullcalendar/core": "~6.1.17", "@fullcalendar/daygrid": "~6.1.17", "@fullcalendar/list": "~6.1.17", "@fullcalendar/resource": "~6.1.17", "@fullcalendar/resource-timeline": "~6.1.17", "@fullcalendar/timegrid": "~6.1.17", "@rollup/plugin-babel": "^5.0.0", "@rollup/plugin-commonjs": "^12.0.0", "@rollup/plugin-json": "^4.0.3", "@rollup/plugin-node-resolve": "^14.0.1", "@rollup/plugin-replace": "^5.0.1", "@testing-library/react": "^13.4.0", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "concurrently": "^5.3.0", "karma": "^6.3.2", "karma-chrome-launcher": "^3.1.0", "karma-jasmine": "^4.0.1", "karma-sourcemap-loader": "^0.3.8", "karma-spec-reporter": "^0.0.32", "react": "^18.3.1", "react-dom": "^18.3.1", "rollup": "^2.79.0", "rollup-plugin-postcss": "^2.0.3", "rollup-plugin-sourcemaps": "^0.6.3", "typescript": "^4.0.5"}, "type": "module", "main": "dist/index.cjs", "module": "dist/index.js", "types": "dist/index.d.ts", "exports": {"./package.json": "./package.json", ".": {"types": "./dist/index.d.ts", "require": "./dist/index.cjs", "import": "./dist/index.js"}}, "files": ["dist", "src"], "sideEffects": false, "scripts": {"build": "pnpm run tsc && pnpm run rollup", "dev": "pnpm run tsc && concurrently 'npm:tsc:dev' 'npm:rollup:dev'", "test": "karma start karma.config.cjs --browsers ChromeHeadless --single-run --no-auto-watch", "test:dev": "karma start karma.config.cjs", "clean": "rm -rf dist tests/dist", "ci": "pnpm run clean && pnpm run build && pnpm run test", "tsc": "tsc -p .", "tsc:dev": "tsc -p . --watch --preserveWatchOutput --pretty", "rollup": "rollup -c", "rollup:dev": "rollup -c --watch"}}