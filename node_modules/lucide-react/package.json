{"name": "lucide-react", "description": "A Lucide icon library package for React applications", "version": "0.303.0", "license": "ISC", "homepage": "https://lucide.dev", "bugs": "https://github.com/lucide-icons/lucide/issues", "repository": {"type": "git", "url": "https://github.com/lucide-icons/lucide.git", "directory": "packages/lucide-react"}, "keywords": ["Lucide", "Angular", "<PERSON><PERSON>", "Icons", "Icon", "SVG", "Feather Icons", "Fontawesome", "Font Awesome"], "author": "<PERSON>", "amdName": "lucide-react", "main": "dist/cjs/lucide-react.js", "main:umd": "dist/umd/lucide-react.js", "module": "dist/esm/lucide-react.js", "unpkg": "dist/umd/lucide-react.min.js", "typings": "dist/lucide-react.d.ts", "sideEffects": false, "files": ["dist", "dynamicIconImports.js", "dynamicIconImports.js.map", "dynamicIconImports.d.ts"], "devDependencies": {"@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^11.2.7", "@types/prop-types": "^15.7.10", "@types/react": "^18.2.37", "@vitejs/plugin-react": "^2.2.0", "react": "17.0.2", "react-dom": "17.0.2", "rollup": "^3.29.4", "rollup-plugin-dts": "^5.3.1", "typescript": "^4.9.5", "vite": "^4.4.12", "vitest": "^0.32.4", "@lucide/build-icons": "1.0.0", "@lucide/rollup-plugins": "1.0.0"}, "peerDependencies": {"react": "^16.5.1 || ^17.0.0 || ^18.0.0"}, "scripts": {"build": "pnpm clean && pnpm copy:license && pnpm build:icons && pnpm typecheck && pnpm build:bundles", "copy:license": "cp ../../LICENSE ./LICENSE", "clean": "rm -rf dist && rm -rf stats && rm -rf ./src/icons/*.ts && rm -f dynamicIconImports.*", "build:icons": "build-icons --output=./src --templateSrc=./scripts/exportTemplate.mjs --renderUniqueKey --withAliases --withDynamicImports --separateAliasesFile --aliasesFileExtension=.ts --iconFileExtension=.ts --exportFileName=index.ts", "build:types": "node ./scripts/buildTypes.mjs", "build:bundles": "rollup -c ./rollup.config.mjs", "typecheck": "tsc", "typecheck:watch": "tsc -w", "test": "vitest run", "test:watch": "vitest watch", "version": "pnpm version --git-tag-version=false"}}