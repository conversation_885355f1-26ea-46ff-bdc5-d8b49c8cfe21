import { logger } from '../utils/logger';

// Temporary mock for development - replace with actual Prisma client after database setup
export const prisma = {
  user: {
    findUnique: async () => null,
    findMany: async () => [],
    create: async () => ({}),
    update: async () => ({}),
    delete: async () => ({}),
    count: async () => 0,
    upsert: async () => ({}),
    groupBy: async () => [],
  },
  contract: {
    findUnique: async () => null,
    findMany: async () => [],
    create: async () => ({}),
    update: async () => ({}),
    delete: async () => ({}),
    count: async () => 0,
  },
  project: {
    findUnique: async () => null,
    findMany: async () => [],
    create: async () => ({}),
    update: async () => ({}),
    delete: async () => ({}),
    count: async () => 0,
    groupBy: async () => [],
  },
  resource: {
    findUnique: async () => null,
    findMany: async () => [],
    create: async () => ({}),
    update: async () => ({}),
    delete: async () => ({}),
    count: async () => 0,
    groupBy: async () => [],
  },
  task: {
    findUnique: async () => null,
    findMany: async () => [],
    create: async () => ({}),
    update: async () => ({}),
    delete: async () => ({}),
    count: async () => 0,
  },
  timesheet: {
    findUnique: async () => null,
    findMany: async () => [],
    create: async () => ({}),
    update: async () => ({}),
    delete: async () => ({}),
    count: async () => 0,
    findFirst: async () => null,
  },
  invoice: {
    findUnique: async () => null,
    findMany: async () => [],
    create: async () => ({}),
    update: async () => ({}),
    delete: async () => ({}),
    count: async () => 0,
    findFirst: async () => null,
  },
  vendor: {
    findUnique: async () => null,
    findMany: async () => [],
    create: async () => ({}),
    update: async () => ({}),
    delete: async () => ({}),
    count: async () => 0,
  },
  skill: {
    findUnique: async () => null,
    findMany: async () => [],
    create: async () => ({}),
    update: async () => ({}),
    delete: async () => ({}),
    upsert: async () => ({}),
  },
  resourceSkill: {
    findUnique: async () => null,
    findMany: async () => [],
    create: async () => ({}),
    update: async () => ({}),
    delete: async () => ({}),
    upsert: async () => ({}),
  },
  projectResource: {
    findUnique: async () => null,
    findMany: async () => [],
    create: async () => ({}),
    update: async () => ({}),
    delete: async () => ({}),
  },
  timesheetEntry: {
    findMany: async () => [],
    create: async () => ({}),
  },
  resourcePlan: {
    findMany: async () => [],
    create: async () => ({}),
  },
  $connect: async () => {},
  $disconnect: async () => {},
} as any;

// Mock database logging
logger.info('Using mock database - please set up PostgreSQL and run database migrations');

// Test database connection
export const connectDatabase = async () => {
  try {
    await prisma.$connect();
    logger.info('Mock database connected successfully');
  } catch (error) {
    logger.error('Failed to connect to database:', error);
  }
};

// Disconnect from database
export const disconnectDatabase = async () => {
  try {
    await prisma.$disconnect();
    logger.info('Mock database disconnected successfully');
  } catch (error) {
    logger.error('Failed to disconnect from database:', error);
  }
};
