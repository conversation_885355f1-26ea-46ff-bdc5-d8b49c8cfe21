import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { Pool } from 'pg';
import { body, validationResult, query } from 'express-validator';

const app = express();
const PORT = process.env.PORT || 3003;

// Database connection
const pool = new Pool({
  connectionString: process.env.DATABASE_URL || 'postgresql://postgres:postgres@localhost:5433/agentic_talent_pro',
});

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again later.',
});

// Middleware
app.use(helmet());
app.use(compression());
app.use(limiter);
app.use(cors({
  origin: [
    'http://localhost:3002',
    'http://localhost:3000',
    process.env.CORS_ORIGIN || 'http://localhost:3002'
  ],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'Accept'],
  preflightContinue: false,
  optionsSuccessStatus: 204
}));
app.use(morgan('combined'));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Authentication middleware
const authenticate = async (req: any, res: any, next: any) => {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ success: false, error: 'Access token required' });
    }

    const token = authHeader.substring(7);
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key') as any;

    const userResult = await pool.query('SELECT * FROM users WHERE id = $1', [decoded.id]);
    const user = userResult.rows[0];

    if (!user || user.status !== 'ACTIVE') {
      return res.status(401).json({ success: false, error: 'Invalid token' });
    }

    req.user = { id: user.id, email: user.email, role: user.role };
    next();
  } catch (error) {
    res.status(401).json({ success: false, error: 'Invalid token' });
  }
};

// Health check
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV || 'development',
  });
});

// Auth endpoints
app.post('/api/auth/login', [
  body('email').isEmail().normalizeEmail(),
  body('password').isLength({ min: 1 }),
], async (req: any, res: any) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ success: false, error: 'Validation failed', details: errors.array() });
    }

    const { email, password } = req.body;

    const userResult = await pool.query('SELECT * FROM users WHERE email = $1', [email]);
    const user = userResult.rows[0];

    if (!user) {
      return res.status(401).json({ success: false, error: 'Invalid credentials' });
    }

    const isValidPassword = await bcrypt.compare(password, user.password);
    if (!isValidPassword) {
      return res.status(401).json({ success: false, error: 'Invalid credentials' });
    }

    if (user.status !== 'ACTIVE') {
      return res.status(401).json({ success: false, error: 'Account is not active' });
    }

    const tokenPayload = { id: user.id, email: user.email, role: user.role };
    const token = jwt.sign(tokenPayload, process.env.JWT_SECRET || 'your-secret-key', { expiresIn: '7d' });
    const refreshToken = jwt.sign(tokenPayload, process.env.JWT_REFRESH_SECRET || 'your-refresh-secret', { expiresIn: '30d' });

    const { password: _, ...userWithoutPassword } = user;

    res.json({
      success: true,
      data: { user: userWithoutPassword, token, refreshToken },
    });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

app.get('/api/auth/profile', authenticate, async (req: any, res: any) => {
  try {
    const userResult = await pool.query(`
      SELECT u.*, r.id as resource_id, r."employeeId", r.designation, r.department 
      FROM users u 
      LEFT JOIN resources r ON u.id = r."userId" 
      WHERE u.id = $1
    `, [req.user.id]);
    
    const user = userResult.rows[0];
    if (!user) {
      return res.status(404).json({ success: false, error: 'User not found' });
    }

    const { password: _, ...userWithoutPassword } = user;
    res.json({ success: true, data: userWithoutPassword });
  } catch (error) {
    console.error('Profile error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

app.post('/api/auth/logout', authenticate, (req: any, res: any) => {
  res.json({ success: true, message: 'Logout successful' });
});

// Dashboard endpoints
app.get('/api/dashboard/stats', authenticate, async (req: any, res: any) => {
  try {
    const [contractsResult, projectsResult, resourcesResult, timesheetsResult, invoicesResult] = await Promise.all([
      pool.query('SELECT COUNT(*) FROM contracts'),
      pool.query('SELECT COUNT(*) FROM projects WHERE status = $1', ['ACTIVE']),
      pool.query('SELECT COUNT(*) FROM resources WHERE status = $1', ['AVAILABLE']),
      pool.query('SELECT COUNT(*) FROM timesheets WHERE status = $1', ['SUBMITTED']),
      pool.query('SELECT COUNT(*) FROM invoices WHERE status IN ($1, $2)', ['SENT', 'OVERDUE']),
    ]);

    const stats: any = {
      overview: {
        totalContracts: parseInt(contractsResult.rows[0].count),
        activeProjects: parseInt(projectsResult.rows[0].count),
        totalResources: parseInt(resourcesResult.rows[0].count),
        pendingTimesheets: parseInt(timesheetsResult.rows[0].count),
        unpaidInvoices: parseInt(invoicesResult.rows[0].count),
      },
    };

    // Role-specific stats
    if (req.user.role === 'PROJECT_MANAGER') {
      const myProjectsResult = await pool.query('SELECT COUNT(*) FROM projects WHERE "managerId" = $1', [req.user.id]);
      stats.myProjects = parseInt(myProjectsResult.rows[0].count);
    }

    if (req.user.role === 'RESOURCE') {
      const resourceResult = await pool.query('SELECT id FROM resources WHERE "userId" = $1', [req.user.id]);
      if (resourceResult.rows[0]) {
        const resourceId = resourceResult.rows[0].id;
        const [tasksResult, timesheetsResult] = await Promise.all([
          pool.query('SELECT COUNT(*) FROM tasks WHERE "assignedToId" = $1', [req.user.id]),
          pool.query('SELECT COUNT(*) FROM timesheets WHERE "resourceId" = $1', [resourceId]),
        ]);
        stats.myTasks = parseInt(tasksResult.rows[0].count);
        stats.myTimesheets = parseInt(timesheetsResult.rows[0].count);
      }
    }

    res.json({ success: true, data: stats });
  } catch (error) {
    console.error('Dashboard stats error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

app.get('/api/dashboard/charts', authenticate, async (req: any, res: any) => {
  try {
    const [projectStatusResult, resourceUtilizationResult, monthlyInvoicesResult] = await Promise.all([
      pool.query('SELECT status, COUNT(*) as count FROM projects GROUP BY status'),
      pool.query('SELECT status, COUNT(*) as count FROM resources GROUP BY status'),
      pool.query(`
        SELECT 
          TO_CHAR("createdAt", 'YYYY-MM') as month,
          SUM(total) as total,
          COUNT(*) as count
        FROM invoices 
        WHERE "createdAt" >= NOW() - INTERVAL '6 months'
        GROUP BY TO_CHAR("createdAt", 'YYYY-MM')
        ORDER BY month
      `),
    ]);

    const charts = {
      projectStatus: projectStatusResult.rows,
      resourceUtilization: resourceUtilizationResult.rows,
      monthlyInvoices: monthlyInvoicesResult.rows,
    };

    res.json({ success: true, data: charts });
  } catch (error) {
    console.error('Dashboard charts error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Contracts endpoints
app.get('/api/contracts', authenticate, [
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 100 }),
  query('status').optional().isIn(['DRAFT', 'ACTIVE', 'COMPLETED', 'TERMINATED']),
], async (req: any, res: any) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ success: false, error: 'Validation failed', details: errors.array() });
    }

    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const skip = (page - 1) * limit;
    const status = req.query.status;

    let whereClause = '';
    const params: any[] = [];
    
    if (status) {
      whereClause = 'WHERE c.status = $1';
      params.push(status);
    }

    // If user is a client, only show their contracts
    if (req.user.role === 'CLIENT') {
      whereClause = whereClause ? `${whereClause} AND c."clientId" = $${params.length + 1}` : `WHERE c."clientId" = $1`;
      params.push(req.user.id);
    }

    const query = `
      SELECT c.*, u."firstName", u."lastName", u.email,
             COUNT(p.id) as project_count
      FROM contracts c 
      JOIN users u ON c."clientId" = u.id 
      LEFT JOIN projects p ON c.id = p."contractId"
      ${whereClause}
      GROUP BY c.id, u."firstName", u."lastName", u.email
      ORDER BY c."createdAt" DESC 
      LIMIT $${params.length + 1} OFFSET $${params.length + 2}
    `;

    const countQuery = `
      SELECT COUNT(*) FROM contracts c ${whereClause}
    `;

    params.push(limit, skip);
    const [result, countResult] = await Promise.all([
      pool.query(query, params),
      pool.query(countQuery, params.slice(0, -2)),
    ]);

    res.json({
      success: true,
      data: result.rows,
      pagination: {
        page,
        limit,
        total: parseInt(countResult.rows[0].count),
        totalPages: Math.ceil(parseInt(countResult.rows[0].count) / limit),
      },
    });
  } catch (error) {
    console.error('Contracts error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Production server running on port ${PORT}`);
  console.log(`📊 Dashboard: http://localhost:3002`);
  console.log(`🔗 API: http://localhost:${PORT}`);
  console.log(`📚 Features: Authentication, RBAC, Dashboard, Contracts, Projects, Resources, Tasks, Timesheets, Invoices`);
});

export default app;
