import express from 'express';
import { body, query, validationResult } from 'express-validator';
import { prisma } from '../config/database';
import { createError } from '../middleware/errorHandler';
import { authenticate, authorize, AuthRequest } from '../middleware/auth';
import { UserRole, TimesheetStatus } from '@agentic-talent-pro/shared';

const router = express.Router();

// Apply authentication to all routes
router.use(authenticate);

/**
 * @swagger
 * /api/timesheets:
 *   get:
 *     summary: Get all timesheets
 *     tags: [Timesheets]
 *     security:
 *       - bearerAuth: []
 */
router.get('/', async (req: AuthRequest, res, next) => {
  try {
    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const skip = (page - 1) * limit;
    const resourceId = req.query.resourceId as string;
    const projectId = req.query.projectId as string;
    const status = req.query.status as TimesheetStatus;

    const where: any = {};
    
    if (resourceId) where.resourceId = resourceId;
    if (projectId) where.projectId = projectId;
    if (status) where.status = status;

    // Filter based on user role
    if (req.user!.role === UserRole.RESOURCE) {
      const resource = await prisma.resource.findUnique({
        where: { userId: req.user!.id },
      });
      if (resource) {
        where.resourceId = resource.id;
      }
    }

    const [timesheets, total] = await Promise.all([
      prisma.timesheet.findMany({
        where,
        skip,
        take: limit,
        include: {
          resource: {
            include: {
              user: {
                select: {
                  id: true,
                  firstName: true,
                  lastName: true,
                  email: true,
                },
              },
            },
          },
          project: {
            select: {
              id: true,
              name: true,
            },
          },
          entries: {
            include: {
              task: {
                select: {
                  id: true,
                  title: true,
                },
              },
            },
          },
        },
        orderBy: { weekStarting: 'desc' },
      }),
      prisma.timesheet.count({ where }),
    ]);

    res.json({
      success: true,
      data: timesheets,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    next(error);
  }
});

/**
 * @swagger
 * /api/timesheets/{id}/approve:
 *   patch:
 *     summary: Approve timesheet
 *     tags: [Timesheets]
 *     security:
 *       - bearerAuth: []
 */
router.patch('/:id/approve', authorize(UserRole.ADMIN, UserRole.PROJECT_MANAGER), async (req: AuthRequest, res, next) => {
  try {
    const { id } = req.params;

    const timesheet = await prisma.timesheet.findUnique({
      where: { id },
      include: { project: true },
    });

    if (!timesheet) {
      throw createError('Timesheet not found', 404);
    }

    if (timesheet.status !== TimesheetStatus.SUBMITTED) {
      throw createError('Only submitted timesheets can be approved', 400);
    }

    // Check if user can approve this timesheet
    if (req.user!.role === UserRole.PROJECT_MANAGER && timesheet.project.managerId !== req.user!.id) {
      throw createError('Access denied', 403);
    }

    const updatedTimesheet = await prisma.timesheet.update({
      where: { id },
      data: {
        status: TimesheetStatus.APPROVED,
        approvedAt: new Date(),
        approvedBy: req.user!.id,
      },
      include: {
        resource: {
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                email: true,
              },
            },
          },
        },
        project: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    res.json({
      success: true,
      data: updatedTimesheet,
      message: 'Timesheet approved successfully',
    });
  } catch (error) {
    next(error);
  }
});

export default router;
