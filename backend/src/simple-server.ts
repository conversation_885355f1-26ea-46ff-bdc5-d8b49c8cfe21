import express from 'express';
import cors from 'cors';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { Pool } from 'pg';

const app = express();
const PORT = process.env.PORT || 3001;

// Database connection
const pool = new Pool({
  connectionString: process.env.DATABASE_URL || 'postgresql://postgres:postgres@localhost:5433/agentic_talent_pro',
});

// Middleware
app.use(cors({
  origin: 'http://localhost:3000',
  credentials: true,
}));
app.use(express.json());

// Health check
app.get('/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

// Login endpoint
app.post('/api/auth/login', async (req, res) => {
  try {
    const { email, password } = req.body;

    // Find user
    const userResult = await pool.query('SELECT * FROM users WHERE email = $1', [email]);
    const user = userResult.rows[0];

    if (!user) {
      return res.status(401).json({ success: false, error: 'Invalid credentials' });
    }

    // Check password
    const isValidPassword = await bcrypt.compare(password, user.password);
    if (!isValidPassword) {
      return res.status(401).json({ success: false, error: 'Invalid credentials' });
    }

    // Check if user is active
    if (user.status !== 'ACTIVE') {
      return res.status(401).json({ success: false, error: 'Account is not active' });
    }

    // Generate token
    const token = jwt.sign(
      { id: user.id, email: user.email, role: user.role },
      process.env.JWT_SECRET || 'your-secret-key',
      { expiresIn: '7d' }
    );

    const refreshToken = jwt.sign(
      { id: user.id, email: user.email, role: user.role },
      process.env.JWT_REFRESH_SECRET || 'your-refresh-secret',
      { expiresIn: '30d' }
    );

    // Remove password from response
    const { password: _, ...userWithoutPassword } = user;

    res.json({
      success: true,
      data: {
        user: userWithoutPassword,
        token,
        refreshToken,
      },
    });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Get profile endpoint
app.get('/api/auth/profile', async (req, res) => {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ success: false, error: 'Access token required' });
    }

    const token = authHeader.substring(7);
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key') as any;

    const userResult = await pool.query('SELECT * FROM users WHERE id = $1', [decoded.id]);
    const user = userResult.rows[0];

    if (!user) {
      return res.status(404).json({ success: false, error: 'User not found' });
    }

    const { password: _, ...userWithoutPassword } = user;
    res.json({ success: true, data: userWithoutPassword });
  } catch (error) {
    console.error('Profile error:', error);
    res.status(401).json({ success: false, error: 'Invalid token' });
  }
});

// Dashboard stats endpoint
app.get('/api/dashboard/stats', async (req, res) => {
  try {
    const [contractsResult, projectsResult, resourcesResult, timesheetsResult, invoicesResult] = await Promise.all([
      pool.query('SELECT COUNT(*) FROM contracts'),
      pool.query('SELECT COUNT(*) FROM projects WHERE status = $1', ['ACTIVE']),
      pool.query('SELECT COUNT(*) FROM resources WHERE status = $1', ['AVAILABLE']),
      pool.query('SELECT COUNT(*) FROM timesheets WHERE status = $1', ['SUBMITTED']),
      pool.query('SELECT COUNT(*) FROM invoices WHERE status IN ($1, $2)', ['SENT', 'OVERDUE']),
    ]);

    const stats = {
      overview: {
        totalContracts: parseInt(contractsResult.rows[0].count),
        activeProjects: parseInt(projectsResult.rows[0].count),
        totalResources: parseInt(resourcesResult.rows[0].count),
        pendingTimesheets: parseInt(timesheetsResult.rows[0].count),
        unpaidInvoices: parseInt(invoicesResult.rows[0].count),
      },
    };

    res.json({ success: true, data: stats });
  } catch (error) {
    console.error('Dashboard stats error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Dashboard charts endpoint
app.get('/api/dashboard/charts', async (req, res) => {
  try {
    const [projectStatusResult, resourceUtilizationResult] = await Promise.all([
      pool.query('SELECT status, COUNT(*) as count FROM projects GROUP BY status'),
      pool.query('SELECT status, COUNT(*) as count FROM resources GROUP BY status'),
    ]);

    const charts = {
      projectStatus: projectStatusResult.rows,
      resourceUtilization: resourceUtilizationResult.rows,
      monthlyInvoices: [], // Placeholder for now
    };

    res.json({ success: true, data: charts });
  } catch (error) {
    console.error('Dashboard charts error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Contracts endpoint
app.get('/api/contracts', async (req, res) => {
  try {
    const result = await pool.query(`
      SELECT c.*, u."firstName", u."lastName", u.email 
      FROM contracts c 
      JOIN users u ON c."clientId" = u.id 
      ORDER BY c."createdAt" DESC
    `);

    res.json({
      success: true,
      data: result.rows,
      pagination: {
        page: 1,
        limit: 10,
        total: result.rows.length,
        totalPages: 1,
      },
    });
  } catch (error) {
    console.error('Contracts error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Projects endpoint
app.get('/api/projects', async (req, res) => {
  try {
    const result = await pool.query(`
      SELECT p.*, c.title as contract_title, u."firstName", u."lastName"
      FROM projects p 
      JOIN contracts c ON p."contractId" = c.id
      JOIN users u ON p."managerId" = u.id 
      ORDER BY p."createdAt" DESC
    `);

    res.json({
      success: true,
      data: result.rows,
      pagination: {
        page: 1,
        limit: 10,
        total: result.rows.length,
        totalPages: 1,
      },
    });
  } catch (error) {
    console.error('Projects error:', error);
    res.status(500).json({ success: false, error: 'Internal server error' });
  }
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Simple server running on port ${PORT}`);
  console.log(`📊 Dashboard: http://localhost:3000`);
  console.log(`🔗 API: http://localhost:${PORT}`);
});

export default app;
