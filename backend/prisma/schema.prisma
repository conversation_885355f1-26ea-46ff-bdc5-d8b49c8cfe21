// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum UserRole {
  ADMIN
  PROJECT_MANAGER
  RESOURCE
  CLIENT
  HR_MANAGER
  BILLING_MANAGER
}

enum UserStatus {
  ACTIVE
  INACTIVE
  PENDING
}

model User {
  id        String     @id @default(cuid())
  email     String     @unique
  password  String
  firstName String
  lastName  String
  role      UserRole
  status    UserStatus @default(PENDING)
  phone     String?
  avatar    String?
  createdAt DateTime   @default(now())
  updatedAt DateTime   @updatedAt

  // Relations
  resource         Resource?
  managedProjects  Project[]
  assignedTasks    Task[]
  approvedTimesheets Timesheet[] @relation("ApprovedBy")
  createdContracts Contract[]

  @@map("users")
}

enum ContractStatus {
  DRAFT
  ACTIVE
  COMPLETED
  TERMINATED
}

enum ContractType {
  FIXED_PRICE
  TIME_AND_MATERIAL
  RETAINER
}

model Contract {
  id          String         @id @default(cuid())
  title       String
  description String
  clientId    String
  type        ContractType
  status      ContractStatus @default(DRAFT)
  startDate   DateTime
  endDate     DateTime
  value       Float
  currency    String         @default("USD")
  terms       String
  createdAt   DateTime       @default(now())
  updatedAt   DateTime       @updatedAt

  // Relations
  client   User      @relation(fields: [clientId], references: [id])
  projects Project[]

  @@map("contracts")
}

enum ProjectStatus {
  PLANNING
  ACTIVE
  ON_HOLD
  COMPLETED
  CANCELLED
}

model Project {
  id          String        @id @default(cuid())
  name        String
  description String
  contractId  String
  managerId   String
  status      ProjectStatus @default(PLANNING)
  startDate   DateTime
  endDate     DateTime
  budget      Float
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt

  // Relations
  contract         Contract           @relation(fields: [contractId], references: [id])
  manager          User               @relation(fields: [managerId], references: [id])
  tasks            Task[]
  timesheets       Timesheet[]
  invoices         Invoice[]
  resourcePlans    ResourcePlan[]
  projectResources ProjectResource[]

  @@map("projects")
}

enum EmploymentType {
  FULL_TIME
  CONTRACTOR
  VENDOR
}

enum ResourceStatus {
  AVAILABLE
  ALLOCATED
  ON_LEAVE
  TERMINATED
}

model Resource {
  id                String           @id @default(cuid())
  userId            String           @unique
  employeeId        String           @unique
  employmentType    EmploymentType
  status            ResourceStatus   @default(AVAILABLE)
  designation       String
  department        String
  location          String
  hourlyRate        Float
  joiningDate       DateTime
  panNumber         String?
  bankDetails       String?
  backgroundCheck   Boolean          @default(false)
  securityClearance String?
  createdAt         DateTime         @default(now())
  updatedAt         DateTime         @updatedAt

  // Relations
  user             User               @relation(fields: [userId], references: [id])
  skills           ResourceSkill[]
  timesheets       Timesheet[]
  invoices         Invoice[]
  projectResources ProjectResource[]
  vendorId         String?
  vendor           Vendor?            @relation(fields: [vendorId], references: [id])

  @@map("resources")
}

model Skill {
  id          String @id @default(cuid())
  name        String @unique
  category    String
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  resourceSkills ResourceSkill[]
  resourcePlans  ResourcePlan[]

  @@map("skills")
}

model ResourceSkill {
  resourceId        String
  skillId           String
  proficiencyLevel  Int     @default(1) // 1-5 scale
  yearsOfExperience Int     @default(0)
  certified         Boolean @default(false)
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  // Relations
  resource Resource @relation(fields: [resourceId], references: [id], onDelete: Cascade)
  skill    Skill    @relation(fields: [skillId], references: [id], onDelete: Cascade)

  @@id([resourceId, skillId])
  @@map("resource_skills")
}

model ResourcePlan {
  id               String @id @default(cuid())
  projectId        String
  skillId          String
  role             String
  allocationPercent Int    @default(100)
  requiredCount    Int    @default(1)
  description      String?
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt

  // Relations
  project Project @relation(fields: [projectId], references: [id], onDelete: Cascade)
  skill   Skill   @relation(fields: [skillId], references: [id])

  @@map("resource_plans")
}

model ProjectResource {
  id               String   @id @default(cuid())
  projectId        String
  resourceId       String
  allocationPercent Int     @default(100)
  startDate        DateTime
  endDate          DateTime?
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt

  // Relations
  project  Project  @relation(fields: [projectId], references: [id], onDelete: Cascade)
  resource Resource @relation(fields: [resourceId], references: [id], onDelete: Cascade)

  @@unique([projectId, resourceId])
  @@map("project_resources")
}

enum TaskStatus {
  TODO
  IN_PROGRESS
  REVIEW
  COMPLETED
  BLOCKED
}

enum TaskPriority {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

model Task {
  id             String       @id @default(cuid())
  title          String
  description    String
  projectId      String
  assignedToId   String
  status         TaskStatus   @default(TODO)
  priority       TaskPriority @default(MEDIUM)
  estimatedHours Float
  actualHours    Float        @default(0)
  startDate      DateTime
  dueDate        DateTime
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt

  // Relations
  project      Project           @relation(fields: [projectId], references: [id], onDelete: Cascade)
  assignedTo   User              @relation(fields: [assignedToId], references: [id])
  timesheetEntries TimesheetEntry[]

  @@map("tasks")
}

enum TimesheetStatus {
  DRAFT
  SUBMITTED
  APPROVED
  REJECTED
}

model Timesheet {
  id           String          @id @default(cuid())
  resourceId   String
  projectId    String
  weekStarting DateTime
  weekEnding   DateTime
  status       TimesheetStatus @default(DRAFT)
  totalHours   Float           @default(0)
  submittedAt  DateTime?
  approvedAt   DateTime?
  approvedBy   String?
  rejectionReason String?
  createdAt    DateTime        @default(now())
  updatedAt    DateTime        @updatedAt

  // Relations
  resource  Resource         @relation(fields: [resourceId], references: [id])
  project   Project          @relation(fields: [projectId], references: [id])
  approver  User?            @relation("ApprovedBy", fields: [approvedBy], references: [id])
  entries   TimesheetEntry[]
  invoices  Invoice[]

  @@unique([resourceId, projectId, weekStarting])
  @@map("timesheets")
}

model TimesheetEntry {
  id          String   @id @default(cuid())
  timesheetId String
  taskId      String
  date        DateTime
  hours       Float
  description String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  timesheet Timesheet @relation(fields: [timesheetId], references: [id], onDelete: Cascade)
  task      Task      @relation(fields: [taskId], references: [id])

  @@map("timesheet_entries")
}

enum InvoiceStatus {
  DRAFT
  SENT
  PAID
  OVERDUE
  CANCELLED
}

model Invoice {
  id            String        @id @default(cuid())
  invoiceNumber String        @unique
  projectId     String
  resourceId    String
  timesheetId   String
  status        InvoiceStatus @default(DRAFT)
  issueDate     DateTime
  dueDate       DateTime
  subtotal      Float
  tax           Float
  penalties     Float         @default(0)
  total         Float
  paidAt        DateTime?
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt

  // Relations
  project   Project   @relation(fields: [projectId], references: [id])
  resource  Resource  @relation(fields: [resourceId], references: [id])
  timesheet Timesheet @relation(fields: [timesheetId], references: [id])

  @@map("invoices")
}

enum VendorStatus {
  ACTIVE
  INACTIVE
  BLACKLISTED
}

model Vendor {
  id            String       @id @default(cuid())
  name          String
  contactPerson String
  email         String       @unique
  phone         String
  address       String
  panNumber     String
  gstNumber     String?
  bankDetails   String
  status        VendorStatus @default(ACTIVE)
  createdAt     DateTime     @default(now())
  updatedAt     DateTime     @updatedAt

  // Relations
  resources Resource[]

  @@map("vendors")
}
