import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seed...');

  // Create admin user
  const adminPassword = await bcrypt.hash('admin123', 12);
  const admin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: adminPassword,
      firstName: 'System',
      lastName: 'Administrator',
      role: 'ADMIN',
      status: 'ACTIVE',
      phone: '+1234567890',
    },
  });

  // Create project manager
  const pmPassword = await bcrypt.hash('pm123', 12);
  const projectManager = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: pmPassword,
      firstName: '<PERSON>',
      lastName: 'Manager',
      role: 'PROJECT_MANAGER',
      status: 'ACTIVE',
      phone: '+1234567891',
    },
  });

  // Create HR manager
  const hrPassword = await bcrypt.hash('hr123', 12);
  const hrManager = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: hrPassword,
      firstName: 'Sarah',
      lastName: 'HR',
      role: 'HR_MANAGER',
      status: 'ACTIVE',
      phone: '+1234567892',
    },
  });

  // Create billing manager
  const billingPassword = await bcrypt.hash('billing123', 12);
  const billingManager = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: billingPassword,
      firstName: 'Mike',
      lastName: 'Finance',
      role: 'BILLING_MANAGER',
      status: 'ACTIVE',
      phone: '+1234567893',
    },
  });

  // Create client
  const clientPassword = await bcrypt.hash('client123', 12);
  const client = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: clientPassword,
      firstName: 'Jane',
      lastName: 'Client',
      role: 'CLIENT',
      status: 'ACTIVE',
      phone: '+1234567894',
    },
  });

  // Create resource users
  const resource1Password = await bcrypt.hash('resource123', 12);
  const resource1User = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: resource1Password,
      firstName: 'Alice',
      lastName: 'Developer',
      role: 'RESOURCE',
      status: 'ACTIVE',
      phone: '+1234567895',
    },
  });

  const resource2User = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      password: resource1Password,
      firstName: 'Bob',
      lastName: 'Designer',
      role: 'RESOURCE',
      status: 'ACTIVE',
      phone: '+1234567896',
    },
  });

  // Create skills
  const skills = [
    { name: 'JavaScript', category: 'Programming', description: 'JavaScript programming language' },
    { name: 'TypeScript', category: 'Programming', description: 'TypeScript programming language' },
    { name: 'React', category: 'Frontend', description: 'React.js framework' },
    { name: 'Node.js', category: 'Backend', description: 'Node.js runtime' },
    { name: 'PostgreSQL', category: 'Database', description: 'PostgreSQL database' },
    { name: 'UI/UX Design', category: 'Design', description: 'User interface and experience design' },
    { name: 'Project Management', category: 'Management', description: 'Project management skills' },
  ];

  for (const skill of skills) {
    await prisma.skill.upsert({
      where: { name: skill.name },
      update: {},
      create: skill,
    });
  }

  // Get created skills
  const jsSkill = await prisma.skill.findUnique({ where: { name: 'JavaScript' } });
  const reactSkill = await prisma.skill.findUnique({ where: { name: 'React' } });
  const nodeSkill = await prisma.skill.findUnique({ where: { name: 'Node.js' } });
  const designSkill = await prisma.skill.findUnique({ where: { name: 'UI/UX Design' } });

  // Create resources
  const resource1 = await prisma.resource.upsert({
    where: { userId: resource1User.id },
    update: {},
    create: {
      userId: resource1User.id,
      employeeId: 'EMP001',
      employmentType: 'FULL_TIME',
      status: 'AVAILABLE',
      designation: 'Senior Developer',
      department: 'Engineering',
      location: 'New York',
      hourlyRate: 75.0,
      joiningDate: new Date('2023-01-15'),
      panNumber: '**********',
      backgroundCheck: true,
    },
  });

  const resource2 = await prisma.resource.upsert({
    where: { userId: resource2User.id },
    update: {},
    create: {
      userId: resource2User.id,
      employeeId: 'EMP002',
      employmentType: 'CONTRACTOR',
      status: 'AVAILABLE',
      designation: 'UI/UX Designer',
      department: 'Design',
      location: 'San Francisco',
      hourlyRate: 65.0,
      joiningDate: new Date('2023-02-01'),
      panNumber: '**********',
      backgroundCheck: true,
    },
  });

  // Add skills to resources
  if (jsSkill && reactSkill && nodeSkill) {
    await prisma.resourceSkill.upsert({
      where: {
        resourceId_skillId: {
          resourceId: resource1.id,
          skillId: jsSkill.id,
        },
      },
      update: {},
      create: {
        resourceId: resource1.id,
        skillId: jsSkill.id,
        proficiencyLevel: 5,
        yearsOfExperience: 5,
        certified: true,
      },
    });

    await prisma.resourceSkill.upsert({
      where: {
        resourceId_skillId: {
          resourceId: resource1.id,
          skillId: reactSkill.id,
        },
      },
      update: {},
      create: {
        resourceId: resource1.id,
        skillId: reactSkill.id,
        proficiencyLevel: 4,
        yearsOfExperience: 3,
        certified: false,
      },
    });

    await prisma.resourceSkill.upsert({
      where: {
        resourceId_skillId: {
          resourceId: resource1.id,
          skillId: nodeSkill.id,
        },
      },
      update: {},
      create: {
        resourceId: resource1.id,
        skillId: nodeSkill.id,
        proficiencyLevel: 4,
        yearsOfExperience: 4,
        certified: false,
      },
    });
  }

  if (designSkill) {
    await prisma.resourceSkill.upsert({
      where: {
        resourceId_skillId: {
          resourceId: resource2.id,
          skillId: designSkill.id,
        },
      },
      update: {},
      create: {
        resourceId: resource2.id,
        skillId: designSkill.id,
        proficiencyLevel: 5,
        yearsOfExperience: 6,
        certified: true,
      },
    });
  }

  // Create a sample contract
  const contract = await prisma.contract.upsert({
    where: { id: 'sample-contract-1' },
    update: {},
    create: {
      id: 'sample-contract-1',
      title: 'Web Application Development',
      description: 'Development of a modern web application with React and Node.js',
      clientId: client.id,
      type: 'TIME_AND_MATERIAL',
      status: 'ACTIVE',
      startDate: new Date('2024-01-01'),
      endDate: new Date('2024-12-31'),
      value: 500000,
      currency: 'USD',
      terms: 'Standard terms and conditions apply. Payment due within 30 days.',
    },
  });

  // Create a sample project
  const project = await prisma.project.upsert({
    where: { id: 'sample-project-1' },
    update: {},
    create: {
      id: 'sample-project-1',
      name: 'E-commerce Platform',
      description: 'Building a scalable e-commerce platform',
      contractId: contract.id,
      managerId: projectManager.id,
      status: 'ACTIVE',
      startDate: new Date('2024-01-15'),
      endDate: new Date('2024-06-15'),
      budget: 250000,
    },
  });

  console.log('✅ Database seeded successfully!');
  console.log('📧 Login credentials:');
  console.log('Admin: <EMAIL> / admin123');
  console.log('Project Manager: <EMAIL> / pm123');
  console.log('HR Manager: <EMAIL> / hr123');
  console.log('Billing Manager: <EMAIL> / billing123');
  console.log('Client: <EMAIL> / client123');
  console.log('Resource 1: <EMAIL> / resource123');
  console.log('Resource 2: <EMAIL> / resource123');
}

main()
  .catch((e) => {
    console.error('❌ Error seeding database:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
