{"name": "@agentic-talent-pro/backend", "version": "1.0.0", "description": "Backend API for Agentic Talent Pro", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "db:generate": "prisma generate", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "ts-node prisma/seed.ts", "db:reset": "prisma migrate reset"}, "dependencies": {"@agentic-talent-pro/shared": "file:../shared", "@prisma/client": "^5.7.1", "express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.7", "winston": "^3.11.0", "compression": "^1.7.4", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/morgan": "^1.9.9", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.11", "@types/nodemailer": "^6.4.14", "@types/compression": "^1.7.5", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.6", "@types/node": "^20.10.5", "@types/jest": "^29.5.8", "@types/supertest": "^2.0.16", "typescript": "^5.3.3", "nodemon": "^3.0.2", "ts-node": "^10.9.2", "jest": "^29.7.0", "ts-jest": "^29.1.1", "supertest": "^6.3.3", "eslint": "^8.56.0", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "prisma": "^5.7.1"}, "engines": {"node": ">=18.0.0"}}