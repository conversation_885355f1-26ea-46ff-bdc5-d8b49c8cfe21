import { z } from 'zod';
export declare enum UserRole {
    ADMIN = "ADMIN",
    PROJECT_MANAGER = "PROJECT_MANAGER",
    RESOURCE = "RESOURCE",
    CLIENT = "CLIENT",
    HR_MANAGER = "HR_MANAGER",
    BILLING_MANAGER = "BILLING_MANAGER"
}
export declare enum UserStatus {
    ACTIVE = "ACTIVE",
    INACTIVE = "INACTIVE",
    PENDING = "PENDING"
}
export declare const UserSchema: z.ZodObject<{
    id: z.ZodString;
    email: z.ZodString;
    firstName: z.ZodString;
    lastName: z.ZodString;
    role: z.ZodNativeEnum<typeof UserRole>;
    status: z.ZodNativeEnum<typeof UserStatus>;
    phone: z.ZodOptional<z.ZodString>;
    avatar: z.ZodOptional<z.ZodString>;
    createdAt: z.ZodDate;
    updatedAt: z.ZodDate;
}, "strip", z.ZodTypeAny, {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    role: UserRole;
    status: UserStatus;
    createdAt: Date;
    updatedAt: Date;
    phone?: string | undefined;
    avatar?: string | undefined;
}, {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    role: UserRole;
    status: UserStatus;
    createdAt: Date;
    updatedAt: Date;
    phone?: string | undefined;
    avatar?: string | undefined;
}>;
export type User = z.infer<typeof UserSchema>;
export declare enum ContractStatus {
    DRAFT = "DRAFT",
    ACTIVE = "ACTIVE",
    COMPLETED = "COMPLETED",
    TERMINATED = "TERMINATED"
}
export declare enum ContractType {
    FIXED_PRICE = "FIXED_PRICE",
    TIME_AND_MATERIAL = "TIME_AND_MATERIAL",
    RETAINER = "RETAINER"
}
export declare const ContractSchema: z.ZodObject<{
    id: z.ZodString;
    title: z.ZodString;
    description: z.ZodString;
    clientId: z.ZodString;
    type: z.ZodNativeEnum<typeof ContractType>;
    status: z.ZodNativeEnum<typeof ContractStatus>;
    startDate: z.ZodDate;
    endDate: z.ZodDate;
    value: z.ZodNumber;
    currency: z.ZodDefault<z.ZodString>;
    terms: z.ZodString;
    createdAt: z.ZodDate;
    updatedAt: z.ZodDate;
}, "strip", z.ZodTypeAny, {
    id: string;
    status: ContractStatus;
    value: number;
    type: ContractType;
    createdAt: Date;
    updatedAt: Date;
    title: string;
    description: string;
    clientId: string;
    startDate: Date;
    endDate: Date;
    currency: string;
    terms: string;
}, {
    id: string;
    status: ContractStatus;
    value: number;
    type: ContractType;
    createdAt: Date;
    updatedAt: Date;
    title: string;
    description: string;
    clientId: string;
    startDate: Date;
    endDate: Date;
    terms: string;
    currency?: string | undefined;
}>;
export type Contract = z.infer<typeof ContractSchema>;
export declare enum ProjectStatus {
    PLANNING = "PLANNING",
    ACTIVE = "ACTIVE",
    ON_HOLD = "ON_HOLD",
    COMPLETED = "COMPLETED",
    CANCELLED = "CANCELLED"
}
export declare const ProjectSchema: z.ZodObject<{
    id: z.ZodString;
    name: z.ZodString;
    description: z.ZodString;
    contractId: z.ZodString;
    managerId: z.ZodString;
    status: z.ZodNativeEnum<typeof ProjectStatus>;
    startDate: z.ZodDate;
    endDate: z.ZodDate;
    budget: z.ZodNumber;
    createdAt: z.ZodDate;
    updatedAt: z.ZodDate;
}, "strip", z.ZodTypeAny, {
    id: string;
    status: ProjectStatus;
    createdAt: Date;
    updatedAt: Date;
    description: string;
    startDate: Date;
    endDate: Date;
    name: string;
    contractId: string;
    managerId: string;
    budget: number;
}, {
    id: string;
    status: ProjectStatus;
    createdAt: Date;
    updatedAt: Date;
    description: string;
    startDate: Date;
    endDate: Date;
    name: string;
    contractId: string;
    managerId: string;
    budget: number;
}>;
export type Project = z.infer<typeof ProjectSchema>;
export declare enum EmploymentType {
    FULL_TIME = "FULL_TIME",
    CONTRACTOR = "CONTRACTOR",
    VENDOR = "VENDOR"
}
export declare enum ResourceStatus {
    AVAILABLE = "AVAILABLE",
    ALLOCATED = "ALLOCATED",
    ON_LEAVE = "ON_LEAVE",
    TERMINATED = "TERMINATED"
}
export declare const SkillSchema: z.ZodObject<{
    id: z.ZodString;
    name: z.ZodString;
    category: z.ZodString;
    description: z.ZodOptional<z.ZodString>;
}, "strip", z.ZodTypeAny, {
    id: string;
    name: string;
    category: string;
    description?: string | undefined;
}, {
    id: string;
    name: string;
    category: string;
    description?: string | undefined;
}>;
export type Skill = z.infer<typeof SkillSchema>;
export declare const ResourceSkillSchema: z.ZodObject<{
    resourceId: z.ZodString;
    skillId: z.ZodString;
    proficiencyLevel: z.ZodNumber;
    yearsOfExperience: z.ZodNumber;
    certified: z.ZodDefault<z.ZodBoolean>;
}, "strip", z.ZodTypeAny, {
    resourceId: string;
    skillId: string;
    proficiencyLevel: number;
    yearsOfExperience: number;
    certified: boolean;
}, {
    resourceId: string;
    skillId: string;
    proficiencyLevel: number;
    yearsOfExperience: number;
    certified?: boolean | undefined;
}>;
export type ResourceSkill = z.infer<typeof ResourceSkillSchema>;
export declare const ResourceSchema: z.ZodObject<{
    id: z.ZodString;
    userId: z.ZodString;
    employeeId: z.ZodString;
    employmentType: z.ZodNativeEnum<typeof EmploymentType>;
    status: z.ZodNativeEnum<typeof ResourceStatus>;
    designation: z.ZodString;
    department: z.ZodString;
    location: z.ZodString;
    hourlyRate: z.ZodNumber;
    joiningDate: z.ZodDate;
    panNumber: z.ZodOptional<z.ZodString>;
    bankDetails: z.ZodOptional<z.ZodString>;
    backgroundCheck: z.ZodDefault<z.ZodBoolean>;
    securityClearance: z.ZodOptional<z.ZodString>;
    createdAt: z.ZodDate;
    updatedAt: z.ZodDate;
}, "strip", z.ZodTypeAny, {
    id: string;
    status: ResourceStatus;
    createdAt: Date;
    updatedAt: Date;
    userId: string;
    employeeId: string;
    employmentType: EmploymentType;
    designation: string;
    department: string;
    location: string;
    hourlyRate: number;
    joiningDate: Date;
    backgroundCheck: boolean;
    panNumber?: string | undefined;
    bankDetails?: string | undefined;
    securityClearance?: string | undefined;
}, {
    id: string;
    status: ResourceStatus;
    createdAt: Date;
    updatedAt: Date;
    userId: string;
    employeeId: string;
    employmentType: EmploymentType;
    designation: string;
    department: string;
    location: string;
    hourlyRate: number;
    joiningDate: Date;
    panNumber?: string | undefined;
    bankDetails?: string | undefined;
    backgroundCheck?: boolean | undefined;
    securityClearance?: string | undefined;
}>;
export type Resource = z.infer<typeof ResourceSchema>;
export declare enum TaskStatus {
    TODO = "TODO",
    IN_PROGRESS = "IN_PROGRESS",
    REVIEW = "REVIEW",
    COMPLETED = "COMPLETED",
    BLOCKED = "BLOCKED"
}
export declare enum TaskPriority {
    LOW = "LOW",
    MEDIUM = "MEDIUM",
    HIGH = "HIGH",
    CRITICAL = "CRITICAL"
}
export declare const TaskSchema: z.ZodObject<{
    id: z.ZodString;
    title: z.ZodString;
    description: z.ZodString;
    projectId: z.ZodString;
    assignedToId: z.ZodString;
    status: z.ZodNativeEnum<typeof TaskStatus>;
    priority: z.ZodNativeEnum<typeof TaskPriority>;
    estimatedHours: z.ZodNumber;
    actualHours: z.ZodDefault<z.ZodNumber>;
    startDate: z.ZodDate;
    dueDate: z.ZodDate;
    createdAt: z.ZodDate;
    updatedAt: z.ZodDate;
}, "strip", z.ZodTypeAny, {
    id: string;
    status: TaskStatus;
    createdAt: Date;
    updatedAt: Date;
    title: string;
    description: string;
    startDate: Date;
    projectId: string;
    assignedToId: string;
    priority: TaskPriority;
    estimatedHours: number;
    actualHours: number;
    dueDate: Date;
}, {
    id: string;
    status: TaskStatus;
    createdAt: Date;
    updatedAt: Date;
    title: string;
    description: string;
    startDate: Date;
    projectId: string;
    assignedToId: string;
    priority: TaskPriority;
    estimatedHours: number;
    dueDate: Date;
    actualHours?: number | undefined;
}>;
export type Task = z.infer<typeof TaskSchema>;
export declare enum TimesheetStatus {
    DRAFT = "DRAFT",
    SUBMITTED = "SUBMITTED",
    APPROVED = "APPROVED",
    REJECTED = "REJECTED"
}
export declare const TimesheetEntrySchema: z.ZodObject<{
    id: z.ZodString;
    timesheetId: z.ZodString;
    taskId: z.ZodString;
    date: z.ZodDate;
    hours: z.ZodNumber;
    description: z.ZodString;
    createdAt: z.ZodDate;
    updatedAt: z.ZodDate;
}, "strip", z.ZodTypeAny, {
    id: string;
    date: Date;
    createdAt: Date;
    updatedAt: Date;
    description: string;
    timesheetId: string;
    taskId: string;
    hours: number;
}, {
    id: string;
    date: Date;
    createdAt: Date;
    updatedAt: Date;
    description: string;
    timesheetId: string;
    taskId: string;
    hours: number;
}>;
export type TimesheetEntry = z.infer<typeof TimesheetEntrySchema>;
export declare const TimesheetSchema: z.ZodObject<{
    id: z.ZodString;
    resourceId: z.ZodString;
    projectId: z.ZodString;
    weekStarting: z.ZodDate;
    weekEnding: z.ZodDate;
    status: z.ZodNativeEnum<typeof TimesheetStatus>;
    totalHours: z.ZodNumber;
    submittedAt: z.ZodOptional<z.ZodDate>;
    approvedAt: z.ZodOptional<z.ZodDate>;
    approvedBy: z.ZodOptional<z.ZodString>;
    rejectionReason: z.ZodOptional<z.ZodString>;
    createdAt: z.ZodDate;
    updatedAt: z.ZodDate;
}, "strip", z.ZodTypeAny, {
    id: string;
    status: TimesheetStatus;
    createdAt: Date;
    updatedAt: Date;
    resourceId: string;
    projectId: string;
    weekStarting: Date;
    weekEnding: Date;
    totalHours: number;
    submittedAt?: Date | undefined;
    approvedAt?: Date | undefined;
    approvedBy?: string | undefined;
    rejectionReason?: string | undefined;
}, {
    id: string;
    status: TimesheetStatus;
    createdAt: Date;
    updatedAt: Date;
    resourceId: string;
    projectId: string;
    weekStarting: Date;
    weekEnding: Date;
    totalHours: number;
    submittedAt?: Date | undefined;
    approvedAt?: Date | undefined;
    approvedBy?: string | undefined;
    rejectionReason?: string | undefined;
}>;
export type Timesheet = z.infer<typeof TimesheetSchema>;
export declare enum InvoiceStatus {
    DRAFT = "DRAFT",
    SENT = "SENT",
    PAID = "PAID",
    OVERDUE = "OVERDUE",
    CANCELLED = "CANCELLED"
}
export declare const InvoiceSchema: z.ZodObject<{
    id: z.ZodString;
    invoiceNumber: z.ZodString;
    projectId: z.ZodString;
    resourceId: z.ZodString;
    timesheetId: z.ZodString;
    status: z.ZodNativeEnum<typeof InvoiceStatus>;
    issueDate: z.ZodDate;
    dueDate: z.ZodDate;
    subtotal: z.ZodNumber;
    tax: z.ZodNumber;
    penalties: z.ZodDefault<z.ZodNumber>;
    total: z.ZodNumber;
    paidAt: z.ZodOptional<z.ZodDate>;
    createdAt: z.ZodDate;
    updatedAt: z.ZodDate;
}, "strip", z.ZodTypeAny, {
    id: string;
    status: InvoiceStatus;
    createdAt: Date;
    updatedAt: Date;
    resourceId: string;
    projectId: string;
    dueDate: Date;
    timesheetId: string;
    invoiceNumber: string;
    issueDate: Date;
    subtotal: number;
    tax: number;
    penalties: number;
    total: number;
    paidAt?: Date | undefined;
}, {
    id: string;
    status: InvoiceStatus;
    createdAt: Date;
    updatedAt: Date;
    resourceId: string;
    projectId: string;
    dueDate: Date;
    timesheetId: string;
    invoiceNumber: string;
    issueDate: Date;
    subtotal: number;
    tax: number;
    total: number;
    penalties?: number | undefined;
    paidAt?: Date | undefined;
}>;
export type Invoice = z.infer<typeof InvoiceSchema>;
export declare enum VendorStatus {
    ACTIVE = "ACTIVE",
    INACTIVE = "INACTIVE",
    BLACKLISTED = "BLACKLISTED"
}
export declare const VendorSchema: z.ZodObject<{
    id: z.ZodString;
    name: z.ZodString;
    contactPerson: z.ZodString;
    email: z.ZodString;
    phone: z.ZodString;
    address: z.ZodString;
    panNumber: z.ZodString;
    gstNumber: z.ZodOptional<z.ZodString>;
    bankDetails: z.ZodString;
    status: z.ZodNativeEnum<typeof VendorStatus>;
    createdAt: z.ZodDate;
    updatedAt: z.ZodDate;
}, "strip", z.ZodTypeAny, {
    id: string;
    email: string;
    status: VendorStatus;
    phone: string;
    createdAt: Date;
    updatedAt: Date;
    name: string;
    panNumber: string;
    bankDetails: string;
    contactPerson: string;
    address: string;
    gstNumber?: string | undefined;
}, {
    id: string;
    email: string;
    status: VendorStatus;
    phone: string;
    createdAt: Date;
    updatedAt: Date;
    name: string;
    panNumber: string;
    bankDetails: string;
    contactPerson: string;
    address: string;
    gstNumber?: string | undefined;
}>;
export type Vendor = z.infer<typeof VendorSchema>;
export interface ApiResponse<T> {
    success: boolean;
    data?: T;
    error?: string;
    message?: string;
}
export interface PaginatedResponse<T> {
    data: T[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
}
export interface LoginForm {
    email: string;
    password: string;
}
export interface CreateContractForm {
    title: string;
    description: string;
    clientId: string;
    type: ContractType;
    startDate: Date;
    endDate: Date;
    value: number;
    currency: string;
    terms: string;
}
export interface CreateProjectForm {
    name: string;
    description: string;
    contractId: string;
    managerId: string;
    startDate: Date;
    endDate: Date;
    budget: number;
}
export interface CreateTaskForm {
    title: string;
    description: string;
    projectId: string;
    assignedToId: string;
    priority: TaskPriority;
    estimatedHours: number;
    startDate: Date;
    dueDate: Date;
}
export interface TimesheetEntryForm {
    taskId: string;
    date: Date;
    hours: number;
    description: string;
}
//# sourceMappingURL=types.d.ts.map