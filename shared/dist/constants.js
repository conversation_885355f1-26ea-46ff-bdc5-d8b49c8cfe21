"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MENU_ITEMS = exports.SUCCESS_MESSAGES = exports.ERROR_MESSAGES = exports.DEFAULT_VALUES = exports.ROLE_PERMISSIONS = exports.STATUS_COLORS = exports.VALIDATION_RULES = exports.DISPLAY_DATETIME_FORMAT = exports.DISPLAY_DATE_FORMAT = exports.DATETIME_FORMAT = exports.DATE_FORMAT = exports.ALLOWED_FILE_TYPES = exports.MAX_FILE_SIZE = exports.ITEMS_PER_PAGE = exports.API_ENDPOINTS = exports.API_BASE_URL = void 0;
// API Constants
exports.API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';
exports.API_ENDPOINTS = {
    AUTH: {
        LOGIN: '/auth/login',
        LOGOUT: '/auth/logout',
        REGISTER: '/auth/register',
        REFRESH: '/auth/refresh',
        PROFILE: '/auth/profile'
    },
    USERS: {
        BASE: '/users',
        BY_ID: (id) => `/users/${id}`,
        BY_ROLE: (role) => `/users?role=${role}`
    },
    CONTRACTS: {
        BASE: '/contracts',
        BY_ID: (id) => `/contracts/${id}`,
        BY_CLIENT: (clientId) => `/contracts?clientId=${clientId}`
    },
    PROJECTS: {
        BASE: '/projects',
        BY_ID: (id) => `/projects/${id}`,
        BY_CONTRACT: (contractId) => `/projects?contractId=${contractId}`,
        TASKS: (projectId) => `/projects/${projectId}/tasks`,
        RESOURCES: (projectId) => `/projects/${projectId}/resources`
    },
    RESOURCES: {
        BASE: '/resources',
        BY_ID: (id) => `/resources/${id}`,
        SKILLS: (resourceId) => `/resources/${resourceId}/skills`,
        AVAILABILITY: (resourceId) => `/resources/${resourceId}/availability`,
        SEARCH: '/resources/search'
    },
    TASKS: {
        BASE: '/tasks',
        BY_ID: (id) => `/tasks/${id}`,
        BY_PROJECT: (projectId) => `/tasks?projectId=${projectId}`,
        BY_ASSIGNEE: (assigneeId) => `/tasks?assigneeId=${assigneeId}`
    },
    TIMESHEETS: {
        BASE: '/timesheets',
        BY_ID: (id) => `/timesheets/${id}`,
        BY_RESOURCE: (resourceId) => `/timesheets?resourceId=${resourceId}`,
        BY_PROJECT: (projectId) => `/timesheets?projectId=${projectId}`,
        ENTRIES: (timesheetId) => `/timesheets/${timesheetId}/entries`,
        APPROVE: (timesheetId) => `/timesheets/${timesheetId}/approve`,
        REJECT: (timesheetId) => `/timesheets/${timesheetId}/reject`
    },
    INVOICES: {
        BASE: '/invoices',
        BY_ID: (id) => `/invoices/${id}`,
        BY_PROJECT: (projectId) => `/invoices?projectId=${projectId}`,
        BY_RESOURCE: (resourceId) => `/invoices?resourceId=${resourceId}`,
        GENERATE: '/invoices/generate'
    },
    VENDORS: {
        BASE: '/vendors',
        BY_ID: (id) => `/vendors/${id}`
    },
    SKILLS: {
        BASE: '/skills',
        BY_ID: (id) => `/skills/${id}`,
        BY_CATEGORY: (category) => `/skills?category=${category}`
    },
    DASHBOARD: {
        STATS: '/dashboard/stats',
        CHARTS: '/dashboard/charts'
    }
};
// UI Constants
exports.ITEMS_PER_PAGE = 10;
exports.MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
exports.ALLOWED_FILE_TYPES = ['image/jpeg', 'image/png', 'application/pdf'];
// Date Constants
exports.DATE_FORMAT = 'YYYY-MM-DD';
exports.DATETIME_FORMAT = 'YYYY-MM-DD HH:mm:ss';
exports.DISPLAY_DATE_FORMAT = 'MMM DD, YYYY';
exports.DISPLAY_DATETIME_FORMAT = 'MMM DD, YYYY HH:mm';
// Validation Constants
exports.VALIDATION_RULES = {
    PASSWORD_MIN_LENGTH: 8,
    NAME_MIN_LENGTH: 2,
    NAME_MAX_LENGTH: 50,
    DESCRIPTION_MAX_LENGTH: 1000,
    PHONE_MIN_LENGTH: 10,
    PHONE_MAX_LENGTH: 15
};
// Status Colors
exports.STATUS_COLORS = {
    ACTIVE: '#10B981',
    INACTIVE: '#6B7280',
    PENDING: '#F59E0B',
    COMPLETED: '#3B82F6',
    CANCELLED: '#EF4444',
    DRAFT: '#6B7280',
    SUBMITTED: '#F59E0B',
    APPROVED: '#10B981',
    REJECTED: '#EF4444',
    TODO: '#6B7280',
    IN_PROGRESS: '#3B82F6',
    REVIEW: '#F59E0B',
    BLOCKED: '#EF4444',
    LOW: '#6B7280',
    MEDIUM: '#F59E0B',
    HIGH: '#FB923C',
    CRITICAL: '#EF4444'
};
// Role Permissions
exports.ROLE_PERMISSIONS = {
    ADMIN: [
        'contracts:read',
        'contracts:write',
        'contracts:delete',
        'projects:read',
        'projects:write',
        'projects:delete',
        'resources:read',
        'resources:write',
        'resources:delete',
        'tasks:read',
        'tasks:write',
        'tasks:delete',
        'timesheets:read',
        'timesheets:approve',
        'invoices:read',
        'invoices:write',
        'invoices:delete',
        'vendors:read',
        'vendors:write',
        'vendors:delete',
        'users:read',
        'users:write',
        'users:delete'
    ],
    PROJECT_MANAGER: [
        'contracts:read',
        'projects:read',
        'projects:write',
        'resources:read',
        'tasks:read',
        'tasks:write',
        'tasks:delete',
        'timesheets:read',
        'timesheets:approve',
        'invoices:read'
    ],
    RESOURCE: [
        'projects:read',
        'tasks:read',
        'tasks:write',
        'timesheets:read',
        'timesheets:write'
    ],
    CLIENT: [
        'contracts:read',
        'projects:read',
        'timesheets:read',
        'invoices:read'
    ],
    HR_MANAGER: [
        'resources:read',
        'resources:write',
        'resources:delete',
        'vendors:read',
        'vendors:write',
        'users:read',
        'users:write'
    ],
    BILLING_MANAGER: [
        'timesheets:read',
        'invoices:read',
        'invoices:write',
        'invoices:delete',
        'vendors:read'
    ]
};
// Default Values
exports.DEFAULT_VALUES = {
    CURRENCY: 'USD',
    TIMEZONE: 'UTC',
    PAGINATION_LIMIT: 10,
    TIMESHEET_HOURS_PER_DAY: 8,
    WORKING_DAYS_PER_WEEK: 5
};
// Error Messages
exports.ERROR_MESSAGES = {
    REQUIRED_FIELD: 'This field is required',
    INVALID_EMAIL: 'Please enter a valid email address',
    INVALID_PHONE: 'Please enter a valid phone number',
    INVALID_PAN: 'Please enter a valid PAN number',
    PASSWORD_TOO_SHORT: `Password must be at least ${exports.VALIDATION_RULES.PASSWORD_MIN_LENGTH} characters`,
    UNAUTHORIZED: 'You are not authorized to perform this action',
    NOT_FOUND: 'The requested resource was not found',
    SERVER_ERROR: 'An unexpected error occurred. Please try again.',
    NETWORK_ERROR: 'Network error. Please check your connection.',
    VALIDATION_ERROR: 'Please check your input and try again'
};
// Success Messages
exports.SUCCESS_MESSAGES = {
    CREATED: 'Created successfully',
    UPDATED: 'Updated successfully',
    DELETED: 'Deleted successfully',
    SAVED: 'Saved successfully',
    SUBMITTED: 'Submitted successfully',
    APPROVED: 'Approved successfully',
    REJECTED: 'Rejected successfully',
    LOGIN_SUCCESS: 'Logged in successfully',
    LOGOUT_SUCCESS: 'Logged out successfully'
};
// Navigation Menu Items
exports.MENU_ITEMS = [
    {
        label: 'Dashboard',
        href: '/dashboard',
        icon: 'dashboard',
        roles: ['ADMIN', 'PROJECT_MANAGER', 'HR_MANAGER', 'BILLING_MANAGER']
    },
    {
        label: 'Contracts',
        href: '/contracts',
        icon: 'contract',
        roles: ['ADMIN', 'PROJECT_MANAGER']
    },
    {
        label: 'Projects',
        href: '/projects',
        icon: 'project',
        roles: ['ADMIN', 'PROJECT_MANAGER', 'RESOURCE']
    },
    {
        label: 'Resources',
        href: '/resources',
        icon: 'users',
        roles: ['ADMIN', 'HR_MANAGER', 'PROJECT_MANAGER']
    },
    {
        label: 'Tasks',
        href: '/tasks',
        icon: 'tasks',
        roles: ['ADMIN', 'PROJECT_MANAGER', 'RESOURCE']
    },
    {
        label: 'Timesheets',
        href: '/timesheets',
        icon: 'clock',
        roles: ['ADMIN', 'PROJECT_MANAGER', 'RESOURCE', 'BILLING_MANAGER']
    },
    {
        label: 'Invoices',
        href: '/invoices',
        icon: 'invoice',
        roles: ['ADMIN', 'BILLING_MANAGER', 'CLIENT']
    },
    {
        label: 'Vendors',
        href: '/vendors',
        icon: 'building',
        roles: ['ADMIN', 'HR_MANAGER']
    }
];
//# sourceMappingURL=constants.js.map