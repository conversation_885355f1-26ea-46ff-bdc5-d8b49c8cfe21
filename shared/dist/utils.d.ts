import { UserRole } from './types';
export declare const formatDate: (date: Date) => string;
export declare const formatDateTime: (date: Date) => string;
export declare const getWeekStartDate: (date: Date) => Date;
export declare const getWeekEndDate: (date: Date) => Date;
export declare const getDaysInWeek: (startDate: Date) => Date[];
export declare const formatCurrency: (amount: number, currency?: string) => string;
export declare const generateId: () => string;
export declare const slugify: (text: string) => string;
export declare const capitalize: (text: string) => string;
export declare const getInitials: (firstName: string, lastName: string) => string;
export declare const hasPermission: (userRole: UserRole, requiredRoles: UserRole[]) => boolean;
export declare const canManageProjects: (userRole: UserRole) => boolean;
export declare const canManageResources: (userRole: UserRole) => boolean;
export declare const canManageBilling: (userRole: UserRole) => boolean;
export declare const canViewAllProjects: (userRole: UserRole) => boolean;
export declare const isValidEmail: (email: string) => boolean;
export declare const isValidPhone: (phone: string) => boolean;
export declare const isValidPAN: (pan: string) => boolean;
export declare const groupBy: <T, K extends keyof any>(array: T[], key: (item: T) => K) => Record<K, T[]>;
export declare const sortBy: <T>(array: T[], key: keyof T, direction?: "asc" | "desc") => T[];
export declare const calculateHours: (startTime: string, endTime: string) => number;
export declare const formatHours: (hours: number) => string;
export declare const getStatusColor: (status: string) => string;
export declare const searchFilter: <T>(items: T[], searchTerm: string, searchFields: (keyof T)[]) => T[];
//# sourceMappingURL=utils.d.ts.map