"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.searchFilter = exports.getStatusColor = exports.formatHours = exports.calculateHours = exports.sortBy = exports.groupBy = exports.isValidPAN = exports.isValidPhone = exports.isValidEmail = exports.canViewAllProjects = exports.canManageBilling = exports.canManageResources = exports.canManageProjects = exports.hasPermission = exports.getInitials = exports.capitalize = exports.slugify = exports.generateId = exports.formatCurrency = exports.getDaysInWeek = exports.getWeekEndDate = exports.getWeekStartDate = exports.formatDateTime = exports.formatDate = void 0;
const types_1 = require("./types");
// Date utilities
const formatDate = (date) => {
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
};
exports.formatDate = formatDate;
const formatDateTime = (date) => {
    return date.toLocaleString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
};
exports.formatDateTime = formatDateTime;
const getWeekStartDate = (date) => {
    const d = new Date(date);
    const day = d.getDay();
    const diff = d.getDate() - day + (day === 0 ? -6 : 1); // Adjust when day is Sunday
    return new Date(d.setDate(diff));
};
exports.getWeekStartDate = getWeekStartDate;
const getWeekEndDate = (date) => {
    const startDate = (0, exports.getWeekStartDate)(date);
    const endDate = new Date(startDate);
    endDate.setDate(startDate.getDate() + 6);
    return endDate;
};
exports.getWeekEndDate = getWeekEndDate;
const getDaysInWeek = (startDate) => {
    const days = [];
    for (let i = 0; i < 7; i++) {
        const day = new Date(startDate);
        day.setDate(startDate.getDate() + i);
        days.push(day);
    }
    return days;
};
exports.getDaysInWeek = getDaysInWeek;
// Currency utilities
const formatCurrency = (amount, currency = 'USD') => {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currency
    }).format(amount);
};
exports.formatCurrency = formatCurrency;
// String utilities
const generateId = () => {
    return Math.random().toString(36).substr(2, 9);
};
exports.generateId = generateId;
const slugify = (text) => {
    return text
        .toLowerCase()
        .replace(/[^\w ]+/g, '')
        .replace(/ +/g, '-');
};
exports.slugify = slugify;
const capitalize = (text) => {
    return text.charAt(0).toUpperCase() + text.slice(1).toLowerCase();
};
exports.capitalize = capitalize;
const getInitials = (firstName, lastName) => {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
};
exports.getInitials = getInitials;
// Permission utilities
const hasPermission = (userRole, requiredRoles) => {
    return requiredRoles.includes(userRole);
};
exports.hasPermission = hasPermission;
const canManageProjects = (userRole) => {
    return (0, exports.hasPermission)(userRole, [types_1.UserRole.ADMIN, types_1.UserRole.PROJECT_MANAGER]);
};
exports.canManageProjects = canManageProjects;
const canManageResources = (userRole) => {
    return (0, exports.hasPermission)(userRole, [types_1.UserRole.ADMIN, types_1.UserRole.HR_MANAGER]);
};
exports.canManageResources = canManageResources;
const canManageBilling = (userRole) => {
    return (0, exports.hasPermission)(userRole, [types_1.UserRole.ADMIN, types_1.UserRole.BILLING_MANAGER]);
};
exports.canManageBilling = canManageBilling;
const canViewAllProjects = (userRole) => {
    return (0, exports.hasPermission)(userRole, [types_1.UserRole.ADMIN, types_1.UserRole.PROJECT_MANAGER, types_1.UserRole.HR_MANAGER]);
};
exports.canViewAllProjects = canViewAllProjects;
// Validation utilities
const isValidEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
};
exports.isValidEmail = isValidEmail;
const isValidPhone = (phone) => {
    const phoneRegex = /^\+?[\d\s\-\(\)]+$/;
    return phoneRegex.test(phone) && phone.replace(/\D/g, '').length >= 10;
};
exports.isValidPhone = isValidPhone;
const isValidPAN = (pan) => {
    const panRegex = /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/;
    return panRegex.test(pan);
};
exports.isValidPAN = isValidPAN;
// Array utilities
const groupBy = (array, key) => {
    return array.reduce((groups, item) => {
        const group = key(item);
        groups[group] = groups[group] || [];
        groups[group].push(item);
        return groups;
    }, {});
};
exports.groupBy = groupBy;
const sortBy = (array, key, direction = 'asc') => {
    return [...array].sort((a, b) => {
        const aVal = a[key];
        const bVal = b[key];
        if (aVal < bVal)
            return direction === 'asc' ? -1 : 1;
        if (aVal > bVal)
            return direction === 'asc' ? 1 : -1;
        return 0;
    });
};
exports.sortBy = sortBy;
// Time utilities
const calculateHours = (startTime, endTime) => {
    const start = new Date(`2000-01-01T${startTime}`);
    const end = new Date(`2000-01-01T${endTime}`);
    const diff = end.getTime() - start.getTime();
    return diff / (1000 * 60 * 60); // Convert milliseconds to hours
};
exports.calculateHours = calculateHours;
const formatHours = (hours) => {
    const wholeHours = Math.floor(hours);
    const minutes = Math.round((hours - wholeHours) * 60);
    if (minutes === 0) {
        return `${wholeHours}h`;
    }
    return `${wholeHours}h ${minutes}m`;
};
exports.formatHours = formatHours;
// Status utilities
const getStatusColor = (status) => {
    const statusColors = {
        ACTIVE: 'green',
        INACTIVE: 'gray',
        PENDING: 'yellow',
        COMPLETED: 'blue',
        CANCELLED: 'red',
        DRAFT: 'gray',
        SUBMITTED: 'yellow',
        APPROVED: 'green',
        REJECTED: 'red',
        TODO: 'gray',
        IN_PROGRESS: 'blue',
        REVIEW: 'yellow',
        BLOCKED: 'red',
        LOW: 'gray',
        MEDIUM: 'yellow',
        HIGH: 'orange',
        CRITICAL: 'red'
    };
    return statusColors[status] || 'gray';
};
exports.getStatusColor = getStatusColor;
// Search utilities
const searchFilter = (items, searchTerm, searchFields) => {
    if (!searchTerm)
        return items;
    const term = searchTerm.toLowerCase();
    return items.filter(item => searchFields.some(field => {
        const value = item[field];
        return value && String(value).toLowerCase().includes(term);
    }));
};
exports.searchFilter = searchFilter;
//# sourceMappingURL=utils.js.map