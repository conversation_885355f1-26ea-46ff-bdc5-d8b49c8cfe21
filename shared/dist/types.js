"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.VendorSchema = exports.VendorStatus = exports.InvoiceSchema = exports.InvoiceStatus = exports.TimesheetSchema = exports.TimesheetEntrySchema = exports.TimesheetStatus = exports.TaskSchema = exports.TaskPriority = exports.TaskStatus = exports.ResourceSchema = exports.ResourceSkillSchema = exports.SkillSchema = exports.ResourceStatus = exports.EmploymentType = exports.ProjectSchema = exports.ProjectStatus = exports.ContractSchema = exports.ContractType = exports.ContractStatus = exports.UserSchema = exports.UserStatus = exports.UserRole = void 0;
const zod_1 = require("zod");
// User and Authentication Types
var UserRole;
(function (UserRole) {
    UserRole["ADMIN"] = "ADMIN";
    UserRole["PROJECT_MANAGER"] = "PROJECT_MANAGER";
    UserRole["RESOURCE"] = "RESOURCE";
    UserRole["CLIENT"] = "CLIENT";
    UserRole["HR_MANAGER"] = "HR_MANAGER";
    UserRole["BILLING_MANAGER"] = "BILLING_MANAGER";
})(UserRole || (exports.UserRole = UserRole = {}));
var UserStatus;
(function (UserStatus) {
    UserStatus["ACTIVE"] = "ACTIVE";
    UserStatus["INACTIVE"] = "INACTIVE";
    UserStatus["PENDING"] = "PENDING";
})(UserStatus || (exports.UserStatus = UserStatus = {}));
exports.UserSchema = zod_1.z.object({
    id: zod_1.z.string(),
    email: zod_1.z.string().email(),
    firstName: zod_1.z.string(),
    lastName: zod_1.z.string(),
    role: zod_1.z.nativeEnum(UserRole),
    status: zod_1.z.nativeEnum(UserStatus),
    phone: zod_1.z.string().optional(),
    avatar: zod_1.z.string().optional(),
    createdAt: zod_1.z.date(),
    updatedAt: zod_1.z.date()
});
// Contract Types
var ContractStatus;
(function (ContractStatus) {
    ContractStatus["DRAFT"] = "DRAFT";
    ContractStatus["ACTIVE"] = "ACTIVE";
    ContractStatus["COMPLETED"] = "COMPLETED";
    ContractStatus["TERMINATED"] = "TERMINATED";
})(ContractStatus || (exports.ContractStatus = ContractStatus = {}));
var ContractType;
(function (ContractType) {
    ContractType["FIXED_PRICE"] = "FIXED_PRICE";
    ContractType["TIME_AND_MATERIAL"] = "TIME_AND_MATERIAL";
    ContractType["RETAINER"] = "RETAINER";
})(ContractType || (exports.ContractType = ContractType = {}));
exports.ContractSchema = zod_1.z.object({
    id: zod_1.z.string(),
    title: zod_1.z.string(),
    description: zod_1.z.string(),
    clientId: zod_1.z.string(),
    type: zod_1.z.nativeEnum(ContractType),
    status: zod_1.z.nativeEnum(ContractStatus),
    startDate: zod_1.z.date(),
    endDate: zod_1.z.date(),
    value: zod_1.z.number(),
    currency: zod_1.z.string().default('USD'),
    terms: zod_1.z.string(),
    createdAt: zod_1.z.date(),
    updatedAt: zod_1.z.date()
});
// Project Types
var ProjectStatus;
(function (ProjectStatus) {
    ProjectStatus["PLANNING"] = "PLANNING";
    ProjectStatus["ACTIVE"] = "ACTIVE";
    ProjectStatus["ON_HOLD"] = "ON_HOLD";
    ProjectStatus["COMPLETED"] = "COMPLETED";
    ProjectStatus["CANCELLED"] = "CANCELLED";
})(ProjectStatus || (exports.ProjectStatus = ProjectStatus = {}));
exports.ProjectSchema = zod_1.z.object({
    id: zod_1.z.string(),
    name: zod_1.z.string(),
    description: zod_1.z.string(),
    contractId: zod_1.z.string(),
    managerId: zod_1.z.string(),
    status: zod_1.z.nativeEnum(ProjectStatus),
    startDate: zod_1.z.date(),
    endDate: zod_1.z.date(),
    budget: zod_1.z.number(),
    createdAt: zod_1.z.date(),
    updatedAt: zod_1.z.date()
});
// Resource Types
var EmploymentType;
(function (EmploymentType) {
    EmploymentType["FULL_TIME"] = "FULL_TIME";
    EmploymentType["CONTRACTOR"] = "CONTRACTOR";
    EmploymentType["VENDOR"] = "VENDOR";
})(EmploymentType || (exports.EmploymentType = EmploymentType = {}));
var ResourceStatus;
(function (ResourceStatus) {
    ResourceStatus["AVAILABLE"] = "AVAILABLE";
    ResourceStatus["ALLOCATED"] = "ALLOCATED";
    ResourceStatus["ON_LEAVE"] = "ON_LEAVE";
    ResourceStatus["TERMINATED"] = "TERMINATED";
})(ResourceStatus || (exports.ResourceStatus = ResourceStatus = {}));
exports.SkillSchema = zod_1.z.object({
    id: zod_1.z.string(),
    name: zod_1.z.string(),
    category: zod_1.z.string(),
    description: zod_1.z.string().optional()
});
exports.ResourceSkillSchema = zod_1.z.object({
    resourceId: zod_1.z.string(),
    skillId: zod_1.z.string(),
    proficiencyLevel: zod_1.z.number().min(1).max(5),
    yearsOfExperience: zod_1.z.number(),
    certified: zod_1.z.boolean().default(false)
});
exports.ResourceSchema = zod_1.z.object({
    id: zod_1.z.string(),
    userId: zod_1.z.string(),
    employeeId: zod_1.z.string(),
    employmentType: zod_1.z.nativeEnum(EmploymentType),
    status: zod_1.z.nativeEnum(ResourceStatus),
    designation: zod_1.z.string(),
    department: zod_1.z.string(),
    location: zod_1.z.string(),
    hourlyRate: zod_1.z.number(),
    joiningDate: zod_1.z.date(),
    panNumber: zod_1.z.string().optional(),
    bankDetails: zod_1.z.string().optional(),
    backgroundCheck: zod_1.z.boolean().default(false),
    securityClearance: zod_1.z.string().optional(),
    createdAt: zod_1.z.date(),
    updatedAt: zod_1.z.date()
});
// Task Types
var TaskStatus;
(function (TaskStatus) {
    TaskStatus["TODO"] = "TODO";
    TaskStatus["IN_PROGRESS"] = "IN_PROGRESS";
    TaskStatus["REVIEW"] = "REVIEW";
    TaskStatus["COMPLETED"] = "COMPLETED";
    TaskStatus["BLOCKED"] = "BLOCKED";
})(TaskStatus || (exports.TaskStatus = TaskStatus = {}));
var TaskPriority;
(function (TaskPriority) {
    TaskPriority["LOW"] = "LOW";
    TaskPriority["MEDIUM"] = "MEDIUM";
    TaskPriority["HIGH"] = "HIGH";
    TaskPriority["CRITICAL"] = "CRITICAL";
})(TaskPriority || (exports.TaskPriority = TaskPriority = {}));
exports.TaskSchema = zod_1.z.object({
    id: zod_1.z.string(),
    title: zod_1.z.string(),
    description: zod_1.z.string(),
    projectId: zod_1.z.string(),
    assignedToId: zod_1.z.string(),
    status: zod_1.z.nativeEnum(TaskStatus),
    priority: zod_1.z.nativeEnum(TaskPriority),
    estimatedHours: zod_1.z.number(),
    actualHours: zod_1.z.number().default(0),
    startDate: zod_1.z.date(),
    dueDate: zod_1.z.date(),
    createdAt: zod_1.z.date(),
    updatedAt: zod_1.z.date()
});
// Timesheet Types
var TimesheetStatus;
(function (TimesheetStatus) {
    TimesheetStatus["DRAFT"] = "DRAFT";
    TimesheetStatus["SUBMITTED"] = "SUBMITTED";
    TimesheetStatus["APPROVED"] = "APPROVED";
    TimesheetStatus["REJECTED"] = "REJECTED";
})(TimesheetStatus || (exports.TimesheetStatus = TimesheetStatus = {}));
exports.TimesheetEntrySchema = zod_1.z.object({
    id: zod_1.z.string(),
    timesheetId: zod_1.z.string(),
    taskId: zod_1.z.string(),
    date: zod_1.z.date(),
    hours: zod_1.z.number(),
    description: zod_1.z.string(),
    createdAt: zod_1.z.date(),
    updatedAt: zod_1.z.date()
});
exports.TimesheetSchema = zod_1.z.object({
    id: zod_1.z.string(),
    resourceId: zod_1.z.string(),
    projectId: zod_1.z.string(),
    weekStarting: zod_1.z.date(),
    weekEnding: zod_1.z.date(),
    status: zod_1.z.nativeEnum(TimesheetStatus),
    totalHours: zod_1.z.number(),
    submittedAt: zod_1.z.date().optional(),
    approvedAt: zod_1.z.date().optional(),
    approvedBy: zod_1.z.string().optional(),
    rejectionReason: zod_1.z.string().optional(),
    createdAt: zod_1.z.date(),
    updatedAt: zod_1.z.date()
});
// Invoice Types
var InvoiceStatus;
(function (InvoiceStatus) {
    InvoiceStatus["DRAFT"] = "DRAFT";
    InvoiceStatus["SENT"] = "SENT";
    InvoiceStatus["PAID"] = "PAID";
    InvoiceStatus["OVERDUE"] = "OVERDUE";
    InvoiceStatus["CANCELLED"] = "CANCELLED";
})(InvoiceStatus || (exports.InvoiceStatus = InvoiceStatus = {}));
exports.InvoiceSchema = zod_1.z.object({
    id: zod_1.z.string(),
    invoiceNumber: zod_1.z.string(),
    projectId: zod_1.z.string(),
    resourceId: zod_1.z.string(),
    timesheetId: zod_1.z.string(),
    status: zod_1.z.nativeEnum(InvoiceStatus),
    issueDate: zod_1.z.date(),
    dueDate: zod_1.z.date(),
    subtotal: zod_1.z.number(),
    tax: zod_1.z.number(),
    penalties: zod_1.z.number().default(0),
    total: zod_1.z.number(),
    paidAt: zod_1.z.date().optional(),
    createdAt: zod_1.z.date(),
    updatedAt: zod_1.z.date()
});
// Vendor Types
var VendorStatus;
(function (VendorStatus) {
    VendorStatus["ACTIVE"] = "ACTIVE";
    VendorStatus["INACTIVE"] = "INACTIVE";
    VendorStatus["BLACKLISTED"] = "BLACKLISTED";
})(VendorStatus || (exports.VendorStatus = VendorStatus = {}));
exports.VendorSchema = zod_1.z.object({
    id: zod_1.z.string(),
    name: zod_1.z.string(),
    contactPerson: zod_1.z.string(),
    email: zod_1.z.string().email(),
    phone: zod_1.z.string(),
    address: zod_1.z.string(),
    panNumber: zod_1.z.string(),
    gstNumber: zod_1.z.string().optional(),
    bankDetails: zod_1.z.string(),
    status: zod_1.z.nativeEnum(VendorStatus),
    createdAt: zod_1.z.date(),
    updatedAt: zod_1.z.date()
});
//# sourceMappingURL=types.js.map