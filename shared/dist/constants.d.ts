export declare const API_BASE_URL: string;
export declare const API_ENDPOINTS: {
    AUTH: {
        LOGIN: string;
        LOGOUT: string;
        REGISTER: string;
        REFRESH: string;
        PROFILE: string;
    };
    USERS: {
        BASE: string;
        BY_ID: (id: string) => string;
        BY_ROLE: (role: string) => string;
    };
    CONTRACTS: {
        BASE: string;
        BY_ID: (id: string) => string;
        BY_CLIENT: (clientId: string) => string;
    };
    PROJECTS: {
        BASE: string;
        BY_ID: (id: string) => string;
        BY_CONTRACT: (contractId: string) => string;
        TASKS: (projectId: string) => string;
        RESOURCES: (projectId: string) => string;
    };
    RESOURCES: {
        BASE: string;
        BY_ID: (id: string) => string;
        SKILLS: (resourceId: string) => string;
        AVAILABILITY: (resourceId: string) => string;
        SEARCH: string;
    };
    TASKS: {
        BASE: string;
        BY_ID: (id: string) => string;
        BY_PROJECT: (projectId: string) => string;
        BY_ASSIGNEE: (assigneeId: string) => string;
    };
    TIMESHEETS: {
        BASE: string;
        BY_ID: (id: string) => string;
        BY_RESOURCE: (resourceId: string) => string;
        BY_PROJECT: (projectId: string) => string;
        ENTRIES: (timesheetId: string) => string;
        APPROVE: (timesheetId: string) => string;
        REJECT: (timesheetId: string) => string;
    };
    INVOICES: {
        BASE: string;
        BY_ID: (id: string) => string;
        BY_PROJECT: (projectId: string) => string;
        BY_RESOURCE: (resourceId: string) => string;
        GENERATE: string;
    };
    VENDORS: {
        BASE: string;
        BY_ID: (id: string) => string;
    };
    SKILLS: {
        BASE: string;
        BY_ID: (id: string) => string;
        BY_CATEGORY: (category: string) => string;
    };
    DASHBOARD: {
        STATS: string;
        CHARTS: string;
    };
};
export declare const ITEMS_PER_PAGE = 10;
export declare const MAX_FILE_SIZE: number;
export declare const ALLOWED_FILE_TYPES: string[];
export declare const DATE_FORMAT = "YYYY-MM-DD";
export declare const DATETIME_FORMAT = "YYYY-MM-DD HH:mm:ss";
export declare const DISPLAY_DATE_FORMAT = "MMM DD, YYYY";
export declare const DISPLAY_DATETIME_FORMAT = "MMM DD, YYYY HH:mm";
export declare const VALIDATION_RULES: {
    PASSWORD_MIN_LENGTH: number;
    NAME_MIN_LENGTH: number;
    NAME_MAX_LENGTH: number;
    DESCRIPTION_MAX_LENGTH: number;
    PHONE_MIN_LENGTH: number;
    PHONE_MAX_LENGTH: number;
};
export declare const STATUS_COLORS: {
    ACTIVE: string;
    INACTIVE: string;
    PENDING: string;
    COMPLETED: string;
    CANCELLED: string;
    DRAFT: string;
    SUBMITTED: string;
    APPROVED: string;
    REJECTED: string;
    TODO: string;
    IN_PROGRESS: string;
    REVIEW: string;
    BLOCKED: string;
    LOW: string;
    MEDIUM: string;
    HIGH: string;
    CRITICAL: string;
};
export declare const ROLE_PERMISSIONS: {
    ADMIN: string[];
    PROJECT_MANAGER: string[];
    RESOURCE: string[];
    CLIENT: string[];
    HR_MANAGER: string[];
    BILLING_MANAGER: string[];
};
export declare const DEFAULT_VALUES: {
    CURRENCY: string;
    TIMEZONE: string;
    PAGINATION_LIMIT: number;
    TIMESHEET_HOURS_PER_DAY: number;
    WORKING_DAYS_PER_WEEK: number;
};
export declare const ERROR_MESSAGES: {
    REQUIRED_FIELD: string;
    INVALID_EMAIL: string;
    INVALID_PHONE: string;
    INVALID_PAN: string;
    PASSWORD_TOO_SHORT: string;
    UNAUTHORIZED: string;
    NOT_FOUND: string;
    SERVER_ERROR: string;
    NETWORK_ERROR: string;
    VALIDATION_ERROR: string;
};
export declare const SUCCESS_MESSAGES: {
    CREATED: string;
    UPDATED: string;
    DELETED: string;
    SAVED: string;
    SUBMITTED: string;
    APPROVED: string;
    REJECTED: string;
    LOGIN_SUCCESS: string;
    LOGOUT_SUCCESS: string;
};
export declare const MENU_ITEMS: {
    label: string;
    href: string;
    icon: string;
    roles: string[];
}[];
//# sourceMappingURL=constants.d.ts.map