"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/es-object-atoms";
exports.ids = ["vendor-chunks/es-object-atoms"];
exports.modules = {

/***/ "(ssr)/../node_modules/es-object-atoms/index.js":
/*!************************************************!*\
  !*** ../node_modules/es-object-atoms/index.js ***!
  \************************************************/
/***/ ((module) => {

eval("\n/** @type {import('.')} */ module.exports = Object;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL2VzLW9iamVjdC1hdG9tcy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUVBLHdCQUF3QixHQUN4QkEsT0FBT0MsT0FBTyxHQUFHQyIsInNvdXJjZXMiOlsid2VicGFjazovL0BhZ2VudGljLXRhbGVudC1wcm8vZnJvbnRlbmQvLi4vbm9kZV9tb2R1bGVzL2VzLW9iamVjdC1hdG9tcy9pbmRleC5qcz9jNDUzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuLyoqIEB0eXBlIHtpbXBvcnQoJy4nKX0gKi9cbm1vZHVsZS5leHBvcnRzID0gT2JqZWN0O1xuIl0sIm5hbWVzIjpbIm1vZHVsZSIsImV4cG9ydHMiLCJPYmplY3QiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/es-object-atoms/index.js\n");

/***/ })

};
;