"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/d3-format";
exports.ids = ["vendor-chunks/d3-format"];
exports.modules = {

/***/ "(ssr)/../node_modules/d3-format/src/defaultLocale.js":
/*!******************************************************!*\
  !*** ../node_modules/d3-format/src/defaultLocale.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ defaultLocale),\n/* harmony export */   format: () => (/* binding */ format),\n/* harmony export */   formatPrefix: () => (/* binding */ formatPrefix)\n/* harmony export */ });\n/* harmony import */ var _locale_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./locale.js */ \"(ssr)/../node_modules/d3-format/src/locale.js\");\n\nvar locale;\nvar format;\nvar formatPrefix;\ndefaultLocale({\n    thousands: \",\",\n    grouping: [\n        3\n    ],\n    currency: [\n        \"$\",\n        \"\"\n    ]\n});\nfunction defaultLocale(definition) {\n    locale = (0,_locale_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(definition);\n    format = locale.format;\n    formatPrefix = locale.formatPrefix;\n    return locale;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL2QzLWZvcm1hdC9zcmMvZGVmYXVsdExvY2FsZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQXVDO0FBRXZDLElBQUlDO0FBQ0csSUFBSUMsT0FBTztBQUNYLElBQUlDLGFBQWE7QUFFeEJDLGNBQWM7SUFDWkMsV0FBVztJQUNYQyxVQUFVO1FBQUM7S0FBRTtJQUNiQyxVQUFVO1FBQUM7UUFBSztLQUFHO0FBQ3JCO0FBRWUsU0FBU0gsY0FBY0ksVUFBVTtJQUM5Q1AsU0FBU0Qsc0RBQVlBLENBQUNRO0lBQ3RCTixTQUFTRCxPQUFPQyxNQUFNO0lBQ3RCQyxlQUFlRixPQUFPRSxZQUFZO0lBQ2xDLE9BQU9GO0FBQ1QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AYWdlbnRpYy10YWxlbnQtcHJvL2Zyb250ZW5kLy4uL25vZGVfbW9kdWxlcy9kMy1mb3JtYXQvc3JjL2RlZmF1bHRMb2NhbGUuanM/ZDRjMSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgZm9ybWF0TG9jYWxlIGZyb20gXCIuL2xvY2FsZS5qc1wiO1xuXG52YXIgbG9jYWxlO1xuZXhwb3J0IHZhciBmb3JtYXQ7XG5leHBvcnQgdmFyIGZvcm1hdFByZWZpeDtcblxuZGVmYXVsdExvY2FsZSh7XG4gIHRob3VzYW5kczogXCIsXCIsXG4gIGdyb3VwaW5nOiBbM10sXG4gIGN1cnJlbmN5OiBbXCIkXCIsIFwiXCJdXG59KTtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gZGVmYXVsdExvY2FsZShkZWZpbml0aW9uKSB7XG4gIGxvY2FsZSA9IGZvcm1hdExvY2FsZShkZWZpbml0aW9uKTtcbiAgZm9ybWF0ID0gbG9jYWxlLmZvcm1hdDtcbiAgZm9ybWF0UHJlZml4ID0gbG9jYWxlLmZvcm1hdFByZWZpeDtcbiAgcmV0dXJuIGxvY2FsZTtcbn1cbiJdLCJuYW1lcyI6WyJmb3JtYXRMb2NhbGUiLCJsb2NhbGUiLCJmb3JtYXQiLCJmb3JtYXRQcmVmaXgiLCJkZWZhdWx0TG9jYWxlIiwidGhvdXNhbmRzIiwiZ3JvdXBpbmciLCJjdXJyZW5jeSIsImRlZmluaXRpb24iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/d3-format/src/defaultLocale.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/d3-format/src/exponent.js":
/*!*************************************************!*\
  !*** ../node_modules/d3-format/src/exponent.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _formatDecimal_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./formatDecimal.js */ \"(ssr)/../node_modules/d3-format/src/formatDecimal.js\");\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(x) {\n    return x = (0,_formatDecimal_js__WEBPACK_IMPORTED_MODULE_0__.formatDecimalParts)(Math.abs(x)), x ? x[1] : NaN;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL2QzLWZvcm1hdC9zcmMvZXhwb25lbnQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBc0Q7QUFFdEQsNkJBQWUsb0NBQVNDLENBQUM7SUFDdkIsT0FBT0EsSUFBSUQscUVBQWtCQSxDQUFDRSxLQUFLQyxHQUFHLENBQUNGLEtBQUtBLElBQUlBLENBQUMsQ0FBQyxFQUFFLEdBQUdHO0FBQ3pEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQGFnZW50aWMtdGFsZW50LXByby9mcm9udGVuZC8uLi9ub2RlX21vZHVsZXMvZDMtZm9ybWF0L3NyYy9leHBvbmVudC5qcz83OTllIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7Zm9ybWF0RGVjaW1hbFBhcnRzfSBmcm9tIFwiLi9mb3JtYXREZWNpbWFsLmpzXCI7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uKHgpIHtcbiAgcmV0dXJuIHggPSBmb3JtYXREZWNpbWFsUGFydHMoTWF0aC5hYnMoeCkpLCB4ID8geFsxXSA6IE5hTjtcbn1cbiJdLCJuYW1lcyI6WyJmb3JtYXREZWNpbWFsUGFydHMiLCJ4IiwiTWF0aCIsImFicyIsIk5hTiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/d3-format/src/exponent.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/d3-format/src/formatDecimal.js":
/*!******************************************************!*\
  !*** ../node_modules/d3-format/src/formatDecimal.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   formatDecimalParts: () => (/* binding */ formatDecimalParts)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(x) {\n    return Math.abs(x = Math.round(x)) >= 1e21 ? x.toLocaleString(\"en\").replace(/,/g, \"\") : x.toString(10);\n}\n// Computes the decimal coefficient and exponent of the specified number x with\n// significant digits p, where x is positive and p is in [1, 21] or undefined.\n// For example, formatDecimalParts(1.23) returns [\"123\", 0].\nfunction formatDecimalParts(x, p) {\n    if ((i = (x = p ? x.toExponential(p - 1) : x.toExponential()).indexOf(\"e\")) < 0) return null; // NaN, ±Infinity\n    var i, coefficient = x.slice(0, i);\n    // The string returned by toExponential either has the form \\d\\.\\d+e[-+]\\d+\n    // (e.g., 1.2e+3) or the form \\de[-+]\\d+ (e.g., 1e+3).\n    return [\n        coefficient.length > 1 ? coefficient[0] + coefficient.slice(2) : coefficient,\n        +x.slice(i + 1)\n    ];\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/d3-format/src/formatDecimal.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/d3-format/src/formatGroup.js":
/*!****************************************************!*\
  !*** ../node_modules/d3-format/src/formatGroup.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(grouping, thousands) {\n    return function(value, width) {\n        var i = value.length, t = [], j = 0, g = grouping[0], length = 0;\n        while(i > 0 && g > 0){\n            if (length + g + 1 > width) g = Math.max(1, width - length);\n            t.push(value.substring(i -= g, i + g));\n            if ((length += g + 1) > width) break;\n            g = grouping[j = (j + 1) % grouping.length];\n        }\n        return t.reverse().join(thousands);\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL2QzLWZvcm1hdC9zcmMvZm9ybWF0R3JvdXAuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLDZCQUFlLG9DQUFTQSxRQUFRLEVBQUVDLFNBQVM7SUFDekMsT0FBTyxTQUFTQyxLQUFLLEVBQUVDLEtBQUs7UUFDMUIsSUFBSUMsSUFBSUYsTUFBTUcsTUFBTSxFQUNoQkMsSUFBSSxFQUFFLEVBQ05DLElBQUksR0FDSkMsSUFBSVIsUUFBUSxDQUFDLEVBQUUsRUFDZkssU0FBUztRQUViLE1BQU9ELElBQUksS0FBS0ksSUFBSSxFQUFHO1lBQ3JCLElBQUlILFNBQVNHLElBQUksSUFBSUwsT0FBT0ssSUFBSUMsS0FBS0MsR0FBRyxDQUFDLEdBQUdQLFFBQVFFO1lBQ3BEQyxFQUFFSyxJQUFJLENBQUNULE1BQU1VLFNBQVMsQ0FBQ1IsS0FBS0ksR0FBR0osSUFBSUk7WUFDbkMsSUFBSSxDQUFDSCxVQUFVRyxJQUFJLEtBQUtMLE9BQU87WUFDL0JLLElBQUlSLFFBQVEsQ0FBQ08sSUFBSSxDQUFDQSxJQUFJLEtBQUtQLFNBQVNLLE1BQU0sQ0FBQztRQUM3QztRQUVBLE9BQU9DLEVBQUVPLE9BQU8sR0FBR0MsSUFBSSxDQUFDYjtJQUMxQjtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQGFnZW50aWMtdGFsZW50LXByby9mcm9udGVuZC8uLi9ub2RlX21vZHVsZXMvZDMtZm9ybWF0L3NyYy9mb3JtYXRHcm91cC5qcz81ZDU4Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uKGdyb3VwaW5nLCB0aG91c2FuZHMpIHtcbiAgcmV0dXJuIGZ1bmN0aW9uKHZhbHVlLCB3aWR0aCkge1xuICAgIHZhciBpID0gdmFsdWUubGVuZ3RoLFxuICAgICAgICB0ID0gW10sXG4gICAgICAgIGogPSAwLFxuICAgICAgICBnID0gZ3JvdXBpbmdbMF0sXG4gICAgICAgIGxlbmd0aCA9IDA7XG5cbiAgICB3aGlsZSAoaSA+IDAgJiYgZyA+IDApIHtcbiAgICAgIGlmIChsZW5ndGggKyBnICsgMSA+IHdpZHRoKSBnID0gTWF0aC5tYXgoMSwgd2lkdGggLSBsZW5ndGgpO1xuICAgICAgdC5wdXNoKHZhbHVlLnN1YnN0cmluZyhpIC09IGcsIGkgKyBnKSk7XG4gICAgICBpZiAoKGxlbmd0aCArPSBnICsgMSkgPiB3aWR0aCkgYnJlYWs7XG4gICAgICBnID0gZ3JvdXBpbmdbaiA9IChqICsgMSkgJSBncm91cGluZy5sZW5ndGhdO1xuICAgIH1cblxuICAgIHJldHVybiB0LnJldmVyc2UoKS5qb2luKHRob3VzYW5kcyk7XG4gIH07XG59XG4iXSwibmFtZXMiOlsiZ3JvdXBpbmciLCJ0aG91c2FuZHMiLCJ2YWx1ZSIsIndpZHRoIiwiaSIsImxlbmd0aCIsInQiLCJqIiwiZyIsIk1hdGgiLCJtYXgiLCJwdXNoIiwic3Vic3RyaW5nIiwicmV2ZXJzZSIsImpvaW4iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/d3-format/src/formatGroup.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/d3-format/src/formatNumerals.js":
/*!*******************************************************!*\
  !*** ../node_modules/d3-format/src/formatNumerals.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(numerals) {\n    return function(value) {\n        return value.replace(/[0-9]/g, function(i) {\n            return numerals[+i];\n        });\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL2QzLWZvcm1hdC9zcmMvZm9ybWF0TnVtZXJhbHMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLDZCQUFlLG9DQUFTQSxRQUFRO0lBQzlCLE9BQU8sU0FBU0MsS0FBSztRQUNuQixPQUFPQSxNQUFNQyxPQUFPLENBQUMsVUFBVSxTQUFTQyxDQUFDO1lBQ3ZDLE9BQU9ILFFBQVEsQ0FBQyxDQUFDRyxFQUFFO1FBQ3JCO0lBQ0Y7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL0BhZ2VudGljLXRhbGVudC1wcm8vZnJvbnRlbmQvLi4vbm9kZV9tb2R1bGVzL2QzLWZvcm1hdC9zcmMvZm9ybWF0TnVtZXJhbHMuanM/Mzk1ZSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBmdW5jdGlvbihudW1lcmFscykge1xuICByZXR1cm4gZnVuY3Rpb24odmFsdWUpIHtcbiAgICByZXR1cm4gdmFsdWUucmVwbGFjZSgvWzAtOV0vZywgZnVuY3Rpb24oaSkge1xuICAgICAgcmV0dXJuIG51bWVyYWxzWytpXTtcbiAgICB9KTtcbiAgfTtcbn1cbiJdLCJuYW1lcyI6WyJudW1lcmFscyIsInZhbHVlIiwicmVwbGFjZSIsImkiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/d3-format/src/formatNumerals.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/d3-format/src/formatPrefixAuto.js":
/*!*********************************************************!*\
  !*** ../node_modules/d3-format/src/formatPrefixAuto.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   prefixExponent: () => (/* binding */ prefixExponent)\n/* harmony export */ });\n/* harmony import */ var _formatDecimal_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./formatDecimal.js */ \"(ssr)/../node_modules/d3-format/src/formatDecimal.js\");\n\nvar prefixExponent;\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(x, p) {\n    var d = (0,_formatDecimal_js__WEBPACK_IMPORTED_MODULE_0__.formatDecimalParts)(x, p);\n    if (!d) return x + \"\";\n    var coefficient = d[0], exponent = d[1], i = exponent - (prefixExponent = Math.max(-8, Math.min(8, Math.floor(exponent / 3))) * 3) + 1, n = coefficient.length;\n    return i === n ? coefficient : i > n ? coefficient + new Array(i - n + 1).join(\"0\") : i > 0 ? coefficient.slice(0, i) + \".\" + coefficient.slice(i) : \"0.\" + new Array(1 - i).join(\"0\") + (0,_formatDecimal_js__WEBPACK_IMPORTED_MODULE_0__.formatDecimalParts)(x, Math.max(0, p + i - 1))[0]; // less than 1y!\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/d3-format/src/formatPrefixAuto.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/d3-format/src/formatRounded.js":
/*!******************************************************!*\
  !*** ../node_modules/d3-format/src/formatRounded.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _formatDecimal_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./formatDecimal.js */ \"(ssr)/../node_modules/d3-format/src/formatDecimal.js\");\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(x, p) {\n    var d = (0,_formatDecimal_js__WEBPACK_IMPORTED_MODULE_0__.formatDecimalParts)(x, p);\n    if (!d) return x + \"\";\n    var coefficient = d[0], exponent = d[1];\n    return exponent < 0 ? \"0.\" + new Array(-exponent).join(\"0\") + coefficient : coefficient.length > exponent + 1 ? coefficient.slice(0, exponent + 1) + \".\" + coefficient.slice(exponent + 1) : coefficient + new Array(exponent - coefficient.length + 2).join(\"0\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL2QzLWZvcm1hdC9zcmMvZm9ybWF0Um91bmRlZC5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFzRDtBQUV0RCw2QkFBZSxvQ0FBU0MsQ0FBQyxFQUFFQyxDQUFDO0lBQzFCLElBQUlDLElBQUlILHFFQUFrQkEsQ0FBQ0MsR0FBR0M7SUFDOUIsSUFBSSxDQUFDQyxHQUFHLE9BQU9GLElBQUk7SUFDbkIsSUFBSUcsY0FBY0QsQ0FBQyxDQUFDLEVBQUUsRUFDbEJFLFdBQVdGLENBQUMsQ0FBQyxFQUFFO0lBQ25CLE9BQU9FLFdBQVcsSUFBSSxPQUFPLElBQUlDLE1BQU0sQ0FBQ0QsVUFBVUUsSUFBSSxDQUFDLE9BQU9ILGNBQ3hEQSxZQUFZSSxNQUFNLEdBQUdILFdBQVcsSUFBSUQsWUFBWUssS0FBSyxDQUFDLEdBQUdKLFdBQVcsS0FBSyxNQUFNRCxZQUFZSyxLQUFLLENBQUNKLFdBQVcsS0FDNUdELGNBQWMsSUFBSUUsTUFBTUQsV0FBV0QsWUFBWUksTUFBTSxHQUFHLEdBQUdELElBQUksQ0FBQztBQUN4RSIsInNvdXJjZXMiOlsid2VicGFjazovL0BhZ2VudGljLXRhbGVudC1wcm8vZnJvbnRlbmQvLi4vbm9kZV9tb2R1bGVzL2QzLWZvcm1hdC9zcmMvZm9ybWF0Um91bmRlZC5qcz9jYjFiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7Zm9ybWF0RGVjaW1hbFBhcnRzfSBmcm9tIFwiLi9mb3JtYXREZWNpbWFsLmpzXCI7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uKHgsIHApIHtcbiAgdmFyIGQgPSBmb3JtYXREZWNpbWFsUGFydHMoeCwgcCk7XG4gIGlmICghZCkgcmV0dXJuIHggKyBcIlwiO1xuICB2YXIgY29lZmZpY2llbnQgPSBkWzBdLFxuICAgICAgZXhwb25lbnQgPSBkWzFdO1xuICByZXR1cm4gZXhwb25lbnQgPCAwID8gXCIwLlwiICsgbmV3IEFycmF5KC1leHBvbmVudCkuam9pbihcIjBcIikgKyBjb2VmZmljaWVudFxuICAgICAgOiBjb2VmZmljaWVudC5sZW5ndGggPiBleHBvbmVudCArIDEgPyBjb2VmZmljaWVudC5zbGljZSgwLCBleHBvbmVudCArIDEpICsgXCIuXCIgKyBjb2VmZmljaWVudC5zbGljZShleHBvbmVudCArIDEpXG4gICAgICA6IGNvZWZmaWNpZW50ICsgbmV3IEFycmF5KGV4cG9uZW50IC0gY29lZmZpY2llbnQubGVuZ3RoICsgMikuam9pbihcIjBcIik7XG59XG4iXSwibmFtZXMiOlsiZm9ybWF0RGVjaW1hbFBhcnRzIiwieCIsInAiLCJkIiwiY29lZmZpY2llbnQiLCJleHBvbmVudCIsIkFycmF5Iiwiam9pbiIsImxlbmd0aCIsInNsaWNlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/d3-format/src/formatRounded.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/d3-format/src/formatSpecifier.js":
/*!********************************************************!*\
  !*** ../node_modules/d3-format/src/formatSpecifier.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FormatSpecifier: () => (/* binding */ FormatSpecifier),\n/* harmony export */   \"default\": () => (/* binding */ formatSpecifier)\n/* harmony export */ });\n// [[fill]align][sign][symbol][0][width][,][.precision][~][type]\nvar re = /^(?:(.)?([<>=^]))?([+\\-( ])?([$#])?(0)?(\\d+)?(,)?(\\.\\d+)?(~)?([a-z%])?$/i;\nfunction formatSpecifier(specifier) {\n    if (!(match = re.exec(specifier))) throw new Error(\"invalid format: \" + specifier);\n    var match;\n    return new FormatSpecifier({\n        fill: match[1],\n        align: match[2],\n        sign: match[3],\n        symbol: match[4],\n        zero: match[5],\n        width: match[6],\n        comma: match[7],\n        precision: match[8] && match[8].slice(1),\n        trim: match[9],\n        type: match[10]\n    });\n}\nformatSpecifier.prototype = FormatSpecifier.prototype; // instanceof\nfunction FormatSpecifier(specifier) {\n    this.fill = specifier.fill === undefined ? \" \" : specifier.fill + \"\";\n    this.align = specifier.align === undefined ? \">\" : specifier.align + \"\";\n    this.sign = specifier.sign === undefined ? \"-\" : specifier.sign + \"\";\n    this.symbol = specifier.symbol === undefined ? \"\" : specifier.symbol + \"\";\n    this.zero = !!specifier.zero;\n    this.width = specifier.width === undefined ? undefined : +specifier.width;\n    this.comma = !!specifier.comma;\n    this.precision = specifier.precision === undefined ? undefined : +specifier.precision;\n    this.trim = !!specifier.trim;\n    this.type = specifier.type === undefined ? \"\" : specifier.type + \"\";\n}\nFormatSpecifier.prototype.toString = function() {\n    return this.fill + this.align + this.sign + this.symbol + (this.zero ? \"0\" : \"\") + (this.width === undefined ? \"\" : Math.max(1, this.width | 0)) + (this.comma ? \",\" : \"\") + (this.precision === undefined ? \"\" : \".\" + Math.max(0, this.precision | 0)) + (this.trim ? \"~\" : \"\") + this.type;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/d3-format/src/formatSpecifier.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/d3-format/src/formatTrim.js":
/*!***************************************************!*\
  !*** ../node_modules/d3-format/src/formatTrim.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n// Trims insignificant zeros, e.g., replaces 1.2000k with 1.2k.\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(s) {\n    out: for(var n = s.length, i = 1, i0 = -1, i1; i < n; ++i){\n        switch(s[i]){\n            case \".\":\n                i0 = i1 = i;\n                break;\n            case \"0\":\n                if (i0 === 0) i0 = i;\n                i1 = i;\n                break;\n            default:\n                if (!+s[i]) break out;\n                if (i0 > 0) i0 = 0;\n                break;\n        }\n    }\n    return i0 > 0 ? s.slice(0, i0) + s.slice(i1 + 1) : s;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL2QzLWZvcm1hdC9zcmMvZm9ybWF0VHJpbS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsK0RBQStEO0FBQy9ELDZCQUFlLG9DQUFTQSxDQUFDO0lBQ3ZCQyxLQUFLLElBQUssSUFBSUMsSUFBSUYsRUFBRUcsTUFBTSxFQUFFQyxJQUFJLEdBQUdDLEtBQUssQ0FBQyxHQUFHQyxJQUFJRixJQUFJRixHQUFHLEVBQUVFLEVBQUc7UUFDMUQsT0FBUUosQ0FBQyxDQUFDSSxFQUFFO1lBQ1YsS0FBSztnQkFBS0MsS0FBS0MsS0FBS0Y7Z0JBQUc7WUFDdkIsS0FBSztnQkFBSyxJQUFJQyxPQUFPLEdBQUdBLEtBQUtEO2dCQUFHRSxLQUFLRjtnQkFBRztZQUN4QztnQkFBUyxJQUFJLENBQUMsQ0FBQ0osQ0FBQyxDQUFDSSxFQUFFLEVBQUUsTUFBTUg7Z0JBQUssSUFBSUksS0FBSyxHQUFHQSxLQUFLO2dCQUFHO1FBQ3REO0lBQ0Y7SUFDQSxPQUFPQSxLQUFLLElBQUlMLEVBQUVPLEtBQUssQ0FBQyxHQUFHRixNQUFNTCxFQUFFTyxLQUFLLENBQUNELEtBQUssS0FBS047QUFDckQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AYWdlbnRpYy10YWxlbnQtcHJvL2Zyb250ZW5kLy4uL25vZGVfbW9kdWxlcy9kMy1mb3JtYXQvc3JjL2Zvcm1hdFRyaW0uanM/MGM1ZCJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBUcmltcyBpbnNpZ25pZmljYW50IHplcm9zLCBlLmcuLCByZXBsYWNlcyAxLjIwMDBrIHdpdGggMS4yay5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uKHMpIHtcbiAgb3V0OiBmb3IgKHZhciBuID0gcy5sZW5ndGgsIGkgPSAxLCBpMCA9IC0xLCBpMTsgaSA8IG47ICsraSkge1xuICAgIHN3aXRjaCAoc1tpXSkge1xuICAgICAgY2FzZSBcIi5cIjogaTAgPSBpMSA9IGk7IGJyZWFrO1xuICAgICAgY2FzZSBcIjBcIjogaWYgKGkwID09PSAwKSBpMCA9IGk7IGkxID0gaTsgYnJlYWs7XG4gICAgICBkZWZhdWx0OiBpZiAoIStzW2ldKSBicmVhayBvdXQ7IGlmIChpMCA+IDApIGkwID0gMDsgYnJlYWs7XG4gICAgfVxuICB9XG4gIHJldHVybiBpMCA+IDAgPyBzLnNsaWNlKDAsIGkwKSArIHMuc2xpY2UoaTEgKyAxKSA6IHM7XG59XG4iXSwibmFtZXMiOlsicyIsIm91dCIsIm4iLCJsZW5ndGgiLCJpIiwiaTAiLCJpMSIsInNsaWNlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/d3-format/src/formatTrim.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/d3-format/src/formatTypes.js":
/*!****************************************************!*\
  !*** ../node_modules/d3-format/src/formatTypes.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _formatDecimal_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./formatDecimal.js */ \"(ssr)/../node_modules/d3-format/src/formatDecimal.js\");\n/* harmony import */ var _formatPrefixAuto_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./formatPrefixAuto.js */ \"(ssr)/../node_modules/d3-format/src/formatPrefixAuto.js\");\n/* harmony import */ var _formatRounded_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./formatRounded.js */ \"(ssr)/../node_modules/d3-format/src/formatRounded.js\");\n\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\n    \"%\": (x, p)=>(x * 100).toFixed(p),\n    \"b\": (x)=>Math.round(x).toString(2),\n    \"c\": (x)=>x + \"\",\n    \"d\": _formatDecimal_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"],\n    \"e\": (x, p)=>x.toExponential(p),\n    \"f\": (x, p)=>x.toFixed(p),\n    \"g\": (x, p)=>x.toPrecision(p),\n    \"o\": (x)=>Math.round(x).toString(8),\n    \"p\": (x, p)=>(0,_formatRounded_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(x * 100, p),\n    \"r\": _formatRounded_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n    \"s\": _formatPrefixAuto_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n    \"X\": (x)=>Math.round(x).toString(16).toUpperCase(),\n    \"x\": (x)=>Math.round(x).toString(16)\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/d3-format/src/formatTypes.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/d3-format/src/identity.js":
/*!*************************************************!*\
  !*** ../node_modules/d3-format/src/identity.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(x) {\n    return x;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL2QzLWZvcm1hdC9zcmMvaWRlbnRpdHkuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLDZCQUFlLG9DQUFTQSxDQUFDO0lBQ3ZCLE9BQU9BO0FBQ1QiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AYWdlbnRpYy10YWxlbnQtcHJvL2Zyb250ZW5kLy4uL25vZGVfbW9kdWxlcy9kMy1mb3JtYXQvc3JjL2lkZW50aXR5LmpzP2NjODEiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24oeCkge1xuICByZXR1cm4geDtcbn1cbiJdLCJuYW1lcyI6WyJ4Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/d3-format/src/identity.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/d3-format/src/locale.js":
/*!***********************************************!*\
  !*** ../node_modules/d3-format/src/locale.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _exponent_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./exponent.js */ \"(ssr)/../node_modules/d3-format/src/exponent.js\");\n/* harmony import */ var _formatGroup_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./formatGroup.js */ \"(ssr)/../node_modules/d3-format/src/formatGroup.js\");\n/* harmony import */ var _formatNumerals_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./formatNumerals.js */ \"(ssr)/../node_modules/d3-format/src/formatNumerals.js\");\n/* harmony import */ var _formatSpecifier_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./formatSpecifier.js */ \"(ssr)/../node_modules/d3-format/src/formatSpecifier.js\");\n/* harmony import */ var _formatTrim_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./formatTrim.js */ \"(ssr)/../node_modules/d3-format/src/formatTrim.js\");\n/* harmony import */ var _formatTypes_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./formatTypes.js */ \"(ssr)/../node_modules/d3-format/src/formatTypes.js\");\n/* harmony import */ var _formatPrefixAuto_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./formatPrefixAuto.js */ \"(ssr)/../node_modules/d3-format/src/formatPrefixAuto.js\");\n/* harmony import */ var _identity_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./identity.js */ \"(ssr)/../node_modules/d3-format/src/identity.js\");\n\n\n\n\n\n\n\n\nvar map = Array.prototype.map, prefixes = [\n    \"y\",\n    \"z\",\n    \"a\",\n    \"f\",\n    \"p\",\n    \"n\",\n    \"\\xb5\",\n    \"m\",\n    \"\",\n    \"k\",\n    \"M\",\n    \"G\",\n    \"T\",\n    \"P\",\n    \"E\",\n    \"Z\",\n    \"Y\"\n];\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(locale) {\n    var group = locale.grouping === undefined || locale.thousands === undefined ? _identity_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"] : (0,_formatGroup_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(map.call(locale.grouping, Number), locale.thousands + \"\"), currencyPrefix = locale.currency === undefined ? \"\" : locale.currency[0] + \"\", currencySuffix = locale.currency === undefined ? \"\" : locale.currency[1] + \"\", decimal = locale.decimal === undefined ? \".\" : locale.decimal + \"\", numerals = locale.numerals === undefined ? _identity_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"] : (0,_formatNumerals_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(map.call(locale.numerals, String)), percent = locale.percent === undefined ? \"%\" : locale.percent + \"\", minus = locale.minus === undefined ? \"−\" : locale.minus + \"\", nan = locale.nan === undefined ? \"NaN\" : locale.nan + \"\";\n    function newFormat(specifier) {\n        specifier = (0,_formatSpecifier_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(specifier);\n        var fill = specifier.fill, align = specifier.align, sign = specifier.sign, symbol = specifier.symbol, zero = specifier.zero, width = specifier.width, comma = specifier.comma, precision = specifier.precision, trim = specifier.trim, type = specifier.type;\n        // The \"n\" type is an alias for \",g\".\n        if (type === \"n\") comma = true, type = \"g\";\n        else if (!_formatTypes_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"][type]) precision === undefined && (precision = 12), trim = true, type = \"g\";\n        // If zero fill is specified, padding goes after sign and before digits.\n        if (zero || fill === \"0\" && align === \"=\") zero = true, fill = \"0\", align = \"=\";\n        // Compute the prefix and suffix.\n        // For SI-prefix, the suffix is lazily computed.\n        var prefix = symbol === \"$\" ? currencyPrefix : symbol === \"#\" && /[boxX]/.test(type) ? \"0\" + type.toLowerCase() : \"\", suffix = symbol === \"$\" ? currencySuffix : /[%p]/.test(type) ? percent : \"\";\n        // What format function should we use?\n        // Is this an integer type?\n        // Can this type generate exponential notation?\n        var formatType = _formatTypes_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"][type], maybeSuffix = /[defgprs%]/.test(type);\n        // Set the default precision if not specified,\n        // or clamp the specified precision to the supported range.\n        // For significant precision, it must be in [1, 21].\n        // For fixed precision, it must be in [0, 20].\n        precision = precision === undefined ? 6 : /[gprs]/.test(type) ? Math.max(1, Math.min(21, precision)) : Math.max(0, Math.min(20, precision));\n        function format(value) {\n            var valuePrefix = prefix, valueSuffix = suffix, i, n, c;\n            if (type === \"c\") {\n                valueSuffix = formatType(value) + valueSuffix;\n                value = \"\";\n            } else {\n                value = +value;\n                // Determine the sign. -0 is not less than 0, but 1 / -0 is!\n                var valueNegative = value < 0 || 1 / value < 0;\n                // Perform the initial formatting.\n                value = isNaN(value) ? nan : formatType(Math.abs(value), precision);\n                // Trim insignificant zeros.\n                if (trim) value = (0,_formatTrim_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(value);\n                // If a negative value rounds to zero after formatting, and no explicit positive sign is requested, hide the sign.\n                if (valueNegative && +value === 0 && sign !== \"+\") valueNegative = false;\n                // Compute the prefix and suffix.\n                valuePrefix = (valueNegative ? sign === \"(\" ? sign : minus : sign === \"-\" || sign === \"(\" ? \"\" : sign) + valuePrefix;\n                valueSuffix = (type === \"s\" ? prefixes[8 + _formatPrefixAuto_js__WEBPACK_IMPORTED_MODULE_6__.prefixExponent / 3] : \"\") + valueSuffix + (valueNegative && sign === \"(\" ? \")\" : \"\");\n                // Break the formatted value into the integer “value” part that can be\n                // grouped, and fractional or exponential “suffix” part that is not.\n                if (maybeSuffix) {\n                    i = -1, n = value.length;\n                    while(++i < n){\n                        if (c = value.charCodeAt(i), 48 > c || c > 57) {\n                            valueSuffix = (c === 46 ? decimal + value.slice(i + 1) : value.slice(i)) + valueSuffix;\n                            value = value.slice(0, i);\n                            break;\n                        }\n                    }\n                }\n            }\n            // If the fill character is not \"0\", grouping is applied before padding.\n            if (comma && !zero) value = group(value, Infinity);\n            // Compute the padding.\n            var length = valuePrefix.length + value.length + valueSuffix.length, padding = length < width ? new Array(width - length + 1).join(fill) : \"\";\n            // If the fill character is \"0\", grouping is applied after padding.\n            if (comma && zero) value = group(padding + value, padding.length ? width - valueSuffix.length : Infinity), padding = \"\";\n            // Reconstruct the final output based on the desired alignment.\n            switch(align){\n                case \"<\":\n                    value = valuePrefix + value + valueSuffix + padding;\n                    break;\n                case \"=\":\n                    value = valuePrefix + padding + value + valueSuffix;\n                    break;\n                case \"^\":\n                    value = padding.slice(0, length = padding.length >> 1) + valuePrefix + value + valueSuffix + padding.slice(length);\n                    break;\n                default:\n                    value = padding + valuePrefix + value + valueSuffix;\n                    break;\n            }\n            return numerals(value);\n        }\n        format.toString = function() {\n            return specifier + \"\";\n        };\n        return format;\n    }\n    function formatPrefix(specifier, value) {\n        var f = newFormat((specifier = (0,_formatSpecifier_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(specifier), specifier.type = \"f\", specifier)), e = Math.max(-8, Math.min(8, Math.floor((0,_exponent_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"])(value) / 3))) * 3, k = Math.pow(10, -e), prefix = prefixes[8 + e / 3];\n        return function(value) {\n            return f(k * value) + prefix;\n        };\n    }\n    return {\n        format: newFormat,\n        formatPrefix: formatPrefix\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/d3-format/src/locale.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/d3-format/src/precisionFixed.js":
/*!*******************************************************!*\
  !*** ../node_modules/d3-format/src/precisionFixed.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _exponent_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./exponent.js */ \"(ssr)/../node_modules/d3-format/src/exponent.js\");\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(step) {\n    return Math.max(0, -(0,_exponent_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(Math.abs(step)));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL2QzLWZvcm1hdC9zcmMvcHJlY2lzaW9uRml4ZWQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBcUM7QUFFckMsNkJBQWUsb0NBQVNDLElBQUk7SUFDMUIsT0FBT0MsS0FBS0MsR0FBRyxDQUFDLEdBQUcsQ0FBQ0gsd0RBQVFBLENBQUNFLEtBQUtFLEdBQUcsQ0FBQ0g7QUFDeEMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AYWdlbnRpYy10YWxlbnQtcHJvL2Zyb250ZW5kLy4uL25vZGVfbW9kdWxlcy9kMy1mb3JtYXQvc3JjL3ByZWNpc2lvbkZpeGVkLmpzPzM3MzYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGV4cG9uZW50IGZyb20gXCIuL2V4cG9uZW50LmpzXCI7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uKHN0ZXApIHtcbiAgcmV0dXJuIE1hdGgubWF4KDAsIC1leHBvbmVudChNYXRoLmFicyhzdGVwKSkpO1xufVxuIl0sIm5hbWVzIjpbImV4cG9uZW50Iiwic3RlcCIsIk1hdGgiLCJtYXgiLCJhYnMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/d3-format/src/precisionFixed.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/d3-format/src/precisionPrefix.js":
/*!********************************************************!*\
  !*** ../node_modules/d3-format/src/precisionPrefix.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _exponent_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./exponent.js */ \"(ssr)/../node_modules/d3-format/src/exponent.js\");\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(step, value) {\n    return Math.max(0, Math.max(-8, Math.min(8, Math.floor((0,_exponent_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(value) / 3))) * 3 - (0,_exponent_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(Math.abs(step)));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL2QzLWZvcm1hdC9zcmMvcHJlY2lzaW9uUHJlZml4LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQXFDO0FBRXJDLDZCQUFlLG9DQUFTQyxJQUFJLEVBQUVDLEtBQUs7SUFDakMsT0FBT0MsS0FBS0MsR0FBRyxDQUFDLEdBQUdELEtBQUtDLEdBQUcsQ0FBQyxDQUFDLEdBQUdELEtBQUtFLEdBQUcsQ0FBQyxHQUFHRixLQUFLRyxLQUFLLENBQUNOLHdEQUFRQSxDQUFDRSxTQUFTLE9BQU8sSUFBSUYsd0RBQVFBLENBQUNHLEtBQUtJLEdBQUcsQ0FBQ047QUFDeEciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AYWdlbnRpYy10YWxlbnQtcHJvL2Zyb250ZW5kLy4uL25vZGVfbW9kdWxlcy9kMy1mb3JtYXQvc3JjL3ByZWNpc2lvblByZWZpeC5qcz8wOGRhIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBleHBvbmVudCBmcm9tIFwiLi9leHBvbmVudC5qc1wiO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbihzdGVwLCB2YWx1ZSkge1xuICByZXR1cm4gTWF0aC5tYXgoMCwgTWF0aC5tYXgoLTgsIE1hdGgubWluKDgsIE1hdGguZmxvb3IoZXhwb25lbnQodmFsdWUpIC8gMykpKSAqIDMgLSBleHBvbmVudChNYXRoLmFicyhzdGVwKSkpO1xufVxuIl0sIm5hbWVzIjpbImV4cG9uZW50Iiwic3RlcCIsInZhbHVlIiwiTWF0aCIsIm1heCIsIm1pbiIsImZsb29yIiwiYWJzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/d3-format/src/precisionPrefix.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/d3-format/src/precisionRound.js":
/*!*******************************************************!*\
  !*** ../node_modules/d3-format/src/precisionRound.js ***!
  \*******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _exponent_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./exponent.js */ \"(ssr)/../node_modules/d3-format/src/exponent.js\");\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(step, max) {\n    step = Math.abs(step), max = Math.abs(max) - step;\n    return Math.max(0, (0,_exponent_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(max) - (0,_exponent_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(step)) + 1;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL2QzLWZvcm1hdC9zcmMvcHJlY2lzaW9uUm91bmQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBcUM7QUFFckMsNkJBQWUsb0NBQVNDLElBQUksRUFBRUMsR0FBRztJQUMvQkQsT0FBT0UsS0FBS0MsR0FBRyxDQUFDSCxPQUFPQyxNQUFNQyxLQUFLQyxHQUFHLENBQUNGLE9BQU9EO0lBQzdDLE9BQU9FLEtBQUtELEdBQUcsQ0FBQyxHQUFHRix3REFBUUEsQ0FBQ0UsT0FBT0Ysd0RBQVFBLENBQUNDLFNBQVM7QUFDdkQiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AYWdlbnRpYy10YWxlbnQtcHJvL2Zyb250ZW5kLy4uL25vZGVfbW9kdWxlcy9kMy1mb3JtYXQvc3JjL3ByZWNpc2lvblJvdW5kLmpzPzM0YmEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGV4cG9uZW50IGZyb20gXCIuL2V4cG9uZW50LmpzXCI7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uKHN0ZXAsIG1heCkge1xuICBzdGVwID0gTWF0aC5hYnMoc3RlcCksIG1heCA9IE1hdGguYWJzKG1heCkgLSBzdGVwO1xuICByZXR1cm4gTWF0aC5tYXgoMCwgZXhwb25lbnQobWF4KSAtIGV4cG9uZW50KHN0ZXApKSArIDE7XG59XG4iXSwibmFtZXMiOlsiZXhwb25lbnQiLCJzdGVwIiwibWF4IiwiTWF0aCIsImFicyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/d3-format/src/precisionRound.js\n");

/***/ })

};
;