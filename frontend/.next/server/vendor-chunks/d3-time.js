"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/d3-time";
exports.ids = ["vendor-chunks/d3-time"];
exports.modules = {

/***/ "(ssr)/../node_modules/d3-time/src/day.js":
/*!******************************************!*\
  !*** ../node_modules/d3-time/src/day.js ***!
  \******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   timeDay: () => (/* binding */ timeDay),\n/* harmony export */   timeDays: () => (/* binding */ timeDays),\n/* harmony export */   unixDay: () => (/* binding */ unixDay),\n/* harmony export */   unixDays: () => (/* binding */ unixDays),\n/* harmony export */   utcDay: () => (/* binding */ utcDay),\n/* harmony export */   utcDays: () => (/* binding */ utcDays)\n/* harmony export */ });\n/* harmony import */ var _interval_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./interval.js */ \"(ssr)/../node_modules/d3-time/src/interval.js\");\n/* harmony import */ var _duration_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./duration.js */ \"(ssr)/../node_modules/d3-time/src/duration.js\");\n\n\nconst timeDay = (0,_interval_js__WEBPACK_IMPORTED_MODULE_0__.timeInterval)((date)=>date.setHours(0, 0, 0, 0), (date, step)=>date.setDate(date.getDate() + step), (start, end)=>(end - start - (end.getTimezoneOffset() - start.getTimezoneOffset()) * _duration_js__WEBPACK_IMPORTED_MODULE_1__.durationMinute) / _duration_js__WEBPACK_IMPORTED_MODULE_1__.durationDay, (date)=>date.getDate() - 1);\nconst timeDays = timeDay.range;\nconst utcDay = (0,_interval_js__WEBPACK_IMPORTED_MODULE_0__.timeInterval)((date)=>{\n    date.setUTCHours(0, 0, 0, 0);\n}, (date, step)=>{\n    date.setUTCDate(date.getUTCDate() + step);\n}, (start, end)=>{\n    return (end - start) / _duration_js__WEBPACK_IMPORTED_MODULE_1__.durationDay;\n}, (date)=>{\n    return date.getUTCDate() - 1;\n});\nconst utcDays = utcDay.range;\nconst unixDay = (0,_interval_js__WEBPACK_IMPORTED_MODULE_0__.timeInterval)((date)=>{\n    date.setUTCHours(0, 0, 0, 0);\n}, (date, step)=>{\n    date.setUTCDate(date.getUTCDate() + step);\n}, (start, end)=>{\n    return (end - start) / _duration_js__WEBPACK_IMPORTED_MODULE_1__.durationDay;\n}, (date)=>{\n    return Math.floor(date / _duration_js__WEBPACK_IMPORTED_MODULE_1__.durationDay);\n});\nconst unixDays = unixDay.range;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/d3-time/src/day.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/d3-time/src/duration.js":
/*!***********************************************!*\
  !*** ../node_modules/d3-time/src/duration.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   durationDay: () => (/* binding */ durationDay),\n/* harmony export */   durationHour: () => (/* binding */ durationHour),\n/* harmony export */   durationMinute: () => (/* binding */ durationMinute),\n/* harmony export */   durationMonth: () => (/* binding */ durationMonth),\n/* harmony export */   durationSecond: () => (/* binding */ durationSecond),\n/* harmony export */   durationWeek: () => (/* binding */ durationWeek),\n/* harmony export */   durationYear: () => (/* binding */ durationYear)\n/* harmony export */ });\nconst durationSecond = 1000;\nconst durationMinute = durationSecond * 60;\nconst durationHour = durationMinute * 60;\nconst durationDay = durationHour * 24;\nconst durationWeek = durationDay * 7;\nconst durationMonth = durationDay * 30;\nconst durationYear = durationDay * 365;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL2QzLXRpbWUvc3JjL2R1cmF0aW9uLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBTyxNQUFNQSxpQkFBaUIsS0FBSztBQUM1QixNQUFNQyxpQkFBaUJELGlCQUFpQixHQUFHO0FBQzNDLE1BQU1FLGVBQWVELGlCQUFpQixHQUFHO0FBQ3pDLE1BQU1FLGNBQWNELGVBQWUsR0FBRztBQUN0QyxNQUFNRSxlQUFlRCxjQUFjLEVBQUU7QUFDckMsTUFBTUUsZ0JBQWdCRixjQUFjLEdBQUc7QUFDdkMsTUFBTUcsZUFBZUgsY0FBYyxJQUFJIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQGFnZW50aWMtdGFsZW50LXByby9mcm9udGVuZC8uLi9ub2RlX21vZHVsZXMvZDMtdGltZS9zcmMvZHVyYXRpb24uanM/ZDA5MSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgZHVyYXRpb25TZWNvbmQgPSAxMDAwO1xuZXhwb3J0IGNvbnN0IGR1cmF0aW9uTWludXRlID0gZHVyYXRpb25TZWNvbmQgKiA2MDtcbmV4cG9ydCBjb25zdCBkdXJhdGlvbkhvdXIgPSBkdXJhdGlvbk1pbnV0ZSAqIDYwO1xuZXhwb3J0IGNvbnN0IGR1cmF0aW9uRGF5ID0gZHVyYXRpb25Ib3VyICogMjQ7XG5leHBvcnQgY29uc3QgZHVyYXRpb25XZWVrID0gZHVyYXRpb25EYXkgKiA3O1xuZXhwb3J0IGNvbnN0IGR1cmF0aW9uTW9udGggPSBkdXJhdGlvbkRheSAqIDMwO1xuZXhwb3J0IGNvbnN0IGR1cmF0aW9uWWVhciA9IGR1cmF0aW9uRGF5ICogMzY1O1xuIl0sIm5hbWVzIjpbImR1cmF0aW9uU2Vjb25kIiwiZHVyYXRpb25NaW51dGUiLCJkdXJhdGlvbkhvdXIiLCJkdXJhdGlvbkRheSIsImR1cmF0aW9uV2VlayIsImR1cmF0aW9uTW9udGgiLCJkdXJhdGlvblllYXIiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/d3-time/src/duration.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/d3-time/src/hour.js":
/*!*******************************************!*\
  !*** ../node_modules/d3-time/src/hour.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   timeHour: () => (/* binding */ timeHour),\n/* harmony export */   timeHours: () => (/* binding */ timeHours),\n/* harmony export */   utcHour: () => (/* binding */ utcHour),\n/* harmony export */   utcHours: () => (/* binding */ utcHours)\n/* harmony export */ });\n/* harmony import */ var _interval_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./interval.js */ \"(ssr)/../node_modules/d3-time/src/interval.js\");\n/* harmony import */ var _duration_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./duration.js */ \"(ssr)/../node_modules/d3-time/src/duration.js\");\n\n\nconst timeHour = (0,_interval_js__WEBPACK_IMPORTED_MODULE_0__.timeInterval)((date)=>{\n    date.setTime(date - date.getMilliseconds() - date.getSeconds() * _duration_js__WEBPACK_IMPORTED_MODULE_1__.durationSecond - date.getMinutes() * _duration_js__WEBPACK_IMPORTED_MODULE_1__.durationMinute);\n}, (date, step)=>{\n    date.setTime(+date + step * _duration_js__WEBPACK_IMPORTED_MODULE_1__.durationHour);\n}, (start, end)=>{\n    return (end - start) / _duration_js__WEBPACK_IMPORTED_MODULE_1__.durationHour;\n}, (date)=>{\n    return date.getHours();\n});\nconst timeHours = timeHour.range;\nconst utcHour = (0,_interval_js__WEBPACK_IMPORTED_MODULE_0__.timeInterval)((date)=>{\n    date.setUTCMinutes(0, 0, 0);\n}, (date, step)=>{\n    date.setTime(+date + step * _duration_js__WEBPACK_IMPORTED_MODULE_1__.durationHour);\n}, (start, end)=>{\n    return (end - start) / _duration_js__WEBPACK_IMPORTED_MODULE_1__.durationHour;\n}, (date)=>{\n    return date.getUTCHours();\n});\nconst utcHours = utcHour.range;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/d3-time/src/hour.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/d3-time/src/interval.js":
/*!***********************************************!*\
  !*** ../node_modules/d3-time/src/interval.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   timeInterval: () => (/* binding */ timeInterval)\n/* harmony export */ });\nconst t0 = new Date, t1 = new Date;\nfunction timeInterval(floori, offseti, count, field) {\n    function interval(date) {\n        return floori(date = arguments.length === 0 ? new Date : new Date(+date)), date;\n    }\n    interval.floor = (date)=>{\n        return floori(date = new Date(+date)), date;\n    };\n    interval.ceil = (date)=>{\n        return floori(date = new Date(date - 1)), offseti(date, 1), floori(date), date;\n    };\n    interval.round = (date)=>{\n        const d0 = interval(date), d1 = interval.ceil(date);\n        return date - d0 < d1 - date ? d0 : d1;\n    };\n    interval.offset = (date, step)=>{\n        return offseti(date = new Date(+date), step == null ? 1 : Math.floor(step)), date;\n    };\n    interval.range = (start, stop, step)=>{\n        const range = [];\n        start = interval.ceil(start);\n        step = step == null ? 1 : Math.floor(step);\n        if (!(start < stop) || !(step > 0)) return range; // also handles Invalid Date\n        let previous;\n        do range.push(previous = new Date(+start)), offseti(start, step), floori(start);\n        while (previous < start && start < stop);\n        return range;\n    };\n    interval.filter = (test)=>{\n        return timeInterval((date)=>{\n            if (date >= date) while(floori(date), !test(date))date.setTime(date - 1);\n        }, (date, step)=>{\n            if (date >= date) {\n                if (step < 0) while(++step <= 0){\n                    while(offseti(date, -1), !test(date)){} // eslint-disable-line no-empty\n                }\n                else while(--step >= 0){\n                    while(offseti(date, +1), !test(date)){} // eslint-disable-line no-empty\n                }\n            }\n        });\n    };\n    if (count) {\n        interval.count = (start, end)=>{\n            t0.setTime(+start), t1.setTime(+end);\n            floori(t0), floori(t1);\n            return Math.floor(count(t0, t1));\n        };\n        interval.every = (step)=>{\n            step = Math.floor(step);\n            return !isFinite(step) || !(step > 0) ? null : !(step > 1) ? interval : interval.filter(field ? (d)=>field(d) % step === 0 : (d)=>interval.count(0, d) % step === 0);\n        };\n    }\n    return interval;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/d3-time/src/interval.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/d3-time/src/millisecond.js":
/*!**************************************************!*\
  !*** ../node_modules/d3-time/src/millisecond.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   millisecond: () => (/* binding */ millisecond),\n/* harmony export */   milliseconds: () => (/* binding */ milliseconds)\n/* harmony export */ });\n/* harmony import */ var _interval_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./interval.js */ \"(ssr)/../node_modules/d3-time/src/interval.js\");\n\nconst millisecond = (0,_interval_js__WEBPACK_IMPORTED_MODULE_0__.timeInterval)(()=>{\n// noop\n}, (date, step)=>{\n    date.setTime(+date + step);\n}, (start, end)=>{\n    return end - start;\n});\n// An optimized implementation for this simple case.\nmillisecond.every = (k)=>{\n    k = Math.floor(k);\n    if (!isFinite(k) || !(k > 0)) return null;\n    if (!(k > 1)) return millisecond;\n    return (0,_interval_js__WEBPACK_IMPORTED_MODULE_0__.timeInterval)((date)=>{\n        date.setTime(Math.floor(date / k) * k);\n    }, (date, step)=>{\n        date.setTime(+date + step * k);\n    }, (start, end)=>{\n        return (end - start) / k;\n    });\n};\nconst milliseconds = millisecond.range;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/d3-time/src/millisecond.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/d3-time/src/minute.js":
/*!*********************************************!*\
  !*** ../node_modules/d3-time/src/minute.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   timeMinute: () => (/* binding */ timeMinute),\n/* harmony export */   timeMinutes: () => (/* binding */ timeMinutes),\n/* harmony export */   utcMinute: () => (/* binding */ utcMinute),\n/* harmony export */   utcMinutes: () => (/* binding */ utcMinutes)\n/* harmony export */ });\n/* harmony import */ var _interval_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./interval.js */ \"(ssr)/../node_modules/d3-time/src/interval.js\");\n/* harmony import */ var _duration_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./duration.js */ \"(ssr)/../node_modules/d3-time/src/duration.js\");\n\n\nconst timeMinute = (0,_interval_js__WEBPACK_IMPORTED_MODULE_0__.timeInterval)((date)=>{\n    date.setTime(date - date.getMilliseconds() - date.getSeconds() * _duration_js__WEBPACK_IMPORTED_MODULE_1__.durationSecond);\n}, (date, step)=>{\n    date.setTime(+date + step * _duration_js__WEBPACK_IMPORTED_MODULE_1__.durationMinute);\n}, (start, end)=>{\n    return (end - start) / _duration_js__WEBPACK_IMPORTED_MODULE_1__.durationMinute;\n}, (date)=>{\n    return date.getMinutes();\n});\nconst timeMinutes = timeMinute.range;\nconst utcMinute = (0,_interval_js__WEBPACK_IMPORTED_MODULE_0__.timeInterval)((date)=>{\n    date.setUTCSeconds(0, 0);\n}, (date, step)=>{\n    date.setTime(+date + step * _duration_js__WEBPACK_IMPORTED_MODULE_1__.durationMinute);\n}, (start, end)=>{\n    return (end - start) / _duration_js__WEBPACK_IMPORTED_MODULE_1__.durationMinute;\n}, (date)=>{\n    return date.getUTCMinutes();\n});\nconst utcMinutes = utcMinute.range;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/d3-time/src/minute.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/d3-time/src/month.js":
/*!********************************************!*\
  !*** ../node_modules/d3-time/src/month.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   timeMonth: () => (/* binding */ timeMonth),\n/* harmony export */   timeMonths: () => (/* binding */ timeMonths),\n/* harmony export */   utcMonth: () => (/* binding */ utcMonth),\n/* harmony export */   utcMonths: () => (/* binding */ utcMonths)\n/* harmony export */ });\n/* harmony import */ var _interval_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./interval.js */ \"(ssr)/../node_modules/d3-time/src/interval.js\");\n\nconst timeMonth = (0,_interval_js__WEBPACK_IMPORTED_MODULE_0__.timeInterval)((date)=>{\n    date.setDate(1);\n    date.setHours(0, 0, 0, 0);\n}, (date, step)=>{\n    date.setMonth(date.getMonth() + step);\n}, (start, end)=>{\n    return end.getMonth() - start.getMonth() + (end.getFullYear() - start.getFullYear()) * 12;\n}, (date)=>{\n    return date.getMonth();\n});\nconst timeMonths = timeMonth.range;\nconst utcMonth = (0,_interval_js__WEBPACK_IMPORTED_MODULE_0__.timeInterval)((date)=>{\n    date.setUTCDate(1);\n    date.setUTCHours(0, 0, 0, 0);\n}, (date, step)=>{\n    date.setUTCMonth(date.getUTCMonth() + step);\n}, (start, end)=>{\n    return end.getUTCMonth() - start.getUTCMonth() + (end.getUTCFullYear() - start.getUTCFullYear()) * 12;\n}, (date)=>{\n    return date.getUTCMonth();\n});\nconst utcMonths = utcMonth.range;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/d3-time/src/month.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/d3-time/src/second.js":
/*!*********************************************!*\
  !*** ../node_modules/d3-time/src/second.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   second: () => (/* binding */ second),\n/* harmony export */   seconds: () => (/* binding */ seconds)\n/* harmony export */ });\n/* harmony import */ var _interval_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./interval.js */ \"(ssr)/../node_modules/d3-time/src/interval.js\");\n/* harmony import */ var _duration_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./duration.js */ \"(ssr)/../node_modules/d3-time/src/duration.js\");\n\n\nconst second = (0,_interval_js__WEBPACK_IMPORTED_MODULE_0__.timeInterval)((date)=>{\n    date.setTime(date - date.getMilliseconds());\n}, (date, step)=>{\n    date.setTime(+date + step * _duration_js__WEBPACK_IMPORTED_MODULE_1__.durationSecond);\n}, (start, end)=>{\n    return (end - start) / _duration_js__WEBPACK_IMPORTED_MODULE_1__.durationSecond;\n}, (date)=>{\n    return date.getUTCSeconds();\n});\nconst seconds = second.range;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL2QzLXRpbWUvc3JjL3NlY29uZC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQTJDO0FBQ0U7QUFFdEMsTUFBTUUsU0FBU0YsMERBQVlBLENBQUMsQ0FBQ0c7SUFDbENBLEtBQUtDLE9BQU8sQ0FBQ0QsT0FBT0EsS0FBS0UsZUFBZTtBQUMxQyxHQUFHLENBQUNGLE1BQU1HO0lBQ1JILEtBQUtDLE9BQU8sQ0FBQyxDQUFDRCxPQUFPRyxPQUFPTCx3REFBY0E7QUFDNUMsR0FBRyxDQUFDTSxPQUFPQztJQUNULE9BQU8sQ0FBQ0EsTUFBTUQsS0FBSSxJQUFLTix3REFBY0E7QUFDdkMsR0FBRyxDQUFDRTtJQUNGLE9BQU9BLEtBQUtNLGFBQWE7QUFDM0IsR0FBRztBQUVJLE1BQU1DLFVBQVVSLE9BQU9TLEtBQUssQ0FBQyIsInNvdXJjZXMiOlsid2VicGFjazovL0BhZ2VudGljLXRhbGVudC1wcm8vZnJvbnRlbmQvLi4vbm9kZV9tb2R1bGVzL2QzLXRpbWUvc3JjL3NlY29uZC5qcz84NzJjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7dGltZUludGVydmFsfSBmcm9tIFwiLi9pbnRlcnZhbC5qc1wiO1xuaW1wb3J0IHtkdXJhdGlvblNlY29uZH0gZnJvbSBcIi4vZHVyYXRpb24uanNcIjtcblxuZXhwb3J0IGNvbnN0IHNlY29uZCA9IHRpbWVJbnRlcnZhbCgoZGF0ZSkgPT4ge1xuICBkYXRlLnNldFRpbWUoZGF0ZSAtIGRhdGUuZ2V0TWlsbGlzZWNvbmRzKCkpO1xufSwgKGRhdGUsIHN0ZXApID0+IHtcbiAgZGF0ZS5zZXRUaW1lKCtkYXRlICsgc3RlcCAqIGR1cmF0aW9uU2Vjb25kKTtcbn0sIChzdGFydCwgZW5kKSA9PiB7XG4gIHJldHVybiAoZW5kIC0gc3RhcnQpIC8gZHVyYXRpb25TZWNvbmQ7XG59LCAoZGF0ZSkgPT4ge1xuICByZXR1cm4gZGF0ZS5nZXRVVENTZWNvbmRzKCk7XG59KTtcblxuZXhwb3J0IGNvbnN0IHNlY29uZHMgPSBzZWNvbmQucmFuZ2U7XG4iXSwibmFtZXMiOlsidGltZUludGVydmFsIiwiZHVyYXRpb25TZWNvbmQiLCJzZWNvbmQiLCJkYXRlIiwic2V0VGltZSIsImdldE1pbGxpc2Vjb25kcyIsInN0ZXAiLCJzdGFydCIsImVuZCIsImdldFVUQ1NlY29uZHMiLCJzZWNvbmRzIiwicmFuZ2UiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/d3-time/src/second.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/d3-time/src/ticks.js":
/*!********************************************!*\
  !*** ../node_modules/d3-time/src/ticks.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   timeTickInterval: () => (/* binding */ timeTickInterval),\n/* harmony export */   timeTicks: () => (/* binding */ timeTicks),\n/* harmony export */   utcTickInterval: () => (/* binding */ utcTickInterval),\n/* harmony export */   utcTicks: () => (/* binding */ utcTicks)\n/* harmony export */ });\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! d3-array */ \"(ssr)/../node_modules/d3-array/src/bisector.js\");\n/* harmony import */ var d3_array__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! d3-array */ \"(ssr)/../node_modules/d3-array/src/ticks.js\");\n/* harmony import */ var _duration_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./duration.js */ \"(ssr)/../node_modules/d3-time/src/duration.js\");\n/* harmony import */ var _millisecond_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./millisecond.js */ \"(ssr)/../node_modules/d3-time/src/millisecond.js\");\n/* harmony import */ var _second_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./second.js */ \"(ssr)/../node_modules/d3-time/src/second.js\");\n/* harmony import */ var _minute_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./minute.js */ \"(ssr)/../node_modules/d3-time/src/minute.js\");\n/* harmony import */ var _hour_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./hour.js */ \"(ssr)/../node_modules/d3-time/src/hour.js\");\n/* harmony import */ var _day_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./day.js */ \"(ssr)/../node_modules/d3-time/src/day.js\");\n/* harmony import */ var _week_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./week.js */ \"(ssr)/../node_modules/d3-time/src/week.js\");\n/* harmony import */ var _month_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./month.js */ \"(ssr)/../node_modules/d3-time/src/month.js\");\n/* harmony import */ var _year_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./year.js */ \"(ssr)/../node_modules/d3-time/src/year.js\");\n\n\n\n\n\n\n\n\n\n\nfunction ticker(year, month, week, day, hour, minute) {\n    const tickIntervals = [\n        [\n            _second_js__WEBPACK_IMPORTED_MODULE_0__.second,\n            1,\n            _duration_js__WEBPACK_IMPORTED_MODULE_1__.durationSecond\n        ],\n        [\n            _second_js__WEBPACK_IMPORTED_MODULE_0__.second,\n            5,\n            5 * _duration_js__WEBPACK_IMPORTED_MODULE_1__.durationSecond\n        ],\n        [\n            _second_js__WEBPACK_IMPORTED_MODULE_0__.second,\n            15,\n            15 * _duration_js__WEBPACK_IMPORTED_MODULE_1__.durationSecond\n        ],\n        [\n            _second_js__WEBPACK_IMPORTED_MODULE_0__.second,\n            30,\n            30 * _duration_js__WEBPACK_IMPORTED_MODULE_1__.durationSecond\n        ],\n        [\n            minute,\n            1,\n            _duration_js__WEBPACK_IMPORTED_MODULE_1__.durationMinute\n        ],\n        [\n            minute,\n            5,\n            5 * _duration_js__WEBPACK_IMPORTED_MODULE_1__.durationMinute\n        ],\n        [\n            minute,\n            15,\n            15 * _duration_js__WEBPACK_IMPORTED_MODULE_1__.durationMinute\n        ],\n        [\n            minute,\n            30,\n            30 * _duration_js__WEBPACK_IMPORTED_MODULE_1__.durationMinute\n        ],\n        [\n            hour,\n            1,\n            _duration_js__WEBPACK_IMPORTED_MODULE_1__.durationHour\n        ],\n        [\n            hour,\n            3,\n            3 * _duration_js__WEBPACK_IMPORTED_MODULE_1__.durationHour\n        ],\n        [\n            hour,\n            6,\n            6 * _duration_js__WEBPACK_IMPORTED_MODULE_1__.durationHour\n        ],\n        [\n            hour,\n            12,\n            12 * _duration_js__WEBPACK_IMPORTED_MODULE_1__.durationHour\n        ],\n        [\n            day,\n            1,\n            _duration_js__WEBPACK_IMPORTED_MODULE_1__.durationDay\n        ],\n        [\n            day,\n            2,\n            2 * _duration_js__WEBPACK_IMPORTED_MODULE_1__.durationDay\n        ],\n        [\n            week,\n            1,\n            _duration_js__WEBPACK_IMPORTED_MODULE_1__.durationWeek\n        ],\n        [\n            month,\n            1,\n            _duration_js__WEBPACK_IMPORTED_MODULE_1__.durationMonth\n        ],\n        [\n            month,\n            3,\n            3 * _duration_js__WEBPACK_IMPORTED_MODULE_1__.durationMonth\n        ],\n        [\n            year,\n            1,\n            _duration_js__WEBPACK_IMPORTED_MODULE_1__.durationYear\n        ]\n    ];\n    function ticks(start, stop, count) {\n        const reverse = stop < start;\n        if (reverse) [start, stop] = [\n            stop,\n            start\n        ];\n        const interval = count && typeof count.range === \"function\" ? count : tickInterval(start, stop, count);\n        const ticks = interval ? interval.range(start, +stop + 1) : []; // inclusive stop\n        return reverse ? ticks.reverse() : ticks;\n    }\n    function tickInterval(start, stop, count) {\n        const target = Math.abs(stop - start) / count;\n        const i = (0,d3_array__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(([, , step])=>step).right(tickIntervals, target);\n        if (i === tickIntervals.length) return year.every((0,d3_array__WEBPACK_IMPORTED_MODULE_3__.tickStep)(start / _duration_js__WEBPACK_IMPORTED_MODULE_1__.durationYear, stop / _duration_js__WEBPACK_IMPORTED_MODULE_1__.durationYear, count));\n        if (i === 0) return _millisecond_js__WEBPACK_IMPORTED_MODULE_4__.millisecond.every(Math.max((0,d3_array__WEBPACK_IMPORTED_MODULE_3__.tickStep)(start, stop, count), 1));\n        const [t, step] = tickIntervals[target / tickIntervals[i - 1][2] < tickIntervals[i][2] / target ? i - 1 : i];\n        return t.every(step);\n    }\n    return [\n        ticks,\n        tickInterval\n    ];\n}\nconst [utcTicks, utcTickInterval] = ticker(_year_js__WEBPACK_IMPORTED_MODULE_5__.utcYear, _month_js__WEBPACK_IMPORTED_MODULE_6__.utcMonth, _week_js__WEBPACK_IMPORTED_MODULE_7__.utcSunday, _day_js__WEBPACK_IMPORTED_MODULE_8__.unixDay, _hour_js__WEBPACK_IMPORTED_MODULE_9__.utcHour, _minute_js__WEBPACK_IMPORTED_MODULE_10__.utcMinute);\nconst [timeTicks, timeTickInterval] = ticker(_year_js__WEBPACK_IMPORTED_MODULE_5__.timeYear, _month_js__WEBPACK_IMPORTED_MODULE_6__.timeMonth, _week_js__WEBPACK_IMPORTED_MODULE_7__.timeSunday, _day_js__WEBPACK_IMPORTED_MODULE_8__.timeDay, _hour_js__WEBPACK_IMPORTED_MODULE_9__.timeHour, _minute_js__WEBPACK_IMPORTED_MODULE_10__.timeMinute);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/d3-time/src/ticks.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/d3-time/src/week.js":
/*!*******************************************!*\
  !*** ../node_modules/d3-time/src/week.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   timeFriday: () => (/* binding */ timeFriday),\n/* harmony export */   timeFridays: () => (/* binding */ timeFridays),\n/* harmony export */   timeMonday: () => (/* binding */ timeMonday),\n/* harmony export */   timeMondays: () => (/* binding */ timeMondays),\n/* harmony export */   timeSaturday: () => (/* binding */ timeSaturday),\n/* harmony export */   timeSaturdays: () => (/* binding */ timeSaturdays),\n/* harmony export */   timeSunday: () => (/* binding */ timeSunday),\n/* harmony export */   timeSundays: () => (/* binding */ timeSundays),\n/* harmony export */   timeThursday: () => (/* binding */ timeThursday),\n/* harmony export */   timeThursdays: () => (/* binding */ timeThursdays),\n/* harmony export */   timeTuesday: () => (/* binding */ timeTuesday),\n/* harmony export */   timeTuesdays: () => (/* binding */ timeTuesdays),\n/* harmony export */   timeWednesday: () => (/* binding */ timeWednesday),\n/* harmony export */   timeWednesdays: () => (/* binding */ timeWednesdays),\n/* harmony export */   utcFriday: () => (/* binding */ utcFriday),\n/* harmony export */   utcFridays: () => (/* binding */ utcFridays),\n/* harmony export */   utcMonday: () => (/* binding */ utcMonday),\n/* harmony export */   utcMondays: () => (/* binding */ utcMondays),\n/* harmony export */   utcSaturday: () => (/* binding */ utcSaturday),\n/* harmony export */   utcSaturdays: () => (/* binding */ utcSaturdays),\n/* harmony export */   utcSunday: () => (/* binding */ utcSunday),\n/* harmony export */   utcSundays: () => (/* binding */ utcSundays),\n/* harmony export */   utcThursday: () => (/* binding */ utcThursday),\n/* harmony export */   utcThursdays: () => (/* binding */ utcThursdays),\n/* harmony export */   utcTuesday: () => (/* binding */ utcTuesday),\n/* harmony export */   utcTuesdays: () => (/* binding */ utcTuesdays),\n/* harmony export */   utcWednesday: () => (/* binding */ utcWednesday),\n/* harmony export */   utcWednesdays: () => (/* binding */ utcWednesdays)\n/* harmony export */ });\n/* harmony import */ var _interval_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./interval.js */ \"(ssr)/../node_modules/d3-time/src/interval.js\");\n/* harmony import */ var _duration_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./duration.js */ \"(ssr)/../node_modules/d3-time/src/duration.js\");\n\n\nfunction timeWeekday(i) {\n    return (0,_interval_js__WEBPACK_IMPORTED_MODULE_0__.timeInterval)((date)=>{\n        date.setDate(date.getDate() - (date.getDay() + 7 - i) % 7);\n        date.setHours(0, 0, 0, 0);\n    }, (date, step)=>{\n        date.setDate(date.getDate() + step * 7);\n    }, (start, end)=>{\n        return (end - start - (end.getTimezoneOffset() - start.getTimezoneOffset()) * _duration_js__WEBPACK_IMPORTED_MODULE_1__.durationMinute) / _duration_js__WEBPACK_IMPORTED_MODULE_1__.durationWeek;\n    });\n}\nconst timeSunday = timeWeekday(0);\nconst timeMonday = timeWeekday(1);\nconst timeTuesday = timeWeekday(2);\nconst timeWednesday = timeWeekday(3);\nconst timeThursday = timeWeekday(4);\nconst timeFriday = timeWeekday(5);\nconst timeSaturday = timeWeekday(6);\nconst timeSundays = timeSunday.range;\nconst timeMondays = timeMonday.range;\nconst timeTuesdays = timeTuesday.range;\nconst timeWednesdays = timeWednesday.range;\nconst timeThursdays = timeThursday.range;\nconst timeFridays = timeFriday.range;\nconst timeSaturdays = timeSaturday.range;\nfunction utcWeekday(i) {\n    return (0,_interval_js__WEBPACK_IMPORTED_MODULE_0__.timeInterval)((date)=>{\n        date.setUTCDate(date.getUTCDate() - (date.getUTCDay() + 7 - i) % 7);\n        date.setUTCHours(0, 0, 0, 0);\n    }, (date, step)=>{\n        date.setUTCDate(date.getUTCDate() + step * 7);\n    }, (start, end)=>{\n        return (end - start) / _duration_js__WEBPACK_IMPORTED_MODULE_1__.durationWeek;\n    });\n}\nconst utcSunday = utcWeekday(0);\nconst utcMonday = utcWeekday(1);\nconst utcTuesday = utcWeekday(2);\nconst utcWednesday = utcWeekday(3);\nconst utcThursday = utcWeekday(4);\nconst utcFriday = utcWeekday(5);\nconst utcSaturday = utcWeekday(6);\nconst utcSundays = utcSunday.range;\nconst utcMondays = utcMonday.range;\nconst utcTuesdays = utcTuesday.range;\nconst utcWednesdays = utcWednesday.range;\nconst utcThursdays = utcThursday.range;\nconst utcFridays = utcFriday.range;\nconst utcSaturdays = utcSaturday.range;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/d3-time/src/week.js\n");

/***/ }),

/***/ "(ssr)/../node_modules/d3-time/src/year.js":
/*!*******************************************!*\
  !*** ../node_modules/d3-time/src/year.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   timeYear: () => (/* binding */ timeYear),\n/* harmony export */   timeYears: () => (/* binding */ timeYears),\n/* harmony export */   utcYear: () => (/* binding */ utcYear),\n/* harmony export */   utcYears: () => (/* binding */ utcYears)\n/* harmony export */ });\n/* harmony import */ var _interval_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./interval.js */ \"(ssr)/../node_modules/d3-time/src/interval.js\");\n\nconst timeYear = (0,_interval_js__WEBPACK_IMPORTED_MODULE_0__.timeInterval)((date)=>{\n    date.setMonth(0, 1);\n    date.setHours(0, 0, 0, 0);\n}, (date, step)=>{\n    date.setFullYear(date.getFullYear() + step);\n}, (start, end)=>{\n    return end.getFullYear() - start.getFullYear();\n}, (date)=>{\n    return date.getFullYear();\n});\n// An optimized implementation for this simple case.\ntimeYear.every = (k)=>{\n    return !isFinite(k = Math.floor(k)) || !(k > 0) ? null : (0,_interval_js__WEBPACK_IMPORTED_MODULE_0__.timeInterval)((date)=>{\n        date.setFullYear(Math.floor(date.getFullYear() / k) * k);\n        date.setMonth(0, 1);\n        date.setHours(0, 0, 0, 0);\n    }, (date, step)=>{\n        date.setFullYear(date.getFullYear() + step * k);\n    });\n};\nconst timeYears = timeYear.range;\nconst utcYear = (0,_interval_js__WEBPACK_IMPORTED_MODULE_0__.timeInterval)((date)=>{\n    date.setUTCMonth(0, 1);\n    date.setUTCHours(0, 0, 0, 0);\n}, (date, step)=>{\n    date.setUTCFullYear(date.getUTCFullYear() + step);\n}, (start, end)=>{\n    return end.getUTCFullYear() - start.getUTCFullYear();\n}, (date)=>{\n    return date.getUTCFullYear();\n});\n// An optimized implementation for this simple case.\nutcYear.every = (k)=>{\n    return !isFinite(k = Math.floor(k)) || !(k > 0) ? null : (0,_interval_js__WEBPACK_IMPORTED_MODULE_0__.timeInterval)((date)=>{\n        date.setUTCFullYear(Math.floor(date.getUTCFullYear() / k) * k);\n        date.setUTCMonth(0, 1);\n        date.setUTCHours(0, 0, 0, 0);\n    }, (date, step)=>{\n        date.setUTCFullYear(date.getUTCFullYear() + step * k);\n    });\n};\nconst utcYears = utcYear.range;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/d3-time/src/year.js\n");

/***/ })

};
;