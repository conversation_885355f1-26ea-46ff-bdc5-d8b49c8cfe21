"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/decimal.js-light";
exports.ids = ["vendor-chunks/decimal.js-light"];
exports.modules = {

/***/ "(ssr)/../node_modules/decimal.js-light/decimal.mjs":
/*!****************************************************!*\
  !*** ../node_modules/decimal.js-light/decimal.mjs ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Decimal: () => (/* binding */ Decimal),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/*\r\n *  decimal.js-light v2.5.1\r\n *  An arbitrary-precision Decimal type for JavaScript.\r\n *  https://github.com/MikeMcl/decimal.js-light\r\n *  Copyright (c) 2020 Michael Mclaughlin <<EMAIL>>\r\n *  MIT Expat Licence\r\n */ // ------------------------------------  EDITABLE DEFAULTS  ------------------------------------- //\n// The limit on the value of `precision`, and on the value of the first argument to\n// `toDecimalPlaces`, `toExponential`, `toFixed`, `toPrecision` and `toSignificantDigits`.\nvar MAX_DIGITS = 1e9, // The initial configuration properties of the Decimal constructor.\ndefaults = {\n    // These values must be integers within the stated ranges (inclusive).\n    // Most of these values can be changed during run-time using `Decimal.config`.\n    // The maximum number of significant digits of the result of a calculation or base conversion.\n    // E.g. `Decimal.config({ precision: 20 });`\n    precision: 20,\n    // The rounding mode used by default by `toInteger`, `toDecimalPlaces`, `toExponential`,\n    // `toFixed`, `toPrecision` and `toSignificantDigits`.\n    //\n    // ROUND_UP         0 Away from zero.\n    // ROUND_DOWN       1 Towards zero.\n    // ROUND_CEIL       2 Towards +Infinity.\n    // ROUND_FLOOR      3 Towards -Infinity.\n    // ROUND_HALF_UP    4 Towards nearest neighbour. If equidistant, up.\n    // ROUND_HALF_DOWN  5 Towards nearest neighbour. If equidistant, down.\n    // ROUND_HALF_EVEN  6 Towards nearest neighbour. If equidistant, towards even neighbour.\n    // ROUND_HALF_CEIL  7 Towards nearest neighbour. If equidistant, towards +Infinity.\n    // ROUND_HALF_FLOOR 8 Towards nearest neighbour. If equidistant, towards -Infinity.\n    //\n    // E.g.\n    // `Decimal.rounding = 4;`\n    // `Decimal.rounding = Decimal.ROUND_HALF_UP;`\n    rounding: 4,\n    // The exponent value at and beneath which `toString` returns exponential notation.\n    // JavaScript numbers: -7\n    toExpNeg: -7,\n    // The exponent value at and above which `toString` returns exponential notation.\n    // JavaScript numbers: 21\n    toExpPos: 21,\n    // The natural logarithm of 10.\n    // 115 digits\n    LN10: \"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286\"\n}, // ------------------------------------ END OF EDITABLE DEFAULTS -------------------------------- //\nDecimal, external = true, decimalError = \"[DecimalError] \", invalidArgument = decimalError + \"Invalid argument: \", exponentOutOfRange = decimalError + \"Exponent out of range: \", mathfloor = Math.floor, mathpow = Math.pow, isDecimal = /^(\\d+(\\.\\d*)?|\\.\\d+)(e[+-]?\\d+)?$/i, ONE, BASE = 1e7, LOG_BASE = 7, MAX_SAFE_INTEGER = 9007199254740991, MAX_E = mathfloor(MAX_SAFE_INTEGER / LOG_BASE), // Decimal.prototype object\nP = {};\n// Decimal prototype methods\n/*\r\n *  absoluteValue                       abs\r\n *  comparedTo                          cmp\r\n *  decimalPlaces                       dp\r\n *  dividedBy                           div\r\n *  dividedToIntegerBy                  idiv\r\n *  equals                              eq\r\n *  exponent\r\n *  greaterThan                         gt\r\n *  greaterThanOrEqualTo                gte\r\n *  isInteger                           isint\r\n *  isNegative                          isneg\r\n *  isPositive                          ispos\r\n *  isZero\r\n *  lessThan                            lt\r\n *  lessThanOrEqualTo                   lte\r\n *  logarithm                           log\r\n *  minus                               sub\r\n *  modulo                              mod\r\n *  naturalExponential                  exp\r\n *  naturalLogarithm                    ln\r\n *  negated                             neg\r\n *  plus                                add\r\n *  precision                           sd\r\n *  squareRoot                          sqrt\r\n *  times                               mul\r\n *  toDecimalPlaces                     todp\r\n *  toExponential\r\n *  toFixed\r\n *  toInteger                           toint\r\n *  toNumber\r\n *  toPower                             pow\r\n *  toPrecision\r\n *  toSignificantDigits                 tosd\r\n *  toString\r\n *  valueOf                             val\r\n */ /*\r\n * Return a new Decimal whose value is the absolute value of this Decimal.\r\n *\r\n */ P.absoluteValue = P.abs = function() {\n    var x = new this.constructor(this);\n    if (x.s) x.s = 1;\n    return x;\n};\n/*\r\n * Return\r\n *   1    if the value of this Decimal is greater than the value of `y`,\r\n *  -1    if the value of this Decimal is less than the value of `y`,\r\n *   0    if they have the same value\r\n *\r\n */ P.comparedTo = P.cmp = function(y) {\n    var i, j, xdL, ydL, x = this;\n    y = new x.constructor(y);\n    // Signs differ?\n    if (x.s !== y.s) return x.s || -y.s;\n    // Compare exponents.\n    if (x.e !== y.e) return x.e > y.e ^ x.s < 0 ? 1 : -1;\n    xdL = x.d.length;\n    ydL = y.d.length;\n    // Compare digit by digit.\n    for(i = 0, j = xdL < ydL ? xdL : ydL; i < j; ++i){\n        if (x.d[i] !== y.d[i]) return x.d[i] > y.d[i] ^ x.s < 0 ? 1 : -1;\n    }\n    // Compare lengths.\n    return xdL === ydL ? 0 : xdL > ydL ^ x.s < 0 ? 1 : -1;\n};\n/*\r\n * Return the number of decimal places of the value of this Decimal.\r\n *\r\n */ P.decimalPlaces = P.dp = function() {\n    var x = this, w = x.d.length - 1, dp = (w - x.e) * LOG_BASE;\n    // Subtract the number of trailing zeros of the last word.\n    w = x.d[w];\n    if (w) for(; w % 10 == 0; w /= 10)dp--;\n    return dp < 0 ? 0 : dp;\n};\n/*\r\n * Return a new Decimal whose value is the value of this Decimal divided by `y`, truncated to\r\n * `precision` significant digits.\r\n *\r\n */ P.dividedBy = P.div = function(y) {\n    return divide(this, new this.constructor(y));\n};\n/*\r\n * Return a new Decimal whose value is the integer part of dividing the value of this Decimal\r\n * by the value of `y`, truncated to `precision` significant digits.\r\n *\r\n */ P.dividedToIntegerBy = P.idiv = function(y) {\n    var x = this, Ctor = x.constructor;\n    return round(divide(x, new Ctor(y), 0, 1), Ctor.precision);\n};\n/*\r\n * Return true if the value of this Decimal is equal to the value of `y`, otherwise return false.\r\n *\r\n */ P.equals = P.eq = function(y) {\n    return !this.cmp(y);\n};\n/*\r\n * Return the (base 10) exponent value of this Decimal (this.e is the base 10000000 exponent).\r\n *\r\n */ P.exponent = function() {\n    return getBase10Exponent(this);\n};\n/*\r\n * Return true if the value of this Decimal is greater than the value of `y`, otherwise return\r\n * false.\r\n *\r\n */ P.greaterThan = P.gt = function(y) {\n    return this.cmp(y) > 0;\n};\n/*\r\n * Return true if the value of this Decimal is greater than or equal to the value of `y`,\r\n * otherwise return false.\r\n *\r\n */ P.greaterThanOrEqualTo = P.gte = function(y) {\n    return this.cmp(y) >= 0;\n};\n/*\r\n * Return true if the value of this Decimal is an integer, otherwise return false.\r\n *\r\n */ P.isInteger = P.isint = function() {\n    return this.e > this.d.length - 2;\n};\n/*\r\n * Return true if the value of this Decimal is negative, otherwise return false.\r\n *\r\n */ P.isNegative = P.isneg = function() {\n    return this.s < 0;\n};\n/*\r\n * Return true if the value of this Decimal is positive, otherwise return false.\r\n *\r\n */ P.isPositive = P.ispos = function() {\n    return this.s > 0;\n};\n/*\r\n * Return true if the value of this Decimal is 0, otherwise return false.\r\n *\r\n */ P.isZero = function() {\n    return this.s === 0;\n};\n/*\r\n * Return true if the value of this Decimal is less than `y`, otherwise return false.\r\n *\r\n */ P.lessThan = P.lt = function(y) {\n    return this.cmp(y) < 0;\n};\n/*\r\n * Return true if the value of this Decimal is less than or equal to `y`, otherwise return false.\r\n *\r\n */ P.lessThanOrEqualTo = P.lte = function(y) {\n    return this.cmp(y) < 1;\n};\n/*\r\n * Return the logarithm of the value of this Decimal to the specified base, truncated to\r\n * `precision` significant digits.\r\n *\r\n * If no base is specified, return log[10](x).\r\n *\r\n * log[base](x) = ln(x) / ln(base)\r\n *\r\n * The maximum error of the result is 1 ulp (unit in the last place).\r\n *\r\n * [base] {number|string|Decimal} The base of the logarithm.\r\n *\r\n */ P.logarithm = P.log = function(base) {\n    var r, x = this, Ctor = x.constructor, pr = Ctor.precision, wpr = pr + 5;\n    // Default base is 10.\n    if (base === void 0) {\n        base = new Ctor(10);\n    } else {\n        base = new Ctor(base);\n        // log[-b](x) = NaN\n        // log[0](x)  = NaN\n        // log[1](x)  = NaN\n        if (base.s < 1 || base.eq(ONE)) throw Error(decimalError + \"NaN\");\n    }\n    // log[b](-x) = NaN\n    // log[b](0) = -Infinity\n    if (x.s < 1) throw Error(decimalError + (x.s ? \"NaN\" : \"-Infinity\"));\n    // log[b](1) = 0\n    if (x.eq(ONE)) return new Ctor(0);\n    external = false;\n    r = divide(ln(x, wpr), ln(base, wpr), wpr);\n    external = true;\n    return round(r, pr);\n};\n/*\r\n * Return a new Decimal whose value is the value of this Decimal minus `y`, truncated to\r\n * `precision` significant digits.\r\n *\r\n */ P.minus = P.sub = function(y) {\n    var x = this;\n    y = new x.constructor(y);\n    return x.s == y.s ? subtract(x, y) : add(x, (y.s = -y.s, y));\n};\n/*\r\n * Return a new Decimal whose value is the value of this Decimal modulo `y`, truncated to\r\n * `precision` significant digits.\r\n *\r\n */ P.modulo = P.mod = function(y) {\n    var q, x = this, Ctor = x.constructor, pr = Ctor.precision;\n    y = new Ctor(y);\n    // x % 0 = NaN\n    if (!y.s) throw Error(decimalError + \"NaN\");\n    // Return x if x is 0.\n    if (!x.s) return round(new Ctor(x), pr);\n    // Prevent rounding of intermediate calculations.\n    external = false;\n    q = divide(x, y, 0, 1).times(y);\n    external = true;\n    return x.minus(q);\n};\n/*\r\n * Return a new Decimal whose value is the natural exponential of the value of this Decimal,\r\n * i.e. the base e raised to the power the value of this Decimal, truncated to `precision`\r\n * significant digits.\r\n *\r\n */ P.naturalExponential = P.exp = function() {\n    return exp(this);\n};\n/*\r\n * Return a new Decimal whose value is the natural logarithm of the value of this Decimal,\r\n * truncated to `precision` significant digits.\r\n *\r\n */ P.naturalLogarithm = P.ln = function() {\n    return ln(this);\n};\n/*\r\n * Return a new Decimal whose value is the value of this Decimal negated, i.e. as if multiplied by\r\n * -1.\r\n *\r\n */ P.negated = P.neg = function() {\n    var x = new this.constructor(this);\n    x.s = -x.s || 0;\n    return x;\n};\n/*\r\n * Return a new Decimal whose value is the value of this Decimal plus `y`, truncated to\r\n * `precision` significant digits.\r\n *\r\n */ P.plus = P.add = function(y) {\n    var x = this;\n    y = new x.constructor(y);\n    return x.s == y.s ? add(x, y) : subtract(x, (y.s = -y.s, y));\n};\n/*\r\n * Return the number of significant digits of the value of this Decimal.\r\n *\r\n * [z] {boolean|number} Whether to count integer-part trailing zeros: true, false, 1 or 0.\r\n *\r\n */ P.precision = P.sd = function(z) {\n    var e, sd, w, x = this;\n    if (z !== void 0 && z !== !!z && z !== 1 && z !== 0) throw Error(invalidArgument + z);\n    e = getBase10Exponent(x) + 1;\n    w = x.d.length - 1;\n    sd = w * LOG_BASE + 1;\n    w = x.d[w];\n    // If non-zero...\n    if (w) {\n        // Subtract the number of trailing zeros of the last word.\n        for(; w % 10 == 0; w /= 10)sd--;\n        // Add the number of digits of the first word.\n        for(w = x.d[0]; w >= 10; w /= 10)sd++;\n    }\n    return z && e > sd ? e : sd;\n};\n/*\r\n * Return a new Decimal whose value is the square root of this Decimal, truncated to `precision`\r\n * significant digits.\r\n *\r\n */ P.squareRoot = P.sqrt = function() {\n    var e, n, pr, r, s, t, wpr, x = this, Ctor = x.constructor;\n    // Negative or zero?\n    if (x.s < 1) {\n        if (!x.s) return new Ctor(0);\n        // sqrt(-x) = NaN\n        throw Error(decimalError + \"NaN\");\n    }\n    e = getBase10Exponent(x);\n    external = false;\n    // Initial estimate.\n    s = Math.sqrt(+x);\n    // Math.sqrt underflow/overflow?\n    // Pass x to Math.sqrt as integer, then adjust the exponent of the result.\n    if (s == 0 || s == 1 / 0) {\n        n = digitsToString(x.d);\n        if ((n.length + e) % 2 == 0) n += \"0\";\n        s = Math.sqrt(n);\n        e = mathfloor((e + 1) / 2) - (e < 0 || e % 2);\n        if (s == 1 / 0) {\n            n = \"5e\" + e;\n        } else {\n            n = s.toExponential();\n            n = n.slice(0, n.indexOf(\"e\") + 1) + e;\n        }\n        r = new Ctor(n);\n    } else {\n        r = new Ctor(s.toString());\n    }\n    pr = Ctor.precision;\n    s = wpr = pr + 3;\n    // Newton-Raphson iteration.\n    for(;;){\n        t = r;\n        r = t.plus(divide(x, t, wpr + 2)).times(0.5);\n        if (digitsToString(t.d).slice(0, wpr) === (n = digitsToString(r.d)).slice(0, wpr)) {\n            n = n.slice(wpr - 3, wpr + 1);\n            // The 4th rounding digit may be in error by -1 so if the 4 rounding digits are 9999 or\n            // 4999, i.e. approaching a rounding boundary, continue the iteration.\n            if (s == wpr && n == \"4999\") {\n                // On the first iteration only, check to see if rounding up gives the exact result as the\n                // nines may infinitely repeat.\n                round(t, pr + 1, 0);\n                if (t.times(t).eq(x)) {\n                    r = t;\n                    break;\n                }\n            } else if (n != \"9999\") {\n                break;\n            }\n            wpr += 4;\n        }\n    }\n    external = true;\n    return round(r, pr);\n};\n/*\r\n * Return a new Decimal whose value is the value of this Decimal times `y`, truncated to\r\n * `precision` significant digits.\r\n *\r\n */ P.times = P.mul = function(y) {\n    var carry, e, i, k, r, rL, t, xdL, ydL, x = this, Ctor = x.constructor, xd = x.d, yd = (y = new Ctor(y)).d;\n    // Return 0 if either is 0.\n    if (!x.s || !y.s) return new Ctor(0);\n    y.s *= x.s;\n    e = x.e + y.e;\n    xdL = xd.length;\n    ydL = yd.length;\n    // Ensure xd points to the longer array.\n    if (xdL < ydL) {\n        r = xd;\n        xd = yd;\n        yd = r;\n        rL = xdL;\n        xdL = ydL;\n        ydL = rL;\n    }\n    // Initialise the result array with zeros.\n    r = [];\n    rL = xdL + ydL;\n    for(i = rL; i--;)r.push(0);\n    // Multiply!\n    for(i = ydL; --i >= 0;){\n        carry = 0;\n        for(k = xdL + i; k > i;){\n            t = r[k] + yd[i] * xd[k - i - 1] + carry;\n            r[k--] = t % BASE | 0;\n            carry = t / BASE | 0;\n        }\n        r[k] = (r[k] + carry) % BASE | 0;\n    }\n    // Remove trailing zeros.\n    for(; !r[--rL];)r.pop();\n    if (carry) ++e;\n    else r.shift();\n    y.d = r;\n    y.e = e;\n    return external ? round(y, Ctor.precision) : y;\n};\n/*\r\n * Return a new Decimal whose value is the value of this Decimal rounded to a maximum of `dp`\r\n * decimal places using rounding mode `rm` or `rounding` if `rm` is omitted.\r\n *\r\n * If `dp` is omitted, return a new Decimal whose value is the value of this Decimal.\r\n *\r\n * [dp] {number} Decimal places. Integer, 0 to MAX_DIGITS inclusive.\r\n * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n *\r\n */ P.toDecimalPlaces = P.todp = function(dp, rm) {\n    var x = this, Ctor = x.constructor;\n    x = new Ctor(x);\n    if (dp === void 0) return x;\n    checkInt32(dp, 0, MAX_DIGITS);\n    if (rm === void 0) rm = Ctor.rounding;\n    else checkInt32(rm, 0, 8);\n    return round(x, dp + getBase10Exponent(x) + 1, rm);\n};\n/*\r\n * Return a string representing the value of this Decimal in exponential notation rounded to\r\n * `dp` fixed decimal places using rounding mode `rounding`.\r\n *\r\n * [dp] {number} Decimal places. Integer, 0 to MAX_DIGITS inclusive.\r\n * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n *\r\n */ P.toExponential = function(dp, rm) {\n    var str, x = this, Ctor = x.constructor;\n    if (dp === void 0) {\n        str = toString(x, true);\n    } else {\n        checkInt32(dp, 0, MAX_DIGITS);\n        if (rm === void 0) rm = Ctor.rounding;\n        else checkInt32(rm, 0, 8);\n        x = round(new Ctor(x), dp + 1, rm);\n        str = toString(x, true, dp + 1);\n    }\n    return str;\n};\n/*\r\n * Return a string representing the value of this Decimal in normal (fixed-point) notation to\r\n * `dp` fixed decimal places and rounded using rounding mode `rm` or `rounding` if `rm` is\r\n * omitted.\r\n *\r\n * As with JavaScript numbers, (-0).toFixed(0) is '0', but e.g. (-0.00001).toFixed(0) is '-0'.\r\n *\r\n * [dp] {number} Decimal places. Integer, 0 to MAX_DIGITS inclusive.\r\n * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n *\r\n * (-0).toFixed(0) is '0', but (-0.1).toFixed(0) is '-0'.\r\n * (-0).toFixed(1) is '0.0', but (-0.01).toFixed(1) is '-0.0'.\r\n * (-0).toFixed(3) is '0.000'.\r\n * (-0.5).toFixed(0) is '-0'.\r\n *\r\n */ P.toFixed = function(dp, rm) {\n    var str, y, x = this, Ctor = x.constructor;\n    if (dp === void 0) return toString(x);\n    checkInt32(dp, 0, MAX_DIGITS);\n    if (rm === void 0) rm = Ctor.rounding;\n    else checkInt32(rm, 0, 8);\n    y = round(new Ctor(x), dp + getBase10Exponent(x) + 1, rm);\n    str = toString(y.abs(), false, dp + getBase10Exponent(y) + 1);\n    // To determine whether to add the minus sign look at the value before it was rounded,\n    // i.e. look at `x` rather than `y`.\n    return x.isneg() && !x.isZero() ? \"-\" + str : str;\n};\n/*\r\n * Return a new Decimal whose value is the value of this Decimal rounded to a whole number using\r\n * rounding mode `rounding`.\r\n *\r\n */ P.toInteger = P.toint = function() {\n    var x = this, Ctor = x.constructor;\n    return round(new Ctor(x), getBase10Exponent(x) + 1, Ctor.rounding);\n};\n/*\r\n * Return the value of this Decimal converted to a number primitive.\r\n *\r\n */ P.toNumber = function() {\n    return +this;\n};\n/*\r\n * Return a new Decimal whose value is the value of this Decimal raised to the power `y`,\r\n * truncated to `precision` significant digits.\r\n *\r\n * For non-integer or very large exponents pow(x, y) is calculated using\r\n *\r\n *   x^y = exp(y*ln(x))\r\n *\r\n * The maximum error is 1 ulp (unit in last place).\r\n *\r\n * y {number|string|Decimal} The power to which to raise this Decimal.\r\n *\r\n */ P.toPower = P.pow = function(y) {\n    var e, k, pr, r, sign, yIsInt, x = this, Ctor = x.constructor, guard = 12, yn = +(y = new Ctor(y));\n    // pow(x, 0) = 1\n    if (!y.s) return new Ctor(ONE);\n    x = new Ctor(x);\n    // pow(0, y > 0) = 0\n    // pow(0, y < 0) = Infinity\n    if (!x.s) {\n        if (y.s < 1) throw Error(decimalError + \"Infinity\");\n        return x;\n    }\n    // pow(1, y) = 1\n    if (x.eq(ONE)) return x;\n    pr = Ctor.precision;\n    // pow(x, 1) = x\n    if (y.eq(ONE)) return round(x, pr);\n    e = y.e;\n    k = y.d.length - 1;\n    yIsInt = e >= k;\n    sign = x.s;\n    if (!yIsInt) {\n        // pow(x < 0, y non-integer) = NaN\n        if (sign < 0) throw Error(decimalError + \"NaN\");\n    // If y is a small integer use the 'exponentiation by squaring' algorithm.\n    } else if ((k = yn < 0 ? -yn : yn) <= MAX_SAFE_INTEGER) {\n        r = new Ctor(ONE);\n        // Max k of 9007199254740991 takes 53 loop iterations.\n        // Maximum digits array length; leaves [28, 34] guard digits.\n        e = Math.ceil(pr / LOG_BASE + 4);\n        external = false;\n        for(;;){\n            if (k % 2) {\n                r = r.times(x);\n                truncate(r.d, e);\n            }\n            k = mathfloor(k / 2);\n            if (k === 0) break;\n            x = x.times(x);\n            truncate(x.d, e);\n        }\n        external = true;\n        return y.s < 0 ? new Ctor(ONE).div(r) : round(r, pr);\n    }\n    // Result is negative if x is negative and the last digit of integer y is odd.\n    sign = sign < 0 && y.d[Math.max(e, k)] & 1 ? -1 : 1;\n    x.s = 1;\n    external = false;\n    r = y.times(ln(x, pr + guard));\n    external = true;\n    r = exp(r);\n    r.s = sign;\n    return r;\n};\n/*\r\n * Return a string representing the value of this Decimal rounded to `sd` significant digits\r\n * using rounding mode `rounding`.\r\n *\r\n * Return exponential notation if `sd` is less than the number of digits necessary to represent\r\n * the integer part of the value in normal notation.\r\n *\r\n * [sd] {number} Significant digits. Integer, 1 to MAX_DIGITS inclusive.\r\n * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n *\r\n */ P.toPrecision = function(sd, rm) {\n    var e, str, x = this, Ctor = x.constructor;\n    if (sd === void 0) {\n        e = getBase10Exponent(x);\n        str = toString(x, e <= Ctor.toExpNeg || e >= Ctor.toExpPos);\n    } else {\n        checkInt32(sd, 1, MAX_DIGITS);\n        if (rm === void 0) rm = Ctor.rounding;\n        else checkInt32(rm, 0, 8);\n        x = round(new Ctor(x), sd, rm);\n        e = getBase10Exponent(x);\n        str = toString(x, sd <= e || e <= Ctor.toExpNeg, sd);\n    }\n    return str;\n};\n/*\r\n * Return a new Decimal whose value is the value of this Decimal rounded to a maximum of `sd`\r\n * significant digits using rounding mode `rm`, or to `precision` and `rounding` respectively if\r\n * omitted.\r\n *\r\n * [sd] {number} Significant digits. Integer, 1 to MAX_DIGITS inclusive.\r\n * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n *\r\n */ P.toSignificantDigits = P.tosd = function(sd, rm) {\n    var x = this, Ctor = x.constructor;\n    if (sd === void 0) {\n        sd = Ctor.precision;\n        rm = Ctor.rounding;\n    } else {\n        checkInt32(sd, 1, MAX_DIGITS);\n        if (rm === void 0) rm = Ctor.rounding;\n        else checkInt32(rm, 0, 8);\n    }\n    return round(new Ctor(x), sd, rm);\n};\n/*\r\n * Return a string representing the value of this Decimal.\r\n *\r\n * Return exponential notation if this Decimal has a positive exponent equal to or greater than\r\n * `toExpPos`, or a negative exponent equal to or less than `toExpNeg`.\r\n *\r\n */ P.toString = P.valueOf = P.val = P.toJSON = P[Symbol.for(\"nodejs.util.inspect.custom\")] = function() {\n    var x = this, e = getBase10Exponent(x), Ctor = x.constructor;\n    return toString(x, e <= Ctor.toExpNeg || e >= Ctor.toExpPos);\n};\n// Helper functions for Decimal.prototype (P) and/or Decimal methods, and their callers.\n/*\r\n *  add                 P.minus, P.plus\r\n *  checkInt32          P.todp, P.toExponential, P.toFixed, P.toPrecision, P.tosd\r\n *  digitsToString      P.log, P.sqrt, P.pow, toString, exp, ln\r\n *  divide              P.div, P.idiv, P.log, P.mod, P.sqrt, exp, ln\r\n *  exp                 P.exp, P.pow\r\n *  getBase10Exponent   P.exponent, P.sd, P.toint, P.sqrt, P.todp, P.toFixed, P.toPrecision,\r\n *                      P.toString, divide, round, toString, exp, ln\r\n *  getLn10             P.log, ln\r\n *  getZeroString       digitsToString, toString\r\n *  ln                  P.log, P.ln, P.pow, exp\r\n *  parseDecimal        Decimal\r\n *  round               P.abs, P.idiv, P.log, P.minus, P.mod, P.neg, P.plus, P.toint, P.sqrt,\r\n *                      P.times, P.todp, P.toExponential, P.toFixed, P.pow, P.toPrecision, P.tosd,\r\n *                      divide, getLn10, exp, ln\r\n *  subtract            P.minus, P.plus\r\n *  toString            P.toExponential, P.toFixed, P.toPrecision, P.toString, P.valueOf\r\n *  truncate            P.pow\r\n *\r\n *  Throws:             P.log, P.mod, P.sd, P.sqrt, P.pow,  checkInt32, divide, round,\r\n *                      getLn10, exp, ln, parseDecimal, Decimal, config\r\n */ function add(x, y) {\n    var carry, d, e, i, k, len, xd, yd, Ctor = x.constructor, pr = Ctor.precision;\n    // If either is zero...\n    if (!x.s || !y.s) {\n        // Return x if y is zero.\n        // Return y if y is non-zero.\n        if (!y.s) y = new Ctor(x);\n        return external ? round(y, pr) : y;\n    }\n    xd = x.d;\n    yd = y.d;\n    // x and y are finite, non-zero numbers with the same sign.\n    k = x.e;\n    e = y.e;\n    xd = xd.slice();\n    i = k - e;\n    // If base 1e7 exponents differ...\n    if (i) {\n        if (i < 0) {\n            d = xd;\n            i = -i;\n            len = yd.length;\n        } else {\n            d = yd;\n            e = k;\n            len = xd.length;\n        }\n        // Limit number of zeros prepended to max(ceil(pr / LOG_BASE), len) + 1.\n        k = Math.ceil(pr / LOG_BASE);\n        len = k > len ? k + 1 : len + 1;\n        if (i > len) {\n            i = len;\n            d.length = 1;\n        }\n        // Prepend zeros to equalise exponents. Note: Faster to use reverse then do unshifts.\n        d.reverse();\n        for(; i--;)d.push(0);\n        d.reverse();\n    }\n    len = xd.length;\n    i = yd.length;\n    // If yd is longer than xd, swap xd and yd so xd points to the longer array.\n    if (len - i < 0) {\n        i = len;\n        d = yd;\n        yd = xd;\n        xd = d;\n    }\n    // Only start adding at yd.length - 1 as the further digits of xd can be left as they are.\n    for(carry = 0; i;){\n        carry = (xd[--i] = xd[i] + yd[i] + carry) / BASE | 0;\n        xd[i] %= BASE;\n    }\n    if (carry) {\n        xd.unshift(carry);\n        ++e;\n    }\n    // Remove trailing zeros.\n    // No need to check for zero, as +x + +y != 0 && -x + -y != 0\n    for(len = xd.length; xd[--len] == 0;)xd.pop();\n    y.d = xd;\n    y.e = e;\n    return external ? round(y, pr) : y;\n}\nfunction checkInt32(i, min, max) {\n    if (i !== ~~i || i < min || i > max) {\n        throw Error(invalidArgument + i);\n    }\n}\nfunction digitsToString(d) {\n    var i, k, ws, indexOfLastWord = d.length - 1, str = \"\", w = d[0];\n    if (indexOfLastWord > 0) {\n        str += w;\n        for(i = 1; i < indexOfLastWord; i++){\n            ws = d[i] + \"\";\n            k = LOG_BASE - ws.length;\n            if (k) str += getZeroString(k);\n            str += ws;\n        }\n        w = d[i];\n        ws = w + \"\";\n        k = LOG_BASE - ws.length;\n        if (k) str += getZeroString(k);\n    } else if (w === 0) {\n        return \"0\";\n    }\n    // Remove trailing zeros of last w.\n    for(; w % 10 === 0;)w /= 10;\n    return str + w;\n}\nvar divide = function() {\n    // Assumes non-zero x and k, and hence non-zero result.\n    function multiplyInteger(x, k) {\n        var temp, carry = 0, i = x.length;\n        for(x = x.slice(); i--;){\n            temp = x[i] * k + carry;\n            x[i] = temp % BASE | 0;\n            carry = temp / BASE | 0;\n        }\n        if (carry) x.unshift(carry);\n        return x;\n    }\n    function compare(a, b, aL, bL) {\n        var i, r;\n        if (aL != bL) {\n            r = aL > bL ? 1 : -1;\n        } else {\n            for(i = r = 0; i < aL; i++){\n                if (a[i] != b[i]) {\n                    r = a[i] > b[i] ? 1 : -1;\n                    break;\n                }\n            }\n        }\n        return r;\n    }\n    function subtract(a, b, aL) {\n        var i = 0;\n        // Subtract b from a.\n        for(; aL--;){\n            a[aL] -= i;\n            i = a[aL] < b[aL] ? 1 : 0;\n            a[aL] = i * BASE + a[aL] - b[aL];\n        }\n        // Remove leading zeros.\n        for(; !a[0] && a.length > 1;)a.shift();\n    }\n    return function(x, y, pr, dp) {\n        var cmp, e, i, k, prod, prodL, q, qd, rem, remL, rem0, sd, t, xi, xL, yd0, yL, yz, Ctor = x.constructor, sign = x.s == y.s ? 1 : -1, xd = x.d, yd = y.d;\n        // Either 0?\n        if (!x.s) return new Ctor(x);\n        if (!y.s) throw Error(decimalError + \"Division by zero\");\n        e = x.e - y.e;\n        yL = yd.length;\n        xL = xd.length;\n        q = new Ctor(sign);\n        qd = q.d = [];\n        // Result exponent may be one less than e.\n        for(i = 0; yd[i] == (xd[i] || 0);)++i;\n        if (yd[i] > (xd[i] || 0)) --e;\n        if (pr == null) {\n            sd = pr = Ctor.precision;\n        } else if (dp) {\n            sd = pr + (getBase10Exponent(x) - getBase10Exponent(y)) + 1;\n        } else {\n            sd = pr;\n        }\n        if (sd < 0) return new Ctor(0);\n        // Convert precision in number of base 10 digits to base 1e7 digits.\n        sd = sd / LOG_BASE + 2 | 0;\n        i = 0;\n        // divisor < 1e7\n        if (yL == 1) {\n            k = 0;\n            yd = yd[0];\n            sd++;\n            // k is the carry.\n            for(; (i < xL || k) && sd--; i++){\n                t = k * BASE + (xd[i] || 0);\n                qd[i] = t / yd | 0;\n                k = t % yd | 0;\n            }\n        // divisor >= 1e7\n        } else {\n            // Normalise xd and yd so highest order digit of yd is >= BASE/2\n            k = BASE / (yd[0] + 1) | 0;\n            if (k > 1) {\n                yd = multiplyInteger(yd, k);\n                xd = multiplyInteger(xd, k);\n                yL = yd.length;\n                xL = xd.length;\n            }\n            xi = yL;\n            rem = xd.slice(0, yL);\n            remL = rem.length;\n            // Add zeros to make remainder as long as divisor.\n            for(; remL < yL;)rem[remL++] = 0;\n            yz = yd.slice();\n            yz.unshift(0);\n            yd0 = yd[0];\n            if (yd[1] >= BASE / 2) ++yd0;\n            do {\n                k = 0;\n                // Compare divisor and remainder.\n                cmp = compare(yd, rem, yL, remL);\n                // If divisor < remainder.\n                if (cmp < 0) {\n                    // Calculate trial digit, k.\n                    rem0 = rem[0];\n                    if (yL != remL) rem0 = rem0 * BASE + (rem[1] || 0);\n                    // k will be how many times the divisor goes into the current remainder.\n                    k = rem0 / yd0 | 0;\n                    //  Algorithm:\n                    //  1. product = divisor * trial digit (k)\n                    //  2. if product > remainder: product -= divisor, k--\n                    //  3. remainder -= product\n                    //  4. if product was < remainder at 2:\n                    //    5. compare new remainder and divisor\n                    //    6. If remainder > divisor: remainder -= divisor, k++\n                    if (k > 1) {\n                        if (k >= BASE) k = BASE - 1;\n                        // product = divisor * trial digit.\n                        prod = multiplyInteger(yd, k);\n                        prodL = prod.length;\n                        remL = rem.length;\n                        // Compare product and remainder.\n                        cmp = compare(prod, rem, prodL, remL);\n                        // product > remainder.\n                        if (cmp == 1) {\n                            k--;\n                            // Subtract divisor from product.\n                            subtract(prod, yL < prodL ? yz : yd, prodL);\n                        }\n                    } else {\n                        // cmp is -1.\n                        // If k is 0, there is no need to compare yd and rem again below, so change cmp to 1\n                        // to avoid it. If k is 1 there is a need to compare yd and rem again below.\n                        if (k == 0) cmp = k = 1;\n                        prod = yd.slice();\n                    }\n                    prodL = prod.length;\n                    if (prodL < remL) prod.unshift(0);\n                    // Subtract product from remainder.\n                    subtract(rem, prod, remL);\n                    // If product was < previous remainder.\n                    if (cmp == -1) {\n                        remL = rem.length;\n                        // Compare divisor and new remainder.\n                        cmp = compare(yd, rem, yL, remL);\n                        // If divisor < new remainder, subtract divisor from remainder.\n                        if (cmp < 1) {\n                            k++;\n                            // Subtract divisor from remainder.\n                            subtract(rem, yL < remL ? yz : yd, remL);\n                        }\n                    }\n                    remL = rem.length;\n                } else if (cmp === 0) {\n                    k++;\n                    rem = [\n                        0\n                    ];\n                } // if cmp === 1, k will be 0\n                // Add the next digit, k, to the result array.\n                qd[i++] = k;\n                // Update the remainder.\n                if (cmp && rem[0]) {\n                    rem[remL++] = xd[xi] || 0;\n                } else {\n                    rem = [\n                        xd[xi]\n                    ];\n                    remL = 1;\n                }\n            }while ((xi++ < xL || rem[0] !== void 0) && sd--);\n        }\n        // Leading zero?\n        if (!qd[0]) qd.shift();\n        q.e = e;\n        return round(q, dp ? pr + getBase10Exponent(q) + 1 : pr);\n    };\n}();\n/*\r\n * Return a new Decimal whose value is the natural exponential of `x` truncated to `sd`\r\n * significant digits.\r\n *\r\n * Taylor/Maclaurin series.\r\n *\r\n * exp(x) = x^0/0! + x^1/1! + x^2/2! + x^3/3! + ...\r\n *\r\n * Argument reduction:\r\n *   Repeat x = x / 32, k += 5, until |x| < 0.1\r\n *   exp(x) = exp(x / 2^k)^(2^k)\r\n *\r\n * Previously, the argument was initially reduced by\r\n * exp(x) = exp(r) * 10^k  where r = x - k * ln10, k = floor(x / ln10)\r\n * to first put r in the range [0, ln10], before dividing by 32 until |x| < 0.1, but this was\r\n * found to be slower than just dividing repeatedly by 32 as above.\r\n *\r\n * (Math object integer min/max: Math.exp(709) = 8.2e+307, Math.exp(-745) = 5e-324)\r\n *\r\n *  exp(x) is non-terminating for any finite, non-zero x.\r\n *\r\n */ function exp(x, sd) {\n    var denominator, guard, pow, sum, t, wpr, i = 0, k = 0, Ctor = x.constructor, pr = Ctor.precision;\n    if (getBase10Exponent(x) > 16) throw Error(exponentOutOfRange + getBase10Exponent(x));\n    // exp(0) = 1\n    if (!x.s) return new Ctor(ONE);\n    if (sd == null) {\n        external = false;\n        wpr = pr;\n    } else {\n        wpr = sd;\n    }\n    t = new Ctor(0.03125);\n    while(x.abs().gte(0.1)){\n        x = x.times(t); // x = x / 2^5\n        k += 5;\n    }\n    // Estimate the precision increase necessary to ensure the first 4 rounding digits are correct.\n    guard = Math.log(mathpow(2, k)) / Math.LN10 * 2 + 5 | 0;\n    wpr += guard;\n    denominator = pow = sum = new Ctor(ONE);\n    Ctor.precision = wpr;\n    for(;;){\n        pow = round(pow.times(x), wpr);\n        denominator = denominator.times(++i);\n        t = sum.plus(divide(pow, denominator, wpr));\n        if (digitsToString(t.d).slice(0, wpr) === digitsToString(sum.d).slice(0, wpr)) {\n            while(k--)sum = round(sum.times(sum), wpr);\n            Ctor.precision = pr;\n            return sd == null ? (external = true, round(sum, pr)) : sum;\n        }\n        sum = t;\n    }\n}\n// Calculate the base 10 exponent from the base 1e7 exponent.\nfunction getBase10Exponent(x) {\n    var e = x.e * LOG_BASE, w = x.d[0];\n    // Add the number of digits of the first word of the digits array.\n    for(; w >= 10; w /= 10)e++;\n    return e;\n}\nfunction getLn10(Ctor, sd, pr) {\n    if (sd > Ctor.LN10.sd()) {\n        // Reset global state in case the exception is caught.\n        external = true;\n        if (pr) Ctor.precision = pr;\n        throw Error(decimalError + \"LN10 precision limit exceeded\");\n    }\n    return round(new Ctor(Ctor.LN10), sd);\n}\nfunction getZeroString(k) {\n    var zs = \"\";\n    for(; k--;)zs += \"0\";\n    return zs;\n}\n/*\r\n * Return a new Decimal whose value is the natural logarithm of `x` truncated to `sd` significant\r\n * digits.\r\n *\r\n *  ln(n) is non-terminating (n != 1)\r\n *\r\n */ function ln(y, sd) {\n    var c, c0, denominator, e, numerator, sum, t, wpr, x2, n = 1, guard = 10, x = y, xd = x.d, Ctor = x.constructor, pr = Ctor.precision;\n    // ln(-x) = NaN\n    // ln(0) = -Infinity\n    if (x.s < 1) throw Error(decimalError + (x.s ? \"NaN\" : \"-Infinity\"));\n    // ln(1) = 0\n    if (x.eq(ONE)) return new Ctor(0);\n    if (sd == null) {\n        external = false;\n        wpr = pr;\n    } else {\n        wpr = sd;\n    }\n    if (x.eq(10)) {\n        if (sd == null) external = true;\n        return getLn10(Ctor, wpr);\n    }\n    wpr += guard;\n    Ctor.precision = wpr;\n    c = digitsToString(xd);\n    c0 = c.charAt(0);\n    e = getBase10Exponent(x);\n    if (Math.abs(e) < 1.5e15) {\n        // Argument reduction.\n        // The series converges faster the closer the argument is to 1, so using\n        // ln(a^b) = b * ln(a),   ln(a) = ln(a^b) / b\n        // multiply the argument by itself until the leading digits of the significand are 7, 8, 9,\n        // 10, 11, 12 or 13, recording the number of multiplications so the sum of the series can\n        // later be divided by this number, then separate out the power of 10 using\n        // ln(a*10^b) = ln(a) + b*ln(10).\n        // max n is 21 (gives 0.9, 1.0 or 1.1) (9e15 / 21 = 4.2e14).\n        //while (c0 < 9 && c0 != 1 || c0 == 1 && c.charAt(1) > 1) {\n        // max n is 6 (gives 0.7 - 1.3)\n        while(c0 < 7 && c0 != 1 || c0 == 1 && c.charAt(1) > 3){\n            x = x.times(y);\n            c = digitsToString(x.d);\n            c0 = c.charAt(0);\n            n++;\n        }\n        e = getBase10Exponent(x);\n        if (c0 > 1) {\n            x = new Ctor(\"0.\" + c);\n            e++;\n        } else {\n            x = new Ctor(c0 + \".\" + c.slice(1));\n        }\n    } else {\n        // The argument reduction method above may result in overflow if the argument y is a massive\n        // number with exponent >= 1500000000000000 (9e15 / 6 = 1.5e15), so instead recall this\n        // function using ln(x*10^e) = ln(x) + e*ln(10).\n        t = getLn10(Ctor, wpr + 2, pr).times(e + \"\");\n        x = ln(new Ctor(c0 + \".\" + c.slice(1)), wpr - guard).plus(t);\n        Ctor.precision = pr;\n        return sd == null ? (external = true, round(x, pr)) : x;\n    }\n    // x is reduced to a value near 1.\n    // Taylor series.\n    // ln(y) = ln((1 + x)/(1 - x)) = 2(x + x^3/3 + x^5/5 + x^7/7 + ...)\n    // where x = (y - 1)/(y + 1)    (|x| < 1)\n    sum = numerator = x = divide(x.minus(ONE), x.plus(ONE), wpr);\n    x2 = round(x.times(x), wpr);\n    denominator = 3;\n    for(;;){\n        numerator = round(numerator.times(x2), wpr);\n        t = sum.plus(divide(numerator, new Ctor(denominator), wpr));\n        if (digitsToString(t.d).slice(0, wpr) === digitsToString(sum.d).slice(0, wpr)) {\n            sum = sum.times(2);\n            // Reverse the argument reduction.\n            if (e !== 0) sum = sum.plus(getLn10(Ctor, wpr + 2, pr).times(e + \"\"));\n            sum = divide(sum, new Ctor(n), wpr);\n            Ctor.precision = pr;\n            return sd == null ? (external = true, round(sum, pr)) : sum;\n        }\n        sum = t;\n        denominator += 2;\n    }\n}\n/*\r\n * Parse the value of a new Decimal `x` from string `str`.\r\n */ function parseDecimal(x, str) {\n    var e, i, len;\n    // Decimal point?\n    if ((e = str.indexOf(\".\")) > -1) str = str.replace(\".\", \"\");\n    // Exponential form?\n    if ((i = str.search(/e/i)) > 0) {\n        // Determine exponent.\n        if (e < 0) e = i;\n        e += +str.slice(i + 1);\n        str = str.substring(0, i);\n    } else if (e < 0) {\n        // Integer.\n        e = str.length;\n    }\n    // Determine leading zeros.\n    for(i = 0; str.charCodeAt(i) === 48;)++i;\n    // Determine trailing zeros.\n    for(len = str.length; str.charCodeAt(len - 1) === 48;)--len;\n    str = str.slice(i, len);\n    if (str) {\n        len -= i;\n        e = e - i - 1;\n        x.e = mathfloor(e / LOG_BASE);\n        x.d = [];\n        // Transform base\n        // e is the base 10 exponent.\n        // i is where to slice str to get the first word of the digits array.\n        i = (e + 1) % LOG_BASE;\n        if (e < 0) i += LOG_BASE;\n        if (i < len) {\n            if (i) x.d.push(+str.slice(0, i));\n            for(len -= LOG_BASE; i < len;)x.d.push(+str.slice(i, i += LOG_BASE));\n            str = str.slice(i);\n            i = LOG_BASE - str.length;\n        } else {\n            i -= len;\n        }\n        for(; i--;)str += \"0\";\n        x.d.push(+str);\n        if (external && (x.e > MAX_E || x.e < -MAX_E)) throw Error(exponentOutOfRange + e);\n    } else {\n        // Zero.\n        x.s = 0;\n        x.e = 0;\n        x.d = [\n            0\n        ];\n    }\n    return x;\n}\n/*\r\n * Round `x` to `sd` significant digits, using rounding mode `rm` if present (truncate otherwise).\r\n */ function round(x, sd, rm) {\n    var i, j, k, n, rd, doRound, w, xdi, xd = x.d;\n    // rd: the rounding digit, i.e. the digit after the digit that may be rounded up.\n    // w: the word of xd which contains the rounding digit, a base 1e7 number.\n    // xdi: the index of w within xd.\n    // n: the number of digits of w.\n    // i: what would be the index of rd within w if all the numbers were 7 digits long (i.e. if\n    // they had leading zeros)\n    // j: if > 0, the actual index of rd within w (if < 0, rd is a leading zero).\n    // Get the length of the first word of the digits array xd.\n    for(n = 1, k = xd[0]; k >= 10; k /= 10)n++;\n    i = sd - n;\n    // Is the rounding digit in the first word of xd?\n    if (i < 0) {\n        i += LOG_BASE;\n        j = sd;\n        w = xd[xdi = 0];\n    } else {\n        xdi = Math.ceil((i + 1) / LOG_BASE);\n        k = xd.length;\n        if (xdi >= k) return x;\n        w = k = xd[xdi];\n        // Get the number of digits of w.\n        for(n = 1; k >= 10; k /= 10)n++;\n        // Get the index of rd within w.\n        i %= LOG_BASE;\n        // Get the index of rd within w, adjusted for leading zeros.\n        // The number of leading zeros of w is given by LOG_BASE - n.\n        j = i - LOG_BASE + n;\n    }\n    if (rm !== void 0) {\n        k = mathpow(10, n - j - 1);\n        // Get the rounding digit at index j of w.\n        rd = w / k % 10 | 0;\n        // Are there any non-zero digits after the rounding digit?\n        doRound = sd < 0 || xd[xdi + 1] !== void 0 || w % k;\n        // The expression `w % mathpow(10, n - j - 1)` returns all the digits of w to the right of the\n        // digit at (left-to-right) index j, e.g. if w is 908714 and j is 2, the expression will give\n        // 714.\n        doRound = rm < 4 ? (rd || doRound) && (rm == 0 || rm == (x.s < 0 ? 3 : 2)) : rd > 5 || rd == 5 && (rm == 4 || doRound || rm == 6 && (i > 0 ? j > 0 ? w / mathpow(10, n - j) : 0 : xd[xdi - 1]) % 10 & 1 || rm == (x.s < 0 ? 8 : 7));\n    }\n    if (sd < 1 || !xd[0]) {\n        if (doRound) {\n            k = getBase10Exponent(x);\n            xd.length = 1;\n            // Convert sd to decimal places.\n            sd = sd - k - 1;\n            // 1, 0.1, 0.01, 0.001, 0.0001 etc.\n            xd[0] = mathpow(10, (LOG_BASE - sd % LOG_BASE) % LOG_BASE);\n            x.e = mathfloor(-sd / LOG_BASE) || 0;\n        } else {\n            xd.length = 1;\n            // Zero.\n            xd[0] = x.e = x.s = 0;\n        }\n        return x;\n    }\n    // Remove excess digits.\n    if (i == 0) {\n        xd.length = xdi;\n        k = 1;\n        xdi--;\n    } else {\n        xd.length = xdi + 1;\n        k = mathpow(10, LOG_BASE - i);\n        // E.g. 56700 becomes 56000 if 7 is the rounding digit.\n        // j > 0 means i > number of leading zeros of w.\n        xd[xdi] = j > 0 ? (w / mathpow(10, n - j) % mathpow(10, j) | 0) * k : 0;\n    }\n    if (doRound) {\n        for(;;){\n            // Is the digit to be rounded up in the first word of xd?\n            if (xdi == 0) {\n                if ((xd[0] += k) == BASE) {\n                    xd[0] = 1;\n                    ++x.e;\n                }\n                break;\n            } else {\n                xd[xdi] += k;\n                if (xd[xdi] != BASE) break;\n                xd[xdi--] = 0;\n                k = 1;\n            }\n        }\n    }\n    // Remove trailing zeros.\n    for(i = xd.length; xd[--i] === 0;)xd.pop();\n    if (external && (x.e > MAX_E || x.e < -MAX_E)) {\n        throw Error(exponentOutOfRange + getBase10Exponent(x));\n    }\n    return x;\n}\nfunction subtract(x, y) {\n    var d, e, i, j, k, len, xd, xe, xLTy, yd, Ctor = x.constructor, pr = Ctor.precision;\n    // Return y negated if x is zero.\n    // Return x if y is zero and x is non-zero.\n    if (!x.s || !y.s) {\n        if (y.s) y.s = -y.s;\n        else y = new Ctor(x);\n        return external ? round(y, pr) : y;\n    }\n    xd = x.d;\n    yd = y.d;\n    // x and y are non-zero numbers with the same sign.\n    e = y.e;\n    xe = x.e;\n    xd = xd.slice();\n    k = xe - e;\n    // If exponents differ...\n    if (k) {\n        xLTy = k < 0;\n        if (xLTy) {\n            d = xd;\n            k = -k;\n            len = yd.length;\n        } else {\n            d = yd;\n            e = xe;\n            len = xd.length;\n        }\n        // Numbers with massively different exponents would result in a very high number of zeros\n        // needing to be prepended, but this can be avoided while still ensuring correct rounding by\n        // limiting the number of zeros to `Math.ceil(pr / LOG_BASE) + 2`.\n        i = Math.max(Math.ceil(pr / LOG_BASE), len) + 2;\n        if (k > i) {\n            k = i;\n            d.length = 1;\n        }\n        // Prepend zeros to equalise exponents.\n        d.reverse();\n        for(i = k; i--;)d.push(0);\n        d.reverse();\n    // Base 1e7 exponents equal.\n    } else {\n        // Check digits to determine which is the bigger number.\n        i = xd.length;\n        len = yd.length;\n        xLTy = i < len;\n        if (xLTy) len = i;\n        for(i = 0; i < len; i++){\n            if (xd[i] != yd[i]) {\n                xLTy = xd[i] < yd[i];\n                break;\n            }\n        }\n        k = 0;\n    }\n    if (xLTy) {\n        d = xd;\n        xd = yd;\n        yd = d;\n        y.s = -y.s;\n    }\n    len = xd.length;\n    // Append zeros to xd if shorter.\n    // Don't add zeros to yd if shorter as subtraction only needs to start at yd length.\n    for(i = yd.length - len; i > 0; --i)xd[len++] = 0;\n    // Subtract yd from xd.\n    for(i = yd.length; i > k;){\n        if (xd[--i] < yd[i]) {\n            for(j = i; j && xd[--j] === 0;)xd[j] = BASE - 1;\n            --xd[j];\n            xd[i] += BASE;\n        }\n        xd[i] -= yd[i];\n    }\n    // Remove trailing zeros.\n    for(; xd[--len] === 0;)xd.pop();\n    // Remove leading zeros and adjust exponent accordingly.\n    for(; xd[0] === 0; xd.shift())--e;\n    // Zero?\n    if (!xd[0]) return new Ctor(0);\n    y.d = xd;\n    y.e = e;\n    //return external && xd.length >= pr / LOG_BASE ? round(y, pr) : y;\n    return external ? round(y, pr) : y;\n}\nfunction toString(x, isExp, sd) {\n    var k, e = getBase10Exponent(x), str = digitsToString(x.d), len = str.length;\n    if (isExp) {\n        if (sd && (k = sd - len) > 0) {\n            str = str.charAt(0) + \".\" + str.slice(1) + getZeroString(k);\n        } else if (len > 1) {\n            str = str.charAt(0) + \".\" + str.slice(1);\n        }\n        str = str + (e < 0 ? \"e\" : \"e+\") + e;\n    } else if (e < 0) {\n        str = \"0.\" + getZeroString(-e - 1) + str;\n        if (sd && (k = sd - len) > 0) str += getZeroString(k);\n    } else if (e >= len) {\n        str += getZeroString(e + 1 - len);\n        if (sd && (k = sd - e - 1) > 0) str = str + \".\" + getZeroString(k);\n    } else {\n        if ((k = e + 1) < len) str = str.slice(0, k) + \".\" + str.slice(k);\n        if (sd && (k = sd - len) > 0) {\n            if (e + 1 === len) str += \".\";\n            str += getZeroString(k);\n        }\n    }\n    return x.s < 0 ? \"-\" + str : str;\n}\n// Does not strip trailing zeros.\nfunction truncate(arr, len) {\n    if (arr.length > len) {\n        arr.length = len;\n        return true;\n    }\n}\n// Decimal methods\n/*\r\n *  clone\r\n *  config/set\r\n */ /*\r\n * Create and return a Decimal constructor with the same configuration properties as this Decimal\r\n * constructor.\r\n *\r\n */ function clone(obj) {\n    var i, p, ps;\n    /*\r\n   * The Decimal constructor and exported function.\r\n   * Return a new Decimal instance.\r\n   *\r\n   * value {number|string|Decimal} A numeric value.\r\n   *\r\n   */ function Decimal(value) {\n        var x = this;\n        // Decimal called without new.\n        if (!(x instanceof Decimal)) return new Decimal(value);\n        // Retain a reference to this Decimal constructor, and shadow Decimal.prototype.constructor\n        // which points to Object.\n        x.constructor = Decimal;\n        // Duplicate.\n        if (value instanceof Decimal) {\n            x.s = value.s;\n            x.e = value.e;\n            x.d = (value = value.d) ? value.slice() : value;\n            return;\n        }\n        if (typeof value === \"number\") {\n            // Reject Infinity/NaN.\n            if (value * 0 !== 0) {\n                throw Error(invalidArgument + value);\n            }\n            if (value > 0) {\n                x.s = 1;\n            } else if (value < 0) {\n                value = -value;\n                x.s = -1;\n            } else {\n                x.s = 0;\n                x.e = 0;\n                x.d = [\n                    0\n                ];\n                return;\n            }\n            // Fast path for small integers.\n            if (value === ~~value && value < 1e7) {\n                x.e = 0;\n                x.d = [\n                    value\n                ];\n                return;\n            }\n            return parseDecimal(x, value.toString());\n        } else if (typeof value !== \"string\") {\n            throw Error(invalidArgument + value);\n        }\n        // Minus sign?\n        if (value.charCodeAt(0) === 45) {\n            value = value.slice(1);\n            x.s = -1;\n        } else {\n            x.s = 1;\n        }\n        if (isDecimal.test(value)) parseDecimal(x, value);\n        else throw Error(invalidArgument + value);\n    }\n    Decimal.prototype = P;\n    Decimal.ROUND_UP = 0;\n    Decimal.ROUND_DOWN = 1;\n    Decimal.ROUND_CEIL = 2;\n    Decimal.ROUND_FLOOR = 3;\n    Decimal.ROUND_HALF_UP = 4;\n    Decimal.ROUND_HALF_DOWN = 5;\n    Decimal.ROUND_HALF_EVEN = 6;\n    Decimal.ROUND_HALF_CEIL = 7;\n    Decimal.ROUND_HALF_FLOOR = 8;\n    Decimal.clone = clone;\n    Decimal.config = Decimal.set = config;\n    if (obj === void 0) obj = {};\n    if (obj) {\n        ps = [\n            \"precision\",\n            \"rounding\",\n            \"toExpNeg\",\n            \"toExpPos\",\n            \"LN10\"\n        ];\n        for(i = 0; i < ps.length;)if (!obj.hasOwnProperty(p = ps[i++])) obj[p] = this[p];\n    }\n    Decimal.config(obj);\n    return Decimal;\n}\n/*\r\n * Configure global settings for a Decimal constructor.\r\n *\r\n * `obj` is an object with one or more of the following properties,\r\n *\r\n *   precision  {number}\r\n *   rounding   {number}\r\n *   toExpNeg   {number}\r\n *   toExpPos   {number}\r\n *\r\n * E.g. Decimal.config({ precision: 20, rounding: 4 })\r\n *\r\n */ function config(obj) {\n    if (!obj || typeof obj !== \"object\") {\n        throw Error(decimalError + \"Object expected\");\n    }\n    var i, p, v, ps = [\n        \"precision\",\n        1,\n        MAX_DIGITS,\n        \"rounding\",\n        0,\n        8,\n        \"toExpNeg\",\n        -1 / 0,\n        0,\n        \"toExpPos\",\n        0,\n        1 / 0\n    ];\n    for(i = 0; i < ps.length; i += 3){\n        if ((v = obj[p = ps[i]]) !== void 0) {\n            if (mathfloor(v) === v && v >= ps[i + 1] && v <= ps[i + 2]) this[p] = v;\n            else throw Error(invalidArgument + p + \": \" + v);\n        }\n    }\n    if ((v = obj[p = \"LN10\"]) !== void 0) {\n        if (v == Math.LN10) this[p] = new this(v);\n        else throw Error(invalidArgument + p + \": \" + v);\n    }\n    return this;\n}\n// Create and configure initial Decimal constructor.\nvar Decimal = clone(defaults);\n// Internal constant.\nONE = new Decimal(1);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Decimal);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzL2RlY2ltYWwuanMtbGlnaHQvZGVjaW1hbC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTs7Ozs7O0NBTUMsR0FHRCxvR0FBb0c7QUFHcEcsbUZBQW1GO0FBQ25GLDBGQUEwRjtBQUMxRixJQUFJQSxhQUFhLEtBR2YsbUVBQW1FO0FBQ25FQyxXQUFXO0lBRVQsc0VBQXNFO0lBQ3RFLDhFQUE4RTtJQUU5RSw4RkFBOEY7SUFDOUYsNENBQTRDO0lBQzVDQyxXQUFXO0lBRVgsd0ZBQXdGO0lBQ3hGLHNEQUFzRDtJQUN0RCxFQUFFO0lBQ0YscUNBQXFDO0lBQ3JDLG1DQUFtQztJQUNuQyx3Q0FBd0M7SUFDeEMsd0NBQXdDO0lBQ3hDLG9FQUFvRTtJQUNwRSxzRUFBc0U7SUFDdEUsd0ZBQXdGO0lBQ3hGLG1GQUFtRjtJQUNuRixtRkFBbUY7SUFDbkYsRUFBRTtJQUNGLE9BQU87SUFDUCwwQkFBMEI7SUFDMUIsOENBQThDO0lBQzlDQyxVQUFVO0lBRVYsbUZBQW1GO0lBQ25GLHlCQUF5QjtJQUN6QkMsVUFBVSxDQUFDO0lBRVgsaUZBQWlGO0lBQ2pGLHlCQUF5QjtJQUN6QkMsVUFBVztJQUVYLCtCQUErQjtJQUMvQixhQUFhO0lBQ2JDLE1BQU07QUFDUixHQUdGLG9HQUFvRztBQUdsR0MsU0FDQUMsV0FBVyxNQUVYQyxlQUFlLG1CQUNmQyxrQkFBa0JELGVBQWUsc0JBQ2pDRSxxQkFBcUJGLGVBQWUsMkJBRXBDRyxZQUFZQyxLQUFLQyxLQUFLLEVBQ3RCQyxVQUFVRixLQUFLRyxHQUFHLEVBRWxCQyxZQUFZLHNDQUVaQyxLQUNBQyxPQUFPLEtBQ1BDLFdBQVcsR0FDWEMsbUJBQW1CLGtCQUNuQkMsUUFBUVYsVUFBVVMsbUJBQW1CRCxXQUVyQywyQkFBMkI7QUFDM0JHLElBQUksQ0FBQztBQUdQLDRCQUE0QjtBQUc1Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0NBb0NDLEdBR0Q7OztDQUdDLEdBQ0RBLEVBQUVDLGFBQWEsR0FBR0QsRUFBRUUsR0FBRyxHQUFHO0lBQ3hCLElBQUlDLElBQUksSUFBSSxJQUFJLENBQUNDLFdBQVcsQ0FBQyxJQUFJO0lBQ2pDLElBQUlELEVBQUVFLENBQUMsRUFBRUYsRUFBRUUsQ0FBQyxHQUFHO0lBQ2YsT0FBT0Y7QUFDVDtBQUdBOzs7Ozs7Q0FNQyxHQUNESCxFQUFFTSxVQUFVLEdBQUdOLEVBQUVPLEdBQUcsR0FBRyxTQUFVQyxDQUFDO0lBQ2hDLElBQUlDLEdBQUdDLEdBQUdDLEtBQUtDLEtBQ2JULElBQUksSUFBSTtJQUVWSyxJQUFJLElBQUlMLEVBQUVDLFdBQVcsQ0FBQ0k7SUFFdEIsZ0JBQWdCO0lBQ2hCLElBQUlMLEVBQUVFLENBQUMsS0FBS0csRUFBRUgsQ0FBQyxFQUFFLE9BQU9GLEVBQUVFLENBQUMsSUFBSSxDQUFDRyxFQUFFSCxDQUFDO0lBRW5DLHFCQUFxQjtJQUNyQixJQUFJRixFQUFFVSxDQUFDLEtBQUtMLEVBQUVLLENBQUMsRUFBRSxPQUFPVixFQUFFVSxDQUFDLEdBQUdMLEVBQUVLLENBQUMsR0FBR1YsRUFBRUUsQ0FBQyxHQUFHLElBQUksSUFBSSxDQUFDO0lBRW5ETSxNQUFNUixFQUFFVyxDQUFDLENBQUNDLE1BQU07SUFDaEJILE1BQU1KLEVBQUVNLENBQUMsQ0FBQ0MsTUFBTTtJQUVoQiwwQkFBMEI7SUFDMUIsSUFBS04sSUFBSSxHQUFHQyxJQUFJQyxNQUFNQyxNQUFNRCxNQUFNQyxLQUFLSCxJQUFJQyxHQUFHLEVBQUVELEVBQUc7UUFDakQsSUFBSU4sRUFBRVcsQ0FBQyxDQUFDTCxFQUFFLEtBQUtELEVBQUVNLENBQUMsQ0FBQ0wsRUFBRSxFQUFFLE9BQU9OLEVBQUVXLENBQUMsQ0FBQ0wsRUFBRSxHQUFHRCxFQUFFTSxDQUFDLENBQUNMLEVBQUUsR0FBR04sRUFBRUUsQ0FBQyxHQUFHLElBQUksSUFBSSxDQUFDO0lBQ2pFO0lBRUEsbUJBQW1CO0lBQ25CLE9BQU9NLFFBQVFDLE1BQU0sSUFBSUQsTUFBTUMsTUFBTVQsRUFBRUUsQ0FBQyxHQUFHLElBQUksSUFBSSxDQUFDO0FBQ3REO0FBR0E7OztDQUdDLEdBQ0RMLEVBQUVnQixhQUFhLEdBQUdoQixFQUFFaUIsRUFBRSxHQUFHO0lBQ3ZCLElBQUlkLElBQUksSUFBSSxFQUNWZSxJQUFJZixFQUFFVyxDQUFDLENBQUNDLE1BQU0sR0FBRyxHQUNqQkUsS0FBSyxDQUFDQyxJQUFJZixFQUFFVSxDQUFDLElBQUloQjtJQUVuQiwwREFBMEQ7SUFDMURxQixJQUFJZixFQUFFVyxDQUFDLENBQUNJLEVBQUU7SUFDVixJQUFJQSxHQUFHLE1BQU9BLElBQUksTUFBTSxHQUFHQSxLQUFLLEdBQUlEO0lBRXBDLE9BQU9BLEtBQUssSUFBSSxJQUFJQTtBQUN0QjtBQUdBOzs7O0NBSUMsR0FDRGpCLEVBQUVtQixTQUFTLEdBQUduQixFQUFFb0IsR0FBRyxHQUFHLFNBQVVaLENBQUM7SUFDL0IsT0FBT2EsT0FBTyxJQUFJLEVBQUUsSUFBSSxJQUFJLENBQUNqQixXQUFXLENBQUNJO0FBQzNDO0FBR0E7Ozs7Q0FJQyxHQUNEUixFQUFFc0Isa0JBQWtCLEdBQUd0QixFQUFFdUIsSUFBSSxHQUFHLFNBQVVmLENBQUM7SUFDekMsSUFBSUwsSUFBSSxJQUFJLEVBQ1ZxQixPQUFPckIsRUFBRUMsV0FBVztJQUN0QixPQUFPcUIsTUFBTUosT0FBT2xCLEdBQUcsSUFBSXFCLEtBQUtoQixJQUFJLEdBQUcsSUFBSWdCLEtBQUs3QyxTQUFTO0FBQzNEO0FBR0E7OztDQUdDLEdBQ0RxQixFQUFFMEIsTUFBTSxHQUFHMUIsRUFBRTJCLEVBQUUsR0FBRyxTQUFVbkIsQ0FBQztJQUMzQixPQUFPLENBQUMsSUFBSSxDQUFDRCxHQUFHLENBQUNDO0FBQ25CO0FBR0E7OztDQUdDLEdBQ0RSLEVBQUU0QixRQUFRLEdBQUc7SUFDWCxPQUFPQyxrQkFBa0IsSUFBSTtBQUMvQjtBQUdBOzs7O0NBSUMsR0FDRDdCLEVBQUU4QixXQUFXLEdBQUc5QixFQUFFK0IsRUFBRSxHQUFHLFNBQVV2QixDQUFDO0lBQ2hDLE9BQU8sSUFBSSxDQUFDRCxHQUFHLENBQUNDLEtBQUs7QUFDdkI7QUFHQTs7OztDQUlDLEdBQ0RSLEVBQUVnQyxvQkFBb0IsR0FBR2hDLEVBQUVpQyxHQUFHLEdBQUcsU0FBVXpCLENBQUM7SUFDMUMsT0FBTyxJQUFJLENBQUNELEdBQUcsQ0FBQ0MsTUFBTTtBQUN4QjtBQUdBOzs7Q0FHQyxHQUNEUixFQUFFa0MsU0FBUyxHQUFHbEMsRUFBRW1DLEtBQUssR0FBRztJQUN0QixPQUFPLElBQUksQ0FBQ3RCLENBQUMsR0FBRyxJQUFJLENBQUNDLENBQUMsQ0FBQ0MsTUFBTSxHQUFHO0FBQ2xDO0FBR0E7OztDQUdDLEdBQ0RmLEVBQUVvQyxVQUFVLEdBQUdwQyxFQUFFcUMsS0FBSyxHQUFHO0lBQ3ZCLE9BQU8sSUFBSSxDQUFDaEMsQ0FBQyxHQUFHO0FBQ2xCO0FBR0E7OztDQUdDLEdBQ0RMLEVBQUVzQyxVQUFVLEdBQUd0QyxFQUFFdUMsS0FBSyxHQUFHO0lBQ3ZCLE9BQU8sSUFBSSxDQUFDbEMsQ0FBQyxHQUFHO0FBQ2xCO0FBR0E7OztDQUdDLEdBQ0RMLEVBQUV3QyxNQUFNLEdBQUc7SUFDVCxPQUFPLElBQUksQ0FBQ25DLENBQUMsS0FBSztBQUNwQjtBQUdBOzs7Q0FHQyxHQUNETCxFQUFFeUMsUUFBUSxHQUFHekMsRUFBRTBDLEVBQUUsR0FBRyxTQUFVbEMsQ0FBQztJQUM3QixPQUFPLElBQUksQ0FBQ0QsR0FBRyxDQUFDQyxLQUFLO0FBQ3ZCO0FBR0E7OztDQUdDLEdBQ0RSLEVBQUUyQyxpQkFBaUIsR0FBRzNDLEVBQUU0QyxHQUFHLEdBQUcsU0FBVXBDLENBQUM7SUFDdkMsT0FBTyxJQUFJLENBQUNELEdBQUcsQ0FBQ0MsS0FBSztBQUN2QjtBQUdBOzs7Ozs7Ozs7Ozs7Q0FZQyxHQUNEUixFQUFFNkMsU0FBUyxHQUFHN0MsRUFBRThDLEdBQUcsR0FBRyxTQUFVQyxJQUFJO0lBQ2xDLElBQUlDLEdBQ0Y3QyxJQUFJLElBQUksRUFDUnFCLE9BQU9yQixFQUFFQyxXQUFXLEVBQ3BCNkMsS0FBS3pCLEtBQUs3QyxTQUFTLEVBQ25CdUUsTUFBTUQsS0FBSztJQUViLHNCQUFzQjtJQUN0QixJQUFJRixTQUFTLEtBQUssR0FBRztRQUNuQkEsT0FBTyxJQUFJdkIsS0FBSztJQUNsQixPQUFPO1FBQ0x1QixPQUFPLElBQUl2QixLQUFLdUI7UUFFaEIsbUJBQW1CO1FBQ25CLG1CQUFtQjtRQUNuQixtQkFBbUI7UUFDbkIsSUFBSUEsS0FBSzFDLENBQUMsR0FBRyxLQUFLMEMsS0FBS3BCLEVBQUUsQ0FBQ2hDLE1BQU0sTUFBTXdELE1BQU1qRSxlQUFlO0lBQzdEO0lBRUEsbUJBQW1CO0lBQ25CLHdCQUF3QjtJQUN4QixJQUFJaUIsRUFBRUUsQ0FBQyxHQUFHLEdBQUcsTUFBTThDLE1BQU1qRSxlQUFnQmlCLENBQUFBLEVBQUVFLENBQUMsR0FBRyxRQUFRLFdBQVU7SUFFakUsZ0JBQWdCO0lBQ2hCLElBQUlGLEVBQUV3QixFQUFFLENBQUNoQyxNQUFNLE9BQU8sSUFBSTZCLEtBQUs7SUFFL0J2QyxXQUFXO0lBQ1grRCxJQUFJM0IsT0FBTytCLEdBQUdqRCxHQUFHK0MsTUFBTUUsR0FBR0wsTUFBTUcsTUFBTUE7SUFDdENqRSxXQUFXO0lBRVgsT0FBT3dDLE1BQU11QixHQUFHQztBQUNsQjtBQUdBOzs7O0NBSUMsR0FDRGpELEVBQUVxRCxLQUFLLEdBQUdyRCxFQUFFc0QsR0FBRyxHQUFHLFNBQVU5QyxDQUFDO0lBQzNCLElBQUlMLElBQUksSUFBSTtJQUNaSyxJQUFJLElBQUlMLEVBQUVDLFdBQVcsQ0FBQ0k7SUFDdEIsT0FBT0wsRUFBRUUsQ0FBQyxJQUFJRyxFQUFFSCxDQUFDLEdBQUdrRCxTQUFTcEQsR0FBR0ssS0FBS2dELElBQUlyRCxHQUFJSyxDQUFBQSxFQUFFSCxDQUFDLEdBQUcsQ0FBQ0csRUFBRUgsQ0FBQyxFQUFFRyxDQUFBQTtBQUMzRDtBQUdBOzs7O0NBSUMsR0FDRFIsRUFBRXlELE1BQU0sR0FBR3pELEVBQUUwRCxHQUFHLEdBQUcsU0FBVWxELENBQUM7SUFDNUIsSUFBSW1ELEdBQ0Z4RCxJQUFJLElBQUksRUFDUnFCLE9BQU9yQixFQUFFQyxXQUFXLEVBQ3BCNkMsS0FBS3pCLEtBQUs3QyxTQUFTO0lBRXJCNkIsSUFBSSxJQUFJZ0IsS0FBS2hCO0lBRWIsY0FBYztJQUNkLElBQUksQ0FBQ0EsRUFBRUgsQ0FBQyxFQUFFLE1BQU04QyxNQUFNakUsZUFBZTtJQUVyQyxzQkFBc0I7SUFDdEIsSUFBSSxDQUFDaUIsRUFBRUUsQ0FBQyxFQUFFLE9BQU9vQixNQUFNLElBQUlELEtBQUtyQixJQUFJOEM7SUFFcEMsaURBQWlEO0lBQ2pEaEUsV0FBVztJQUNYMEUsSUFBSXRDLE9BQU9sQixHQUFHSyxHQUFHLEdBQUcsR0FBR29ELEtBQUssQ0FBQ3BEO0lBQzdCdkIsV0FBVztJQUVYLE9BQU9rQixFQUFFa0QsS0FBSyxDQUFDTTtBQUNqQjtBQUdBOzs7OztDQUtDLEdBQ0QzRCxFQUFFNkQsa0JBQWtCLEdBQUc3RCxFQUFFOEQsR0FBRyxHQUFHO0lBQzdCLE9BQU9BLElBQUksSUFBSTtBQUNqQjtBQUdBOzs7O0NBSUMsR0FDRDlELEVBQUUrRCxnQkFBZ0IsR0FBRy9ELEVBQUVvRCxFQUFFLEdBQUc7SUFDMUIsT0FBT0EsR0FBRyxJQUFJO0FBQ2hCO0FBR0E7Ozs7Q0FJQyxHQUNEcEQsRUFBRWdFLE9BQU8sR0FBR2hFLEVBQUVpRSxHQUFHLEdBQUc7SUFDbEIsSUFBSTlELElBQUksSUFBSSxJQUFJLENBQUNDLFdBQVcsQ0FBQyxJQUFJO0lBQ2pDRCxFQUFFRSxDQUFDLEdBQUcsQ0FBQ0YsRUFBRUUsQ0FBQyxJQUFJO0lBQ2QsT0FBT0Y7QUFDVDtBQUdBOzs7O0NBSUMsR0FDREgsRUFBRWtFLElBQUksR0FBR2xFLEVBQUV3RCxHQUFHLEdBQUcsU0FBVWhELENBQUM7SUFDMUIsSUFBSUwsSUFBSSxJQUFJO0lBQ1pLLElBQUksSUFBSUwsRUFBRUMsV0FBVyxDQUFDSTtJQUN0QixPQUFPTCxFQUFFRSxDQUFDLElBQUlHLEVBQUVILENBQUMsR0FBR21ELElBQUlyRCxHQUFHSyxLQUFLK0MsU0FBU3BELEdBQUlLLENBQUFBLEVBQUVILENBQUMsR0FBRyxDQUFDRyxFQUFFSCxDQUFDLEVBQUVHLENBQUFBO0FBQzNEO0FBR0E7Ozs7O0NBS0MsR0FDRFIsRUFBRXJCLFNBQVMsR0FBR3FCLEVBQUVtRSxFQUFFLEdBQUcsU0FBVUMsQ0FBQztJQUM5QixJQUFJdkQsR0FBR3NELElBQUlqRCxHQUNUZixJQUFJLElBQUk7SUFFVixJQUFJaUUsTUFBTSxLQUFLLEtBQUtBLE1BQU0sQ0FBQyxDQUFDQSxLQUFLQSxNQUFNLEtBQUtBLE1BQU0sR0FBRyxNQUFNakIsTUFBTWhFLGtCQUFrQmlGO0lBRW5GdkQsSUFBSWdCLGtCQUFrQjFCLEtBQUs7SUFDM0JlLElBQUlmLEVBQUVXLENBQUMsQ0FBQ0MsTUFBTSxHQUFHO0lBQ2pCb0QsS0FBS2pELElBQUlyQixXQUFXO0lBQ3BCcUIsSUFBSWYsRUFBRVcsQ0FBQyxDQUFDSSxFQUFFO0lBRVYsaUJBQWlCO0lBQ2pCLElBQUlBLEdBQUc7UUFFTCwwREFBMEQ7UUFDMUQsTUFBT0EsSUFBSSxNQUFNLEdBQUdBLEtBQUssR0FBSWlEO1FBRTdCLDhDQUE4QztRQUM5QyxJQUFLakQsSUFBSWYsRUFBRVcsQ0FBQyxDQUFDLEVBQUUsRUFBRUksS0FBSyxJQUFJQSxLQUFLLEdBQUlpRDtJQUNyQztJQUVBLE9BQU9DLEtBQUt2RCxJQUFJc0QsS0FBS3RELElBQUlzRDtBQUMzQjtBQUdBOzs7O0NBSUMsR0FDRG5FLEVBQUVxRSxVQUFVLEdBQUdyRSxFQUFFc0UsSUFBSSxHQUFHO0lBQ3RCLElBQUl6RCxHQUFHMEQsR0FBR3RCLElBQUlELEdBQUczQyxHQUFHbUUsR0FBR3RCLEtBQ3JCL0MsSUFBSSxJQUFJLEVBQ1JxQixPQUFPckIsRUFBRUMsV0FBVztJQUV0QixvQkFBb0I7SUFDcEIsSUFBSUQsRUFBRUUsQ0FBQyxHQUFHLEdBQUc7UUFDWCxJQUFJLENBQUNGLEVBQUVFLENBQUMsRUFBRSxPQUFPLElBQUltQixLQUFLO1FBRTFCLGlCQUFpQjtRQUNqQixNQUFNMkIsTUFBTWpFLGVBQWU7SUFDN0I7SUFFQTJCLElBQUlnQixrQkFBa0IxQjtJQUN0QmxCLFdBQVc7SUFFWCxvQkFBb0I7SUFDcEJvQixJQUFJZixLQUFLZ0YsSUFBSSxDQUFDLENBQUNuRTtJQUVmLGdDQUFnQztJQUNoQywwRUFBMEU7SUFDMUUsSUFBSUUsS0FBSyxLQUFLQSxLQUFLLElBQUksR0FBRztRQUN4QmtFLElBQUlFLGVBQWV0RSxFQUFFVyxDQUFDO1FBQ3RCLElBQUksQ0FBQ3lELEVBQUV4RCxNQUFNLEdBQUdGLENBQUFBLElBQUssS0FBSyxHQUFHMEQsS0FBSztRQUNsQ2xFLElBQUlmLEtBQUtnRixJQUFJLENBQUNDO1FBQ2QxRCxJQUFJeEIsVUFBVSxDQUFDd0IsSUFBSSxLQUFLLEtBQU1BLENBQUFBLElBQUksS0FBS0EsSUFBSTtRQUUzQyxJQUFJUixLQUFLLElBQUksR0FBRztZQUNka0UsSUFBSSxPQUFPMUQ7UUFDYixPQUFPO1lBQ0wwRCxJQUFJbEUsRUFBRXFFLGFBQWE7WUFDbkJILElBQUlBLEVBQUVJLEtBQUssQ0FBQyxHQUFHSixFQUFFSyxPQUFPLENBQUMsT0FBTyxLQUFLL0Q7UUFDdkM7UUFFQW1DLElBQUksSUFBSXhCLEtBQUsrQztJQUNmLE9BQU87UUFDTHZCLElBQUksSUFBSXhCLEtBQUtuQixFQUFFd0UsUUFBUTtJQUN6QjtJQUVBNUIsS0FBS3pCLEtBQUs3QyxTQUFTO0lBQ25CMEIsSUFBSTZDLE1BQU1ELEtBQUs7SUFFZiw0QkFBNEI7SUFDNUIsT0FBUztRQUNQdUIsSUFBSXhCO1FBQ0pBLElBQUl3QixFQUFFTixJQUFJLENBQUM3QyxPQUFPbEIsR0FBR3FFLEdBQUd0QixNQUFNLElBQUlVLEtBQUssQ0FBQztRQUV4QyxJQUFJYSxlQUFlRCxFQUFFMUQsQ0FBQyxFQUFFNkQsS0FBSyxDQUFDLEdBQUd6QixTQUFTLENBQUNxQixJQUFJRSxlQUFlekIsRUFBRWxDLENBQUMsR0FBRzZELEtBQUssQ0FBQyxHQUFHekIsTUFBTTtZQUNqRnFCLElBQUlBLEVBQUVJLEtBQUssQ0FBQ3pCLE1BQU0sR0FBR0EsTUFBTTtZQUUzQix1RkFBdUY7WUFDdkYsc0VBQXNFO1lBQ3RFLElBQUk3QyxLQUFLNkMsT0FBT3FCLEtBQUssUUFBUTtnQkFFM0IseUZBQXlGO2dCQUN6RiwrQkFBK0I7Z0JBQy9COUMsTUFBTStDLEdBQUd2QixLQUFLLEdBQUc7Z0JBRWpCLElBQUl1QixFQUFFWixLQUFLLENBQUNZLEdBQUc3QyxFQUFFLENBQUN4QixJQUFJO29CQUNwQjZDLElBQUl3QjtvQkFDSjtnQkFDRjtZQUNGLE9BQU8sSUFBSUQsS0FBSyxRQUFRO2dCQUN0QjtZQUNGO1lBRUFyQixPQUFPO1FBQ1Q7SUFDRjtJQUVBakUsV0FBVztJQUVYLE9BQU93QyxNQUFNdUIsR0FBR0M7QUFDbEI7QUFHQTs7OztDQUlDLEdBQ0RqRCxFQUFFNEQsS0FBSyxHQUFHNUQsRUFBRThFLEdBQUcsR0FBRyxTQUFVdEUsQ0FBQztJQUMzQixJQUFJdUUsT0FBT2xFLEdBQUdKLEdBQUd1RSxHQUFHaEMsR0FBR2lDLElBQUlULEdBQUc3RCxLQUFLQyxLQUNqQ1QsSUFBSSxJQUFJLEVBQ1JxQixPQUFPckIsRUFBRUMsV0FBVyxFQUNwQjhFLEtBQUsvRSxFQUFFVyxDQUFDLEVBQ1JxRSxLQUFLLENBQUMzRSxJQUFJLElBQUlnQixLQUFLaEIsRUFBQyxFQUFHTSxDQUFDO0lBRTFCLDJCQUEyQjtJQUMzQixJQUFJLENBQUNYLEVBQUVFLENBQUMsSUFBSSxDQUFDRyxFQUFFSCxDQUFDLEVBQUUsT0FBTyxJQUFJbUIsS0FBSztJQUVsQ2hCLEVBQUVILENBQUMsSUFBSUYsRUFBRUUsQ0FBQztJQUNWUSxJQUFJVixFQUFFVSxDQUFDLEdBQUdMLEVBQUVLLENBQUM7SUFDYkYsTUFBTXVFLEdBQUduRSxNQUFNO0lBQ2ZILE1BQU11RSxHQUFHcEUsTUFBTTtJQUVmLHdDQUF3QztJQUN4QyxJQUFJSixNQUFNQyxLQUFLO1FBQ2JvQyxJQUFJa0M7UUFDSkEsS0FBS0M7UUFDTEEsS0FBS25DO1FBQ0xpQyxLQUFLdEU7UUFDTEEsTUFBTUM7UUFDTkEsTUFBTXFFO0lBQ1I7SUFFQSwwQ0FBMEM7SUFDMUNqQyxJQUFJLEVBQUU7SUFDTmlDLEtBQUt0RSxNQUFNQztJQUNYLElBQUtILElBQUl3RSxJQUFJeEUsS0FBTXVDLEVBQUVvQyxJQUFJLENBQUM7SUFFMUIsWUFBWTtJQUNaLElBQUszRSxJQUFJRyxLQUFLLEVBQUVILEtBQUssR0FBSTtRQUN2QnNFLFFBQVE7UUFDUixJQUFLQyxJQUFJckUsTUFBTUYsR0FBR3VFLElBQUl2RSxHQUFJO1lBQ3hCK0QsSUFBSXhCLENBQUMsQ0FBQ2dDLEVBQUUsR0FBR0csRUFBRSxDQUFDMUUsRUFBRSxHQUFHeUUsRUFBRSxDQUFDRixJQUFJdkUsSUFBSSxFQUFFLEdBQUdzRTtZQUNuQy9CLENBQUMsQ0FBQ2dDLElBQUksR0FBR1IsSUFBSTVFLE9BQU87WUFDcEJtRixRQUFRUCxJQUFJNUUsT0FBTztRQUNyQjtRQUVBb0QsQ0FBQyxDQUFDZ0MsRUFBRSxHQUFHLENBQUNoQyxDQUFDLENBQUNnQyxFQUFFLEdBQUdELEtBQUksSUFBS25GLE9BQU87SUFDakM7SUFFQSx5QkFBeUI7SUFDekIsTUFBTyxDQUFDb0QsQ0FBQyxDQUFDLEVBQUVpQyxHQUFHLEVBQUdqQyxFQUFFcUMsR0FBRztJQUV2QixJQUFJTixPQUFPLEVBQUVsRTtTQUNSbUMsRUFBRXNDLEtBQUs7SUFFWjlFLEVBQUVNLENBQUMsR0FBR2tDO0lBQ054QyxFQUFFSyxDQUFDLEdBQUdBO0lBRU4sT0FBTzVCLFdBQVd3QyxNQUFNakIsR0FBR2dCLEtBQUs3QyxTQUFTLElBQUk2QjtBQUMvQztBQUdBOzs7Ozs7Ozs7Q0FTQyxHQUNEUixFQUFFdUYsZUFBZSxHQUFHdkYsRUFBRXdGLElBQUksR0FBRyxTQUFVdkUsRUFBRSxFQUFFd0UsRUFBRTtJQUMzQyxJQUFJdEYsSUFBSSxJQUFJLEVBQ1ZxQixPQUFPckIsRUFBRUMsV0FBVztJQUV0QkQsSUFBSSxJQUFJcUIsS0FBS3JCO0lBQ2IsSUFBSWMsT0FBTyxLQUFLLEdBQUcsT0FBT2Q7SUFFMUJ1RixXQUFXekUsSUFBSSxHQUFHeEM7SUFFbEIsSUFBSWdILE9BQU8sS0FBSyxHQUFHQSxLQUFLakUsS0FBSzVDLFFBQVE7U0FDaEM4RyxXQUFXRCxJQUFJLEdBQUc7SUFFdkIsT0FBT2hFLE1BQU10QixHQUFHYyxLQUFLWSxrQkFBa0IxQixLQUFLLEdBQUdzRjtBQUNqRDtBQUdBOzs7Ozs7O0NBT0MsR0FDRHpGLEVBQUUwRSxhQUFhLEdBQUcsU0FBVXpELEVBQUUsRUFBRXdFLEVBQUU7SUFDaEMsSUFBSUUsS0FDRnhGLElBQUksSUFBSSxFQUNScUIsT0FBT3JCLEVBQUVDLFdBQVc7SUFFdEIsSUFBSWEsT0FBTyxLQUFLLEdBQUc7UUFDakIwRSxNQUFNZCxTQUFTMUUsR0FBRztJQUNwQixPQUFPO1FBQ0x1RixXQUFXekUsSUFBSSxHQUFHeEM7UUFFbEIsSUFBSWdILE9BQU8sS0FBSyxHQUFHQSxLQUFLakUsS0FBSzVDLFFBQVE7YUFDaEM4RyxXQUFXRCxJQUFJLEdBQUc7UUFFdkJ0RixJQUFJc0IsTUFBTSxJQUFJRCxLQUFLckIsSUFBSWMsS0FBSyxHQUFHd0U7UUFDL0JFLE1BQU1kLFNBQVMxRSxHQUFHLE1BQU1jLEtBQUs7SUFDL0I7SUFFQSxPQUFPMEU7QUFDVDtBQUdBOzs7Ozs7Ozs7Ozs7Ozs7Q0FlQyxHQUNEM0YsRUFBRTRGLE9BQU8sR0FBRyxTQUFVM0UsRUFBRSxFQUFFd0UsRUFBRTtJQUMxQixJQUFJRSxLQUFLbkYsR0FDUEwsSUFBSSxJQUFJLEVBQ1JxQixPQUFPckIsRUFBRUMsV0FBVztJQUV0QixJQUFJYSxPQUFPLEtBQUssR0FBRyxPQUFPNEQsU0FBUzFFO0lBRW5DdUYsV0FBV3pFLElBQUksR0FBR3hDO0lBRWxCLElBQUlnSCxPQUFPLEtBQUssR0FBR0EsS0FBS2pFLEtBQUs1QyxRQUFRO1NBQ2hDOEcsV0FBV0QsSUFBSSxHQUFHO0lBRXZCakYsSUFBSWlCLE1BQU0sSUFBSUQsS0FBS3JCLElBQUljLEtBQUtZLGtCQUFrQjFCLEtBQUssR0FBR3NGO0lBQ3RERSxNQUFNZCxTQUFTckUsRUFBRU4sR0FBRyxJQUFJLE9BQU9lLEtBQUtZLGtCQUFrQnJCLEtBQUs7SUFFM0Qsc0ZBQXNGO0lBQ3RGLG9DQUFvQztJQUNwQyxPQUFPTCxFQUFFa0MsS0FBSyxNQUFNLENBQUNsQyxFQUFFcUMsTUFBTSxLQUFLLE1BQU1tRCxNQUFNQTtBQUNoRDtBQUdBOzs7O0NBSUMsR0FDRDNGLEVBQUU2RixTQUFTLEdBQUc3RixFQUFFOEYsS0FBSyxHQUFHO0lBQ3RCLElBQUkzRixJQUFJLElBQUksRUFDVnFCLE9BQU9yQixFQUFFQyxXQUFXO0lBQ3RCLE9BQU9xQixNQUFNLElBQUlELEtBQUtyQixJQUFJMEIsa0JBQWtCMUIsS0FBSyxHQUFHcUIsS0FBSzVDLFFBQVE7QUFDbkU7QUFHQTs7O0NBR0MsR0FDRG9CLEVBQUUrRixRQUFRLEdBQUc7SUFDWCxPQUFPLENBQUMsSUFBSTtBQUNkO0FBR0E7Ozs7Ozs7Ozs7OztDQVlDLEdBQ0QvRixFQUFFZ0csT0FBTyxHQUFHaEcsRUFBRVAsR0FBRyxHQUFHLFNBQVVlLENBQUM7SUFDN0IsSUFBSUssR0FBR21FLEdBQUcvQixJQUFJRCxHQUFHaUQsTUFBTUMsUUFDckIvRixJQUFJLElBQUksRUFDUnFCLE9BQU9yQixFQUFFQyxXQUFXLEVBQ3BCK0YsUUFBUSxJQUNSQyxLQUFLLENBQUU1RixDQUFBQSxJQUFJLElBQUlnQixLQUFLaEIsRUFBQztJQUV2QixnQkFBZ0I7SUFDaEIsSUFBSSxDQUFDQSxFQUFFSCxDQUFDLEVBQUUsT0FBTyxJQUFJbUIsS0FBSzdCO0lBRTFCUSxJQUFJLElBQUlxQixLQUFLckI7SUFFYixvQkFBb0I7SUFDcEIsMkJBQTJCO0lBQzNCLElBQUksQ0FBQ0EsRUFBRUUsQ0FBQyxFQUFFO1FBQ1IsSUFBSUcsRUFBRUgsQ0FBQyxHQUFHLEdBQUcsTUFBTThDLE1BQU1qRSxlQUFlO1FBQ3hDLE9BQU9pQjtJQUNUO0lBRUEsZ0JBQWdCO0lBQ2hCLElBQUlBLEVBQUV3QixFQUFFLENBQUNoQyxNQUFNLE9BQU9RO0lBRXRCOEMsS0FBS3pCLEtBQUs3QyxTQUFTO0lBRW5CLGdCQUFnQjtJQUNoQixJQUFJNkIsRUFBRW1CLEVBQUUsQ0FBQ2hDLE1BQU0sT0FBTzhCLE1BQU10QixHQUFHOEM7SUFFL0JwQyxJQUFJTCxFQUFFSyxDQUFDO0lBQ1BtRSxJQUFJeEUsRUFBRU0sQ0FBQyxDQUFDQyxNQUFNLEdBQUc7SUFDakJtRixTQUFTckYsS0FBS21FO0lBQ2RpQixPQUFPOUYsRUFBRUUsQ0FBQztJQUVWLElBQUksQ0FBQzZGLFFBQVE7UUFFWCxrQ0FBa0M7UUFDbEMsSUFBSUQsT0FBTyxHQUFHLE1BQU05QyxNQUFNakUsZUFBZTtJQUUzQywwRUFBMEU7SUFDMUUsT0FBTyxJQUFJLENBQUM4RixJQUFJb0IsS0FBSyxJQUFJLENBQUNBLEtBQUtBLEVBQUMsS0FBTXRHLGtCQUFrQjtRQUN0RGtELElBQUksSUFBSXhCLEtBQUs3QjtRQUViLHNEQUFzRDtRQUN0RCw2REFBNkQ7UUFDN0RrQixJQUFJdkIsS0FBSytHLElBQUksQ0FBQ3BELEtBQUtwRCxXQUFXO1FBRTlCWixXQUFXO1FBRVgsT0FBUztZQUNQLElBQUkrRixJQUFJLEdBQUc7Z0JBQ1RoQyxJQUFJQSxFQUFFWSxLQUFLLENBQUN6RDtnQkFDWm1HLFNBQVN0RCxFQUFFbEMsQ0FBQyxFQUFFRDtZQUNoQjtZQUVBbUUsSUFBSTNGLFVBQVUyRixJQUFJO1lBQ2xCLElBQUlBLE1BQU0sR0FBRztZQUViN0UsSUFBSUEsRUFBRXlELEtBQUssQ0FBQ3pEO1lBQ1ptRyxTQUFTbkcsRUFBRVcsQ0FBQyxFQUFFRDtRQUNoQjtRQUVBNUIsV0FBVztRQUVYLE9BQU91QixFQUFFSCxDQUFDLEdBQUcsSUFBSSxJQUFJbUIsS0FBSzdCLEtBQUt5QixHQUFHLENBQUM0QixLQUFLdkIsTUFBTXVCLEdBQUdDO0lBQ25EO0lBRUEsOEVBQThFO0lBQzlFZ0QsT0FBT0EsT0FBTyxLQUFLekYsRUFBRU0sQ0FBQyxDQUFDeEIsS0FBS2lILEdBQUcsQ0FBQzFGLEdBQUdtRSxHQUFHLEdBQUcsSUFBSSxDQUFDLElBQUk7SUFFbEQ3RSxFQUFFRSxDQUFDLEdBQUc7SUFDTnBCLFdBQVc7SUFDWCtELElBQUl4QyxFQUFFb0QsS0FBSyxDQUFDUixHQUFHakQsR0FBRzhDLEtBQUtrRDtJQUN2QmxILFdBQVc7SUFDWCtELElBQUljLElBQUlkO0lBQ1JBLEVBQUUzQyxDQUFDLEdBQUc0RjtJQUVOLE9BQU9qRDtBQUNUO0FBR0E7Ozs7Ozs7Ozs7Q0FVQyxHQUNEaEQsRUFBRXdHLFdBQVcsR0FBRyxTQUFVckMsRUFBRSxFQUFFc0IsRUFBRTtJQUM5QixJQUFJNUUsR0FBRzhFLEtBQ0x4RixJQUFJLElBQUksRUFDUnFCLE9BQU9yQixFQUFFQyxXQUFXO0lBRXRCLElBQUkrRCxPQUFPLEtBQUssR0FBRztRQUNqQnRELElBQUlnQixrQkFBa0IxQjtRQUN0QndGLE1BQU1kLFNBQVMxRSxHQUFHVSxLQUFLVyxLQUFLM0MsUUFBUSxJQUFJZ0MsS0FBS1csS0FBSzFDLFFBQVE7SUFDNUQsT0FBTztRQUNMNEcsV0FBV3ZCLElBQUksR0FBRzFGO1FBRWxCLElBQUlnSCxPQUFPLEtBQUssR0FBR0EsS0FBS2pFLEtBQUs1QyxRQUFRO2FBQ2hDOEcsV0FBV0QsSUFBSSxHQUFHO1FBRXZCdEYsSUFBSXNCLE1BQU0sSUFBSUQsS0FBS3JCLElBQUlnRSxJQUFJc0I7UUFDM0I1RSxJQUFJZ0Isa0JBQWtCMUI7UUFDdEJ3RixNQUFNZCxTQUFTMUUsR0FBR2dFLE1BQU10RCxLQUFLQSxLQUFLVyxLQUFLM0MsUUFBUSxFQUFFc0Y7SUFDbkQ7SUFFQSxPQUFPd0I7QUFDVDtBQUdBOzs7Ozs7OztDQVFDLEdBQ0QzRixFQUFFeUcsbUJBQW1CLEdBQUd6RyxFQUFFMEcsSUFBSSxHQUFHLFNBQVV2QyxFQUFFLEVBQUVzQixFQUFFO0lBQy9DLElBQUl0RixJQUFJLElBQUksRUFDVnFCLE9BQU9yQixFQUFFQyxXQUFXO0lBRXRCLElBQUkrRCxPQUFPLEtBQUssR0FBRztRQUNqQkEsS0FBSzNDLEtBQUs3QyxTQUFTO1FBQ25COEcsS0FBS2pFLEtBQUs1QyxRQUFRO0lBQ3BCLE9BQU87UUFDTDhHLFdBQVd2QixJQUFJLEdBQUcxRjtRQUVsQixJQUFJZ0gsT0FBTyxLQUFLLEdBQUdBLEtBQUtqRSxLQUFLNUMsUUFBUTthQUNoQzhHLFdBQVdELElBQUksR0FBRztJQUN6QjtJQUVBLE9BQU9oRSxNQUFNLElBQUlELEtBQUtyQixJQUFJZ0UsSUFBSXNCO0FBQ2hDO0FBR0E7Ozs7OztDQU1DLEdBQ0R6RixFQUFFNkUsUUFBUSxHQUFHN0UsRUFBRTJHLE9BQU8sR0FBRzNHLEVBQUU0RyxHQUFHLEdBQUc1RyxFQUFFNkcsTUFBTSxHQUFHN0csQ0FBQyxDQUFDOEcsT0FBT0MsR0FBRyxDQUFDLDhCQUE4QixHQUFHO0lBQ3hGLElBQUk1RyxJQUFJLElBQUksRUFDVlUsSUFBSWdCLGtCQUFrQjFCLElBQ3RCcUIsT0FBT3JCLEVBQUVDLFdBQVc7SUFFdEIsT0FBT3lFLFNBQVMxRSxHQUFHVSxLQUFLVyxLQUFLM0MsUUFBUSxJQUFJZ0MsS0FBS1csS0FBSzFDLFFBQVE7QUFDN0Q7QUFHQSx3RkFBd0Y7QUFHeEY7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztDQXFCQyxHQUdELFNBQVMwRSxJQUFJckQsQ0FBQyxFQUFFSyxDQUFDO0lBQ2YsSUFBSXVFLE9BQU9qRSxHQUFHRCxHQUFHSixHQUFHdUUsR0FBR2dDLEtBQUs5QixJQUFJQyxJQUM5QjNELE9BQU9yQixFQUFFQyxXQUFXLEVBQ3BCNkMsS0FBS3pCLEtBQUs3QyxTQUFTO0lBRXJCLHVCQUF1QjtJQUN2QixJQUFJLENBQUN3QixFQUFFRSxDQUFDLElBQUksQ0FBQ0csRUFBRUgsQ0FBQyxFQUFFO1FBRWhCLHlCQUF5QjtRQUN6Qiw2QkFBNkI7UUFDN0IsSUFBSSxDQUFDRyxFQUFFSCxDQUFDLEVBQUVHLElBQUksSUFBSWdCLEtBQUtyQjtRQUN2QixPQUFPbEIsV0FBV3dDLE1BQU1qQixHQUFHeUMsTUFBTXpDO0lBQ25DO0lBRUEwRSxLQUFLL0UsRUFBRVcsQ0FBQztJQUNScUUsS0FBSzNFLEVBQUVNLENBQUM7SUFFUiwyREFBMkQ7SUFFM0RrRSxJQUFJN0UsRUFBRVUsQ0FBQztJQUNQQSxJQUFJTCxFQUFFSyxDQUFDO0lBQ1BxRSxLQUFLQSxHQUFHUCxLQUFLO0lBQ2JsRSxJQUFJdUUsSUFBSW5FO0lBRVIsa0NBQWtDO0lBQ2xDLElBQUlKLEdBQUc7UUFDTCxJQUFJQSxJQUFJLEdBQUc7WUFDVEssSUFBSW9FO1lBQ0p6RSxJQUFJLENBQUNBO1lBQ0x1RyxNQUFNN0IsR0FBR3BFLE1BQU07UUFDakIsT0FBTztZQUNMRCxJQUFJcUU7WUFDSnRFLElBQUltRTtZQUNKZ0MsTUFBTTlCLEdBQUduRSxNQUFNO1FBQ2pCO1FBRUEsd0VBQXdFO1FBQ3hFaUUsSUFBSTFGLEtBQUsrRyxJQUFJLENBQUNwRCxLQUFLcEQ7UUFDbkJtSCxNQUFNaEMsSUFBSWdDLE1BQU1oQyxJQUFJLElBQUlnQyxNQUFNO1FBRTlCLElBQUl2RyxJQUFJdUcsS0FBSztZQUNYdkcsSUFBSXVHO1lBQ0psRyxFQUFFQyxNQUFNLEdBQUc7UUFDYjtRQUVBLHFGQUFxRjtRQUNyRkQsRUFBRW1HLE9BQU87UUFDVCxNQUFPeEcsS0FBTUssRUFBRXNFLElBQUksQ0FBQztRQUNwQnRFLEVBQUVtRyxPQUFPO0lBQ1g7SUFFQUQsTUFBTTlCLEdBQUduRSxNQUFNO0lBQ2ZOLElBQUkwRSxHQUFHcEUsTUFBTTtJQUViLDRFQUE0RTtJQUM1RSxJQUFJaUcsTUFBTXZHLElBQUksR0FBRztRQUNmQSxJQUFJdUc7UUFDSmxHLElBQUlxRTtRQUNKQSxLQUFLRDtRQUNMQSxLQUFLcEU7SUFDUDtJQUVBLDBGQUEwRjtJQUMxRixJQUFLaUUsUUFBUSxHQUFHdEUsR0FBSTtRQUNsQnNFLFFBQVEsQ0FBQ0csRUFBRSxDQUFDLEVBQUV6RSxFQUFFLEdBQUd5RSxFQUFFLENBQUN6RSxFQUFFLEdBQUcwRSxFQUFFLENBQUMxRSxFQUFFLEdBQUdzRSxLQUFJLElBQUtuRixPQUFPO1FBQ25Ec0YsRUFBRSxDQUFDekUsRUFBRSxJQUFJYjtJQUNYO0lBRUEsSUFBSW1GLE9BQU87UUFDVEcsR0FBR2dDLE9BQU8sQ0FBQ25DO1FBQ1gsRUFBRWxFO0lBQ0o7SUFFQSx5QkFBeUI7SUFDekIsNkRBQTZEO0lBQzdELElBQUttRyxNQUFNOUIsR0FBR25FLE1BQU0sRUFBRW1FLEVBQUUsQ0FBQyxFQUFFOEIsSUFBSSxJQUFJLEdBQUk5QixHQUFHRyxHQUFHO0lBRTdDN0UsRUFBRU0sQ0FBQyxHQUFHb0U7SUFDTjFFLEVBQUVLLENBQUMsR0FBR0E7SUFFTixPQUFPNUIsV0FBV3dDLE1BQU1qQixHQUFHeUMsTUFBTXpDO0FBQ25DO0FBR0EsU0FBU2tGLFdBQVdqRixDQUFDLEVBQUUwRyxHQUFHLEVBQUVaLEdBQUc7SUFDN0IsSUFBSTlGLE1BQU0sQ0FBQyxDQUFDQSxLQUFLQSxJQUFJMEcsT0FBTzFHLElBQUk4RixLQUFLO1FBQ25DLE1BQU1wRCxNQUFNaEUsa0JBQWtCc0I7SUFDaEM7QUFDRjtBQUdBLFNBQVNnRSxlQUFlM0QsQ0FBQztJQUN2QixJQUFJTCxHQUFHdUUsR0FBR29DLElBQ1JDLGtCQUFrQnZHLEVBQUVDLE1BQU0sR0FBRyxHQUM3QjRFLE1BQU0sSUFDTnpFLElBQUlKLENBQUMsQ0FBQyxFQUFFO0lBRVYsSUFBSXVHLGtCQUFrQixHQUFHO1FBQ3ZCMUIsT0FBT3pFO1FBQ1AsSUFBS1QsSUFBSSxHQUFHQSxJQUFJNEcsaUJBQWlCNUcsSUFBSztZQUNwQzJHLEtBQUt0RyxDQUFDLENBQUNMLEVBQUUsR0FBRztZQUNadUUsSUFBSW5GLFdBQVd1SCxHQUFHckcsTUFBTTtZQUN4QixJQUFJaUUsR0FBR1csT0FBTzJCLGNBQWN0QztZQUM1QlcsT0FBT3lCO1FBQ1Q7UUFFQWxHLElBQUlKLENBQUMsQ0FBQ0wsRUFBRTtRQUNSMkcsS0FBS2xHLElBQUk7UUFDVDhELElBQUluRixXQUFXdUgsR0FBR3JHLE1BQU07UUFDeEIsSUFBSWlFLEdBQUdXLE9BQU8yQixjQUFjdEM7SUFDOUIsT0FBTyxJQUFJOUQsTUFBTSxHQUFHO1FBQ2xCLE9BQU87SUFDVDtJQUVBLG1DQUFtQztJQUNuQyxNQUFPQSxJQUFJLE9BQU8sR0FBSUEsS0FBSztJQUUzQixPQUFPeUUsTUFBTXpFO0FBQ2Y7QUFHQSxJQUFJRyxTQUFTO0lBRVgsdURBQXVEO0lBQ3ZELFNBQVNrRyxnQkFBZ0JwSCxDQUFDLEVBQUU2RSxDQUFDO1FBQzNCLElBQUl3QyxNQUNGekMsUUFBUSxHQUNSdEUsSUFBSU4sRUFBRVksTUFBTTtRQUVkLElBQUtaLElBQUlBLEVBQUV3RSxLQUFLLElBQUlsRSxLQUFNO1lBQ3hCK0csT0FBT3JILENBQUMsQ0FBQ00sRUFBRSxHQUFHdUUsSUFBSUQ7WUFDbEI1RSxDQUFDLENBQUNNLEVBQUUsR0FBRytHLE9BQU81SCxPQUFPO1lBQ3JCbUYsUUFBUXlDLE9BQU81SCxPQUFPO1FBQ3hCO1FBRUEsSUFBSW1GLE9BQU81RSxFQUFFK0csT0FBTyxDQUFDbkM7UUFFckIsT0FBTzVFO0lBQ1Q7SUFFQSxTQUFTc0gsUUFBUUMsQ0FBQyxFQUFFQyxDQUFDLEVBQUVDLEVBQUUsRUFBRUMsRUFBRTtRQUMzQixJQUFJcEgsR0FBR3VDO1FBRVAsSUFBSTRFLE1BQU1DLElBQUk7WUFDWjdFLElBQUk0RSxLQUFLQyxLQUFLLElBQUksQ0FBQztRQUNyQixPQUFPO1lBQ0wsSUFBS3BILElBQUl1QyxJQUFJLEdBQUd2QyxJQUFJbUgsSUFBSW5ILElBQUs7Z0JBQzNCLElBQUlpSCxDQUFDLENBQUNqSCxFQUFFLElBQUlrSCxDQUFDLENBQUNsSCxFQUFFLEVBQUU7b0JBQ2hCdUMsSUFBSTBFLENBQUMsQ0FBQ2pILEVBQUUsR0FBR2tILENBQUMsQ0FBQ2xILEVBQUUsR0FBRyxJQUFJLENBQUM7b0JBQ3ZCO2dCQUNGO1lBQ0Y7UUFDRjtRQUVBLE9BQU91QztJQUNUO0lBRUEsU0FBU08sU0FBU21FLENBQUMsRUFBRUMsQ0FBQyxFQUFFQyxFQUFFO1FBQ3hCLElBQUluSCxJQUFJO1FBRVIscUJBQXFCO1FBQ3JCLE1BQU9tSCxNQUFPO1lBQ1pGLENBQUMsQ0FBQ0UsR0FBRyxJQUFJbkg7WUFDVEEsSUFBSWlILENBQUMsQ0FBQ0UsR0FBRyxHQUFHRCxDQUFDLENBQUNDLEdBQUcsR0FBRyxJQUFJO1lBQ3hCRixDQUFDLENBQUNFLEdBQUcsR0FBR25ILElBQUliLE9BQU84SCxDQUFDLENBQUNFLEdBQUcsR0FBR0QsQ0FBQyxDQUFDQyxHQUFHO1FBQ2xDO1FBRUEsd0JBQXdCO1FBQ3hCLE1BQU8sQ0FBQ0YsQ0FBQyxDQUFDLEVBQUUsSUFBSUEsRUFBRTNHLE1BQU0sR0FBRyxHQUFJMkcsRUFBRXBDLEtBQUs7SUFDeEM7SUFFQSxPQUFPLFNBQVVuRixDQUFDLEVBQUVLLENBQUMsRUFBRXlDLEVBQUUsRUFBRWhDLEVBQUU7UUFDM0IsSUFBSVYsS0FBS00sR0FBR0osR0FBR3VFLEdBQUc4QyxNQUFNQyxPQUFPcEUsR0FBR3FFLElBQUlDLEtBQUtDLE1BQU1DLE1BQU1oRSxJQUFJSyxHQUFHNEQsSUFBSUMsSUFBSUMsS0FBS0MsSUFBSUMsSUFDN0VoSCxPQUFPckIsRUFBRUMsV0FBVyxFQUNwQjZGLE9BQU85RixFQUFFRSxDQUFDLElBQUlHLEVBQUVILENBQUMsR0FBRyxJQUFJLENBQUMsR0FDekI2RSxLQUFLL0UsRUFBRVcsQ0FBQyxFQUNScUUsS0FBSzNFLEVBQUVNLENBQUM7UUFFVixZQUFZO1FBQ1osSUFBSSxDQUFDWCxFQUFFRSxDQUFDLEVBQUUsT0FBTyxJQUFJbUIsS0FBS3JCO1FBQzFCLElBQUksQ0FBQ0ssRUFBRUgsQ0FBQyxFQUFFLE1BQU04QyxNQUFNakUsZUFBZTtRQUVyQzJCLElBQUlWLEVBQUVVLENBQUMsR0FBR0wsRUFBRUssQ0FBQztRQUNiMEgsS0FBS3BELEdBQUdwRSxNQUFNO1FBQ2RzSCxLQUFLbkQsR0FBR25FLE1BQU07UUFDZDRDLElBQUksSUFBSW5DLEtBQUt5RTtRQUNiK0IsS0FBS3JFLEVBQUU3QyxDQUFDLEdBQUcsRUFBRTtRQUViLDBDQUEwQztRQUMxQyxJQUFLTCxJQUFJLEdBQUcwRSxFQUFFLENBQUMxRSxFQUFFLElBQUt5RSxDQUFBQSxFQUFFLENBQUN6RSxFQUFFLElBQUksSUFBTSxFQUFFQTtRQUN2QyxJQUFJMEUsRUFBRSxDQUFDMUUsRUFBRSxHQUFJeUUsQ0FBQUEsRUFBRSxDQUFDekUsRUFBRSxJQUFJLElBQUksRUFBRUk7UUFFNUIsSUFBSW9DLE1BQU0sTUFBTTtZQUNka0IsS0FBS2xCLEtBQUt6QixLQUFLN0MsU0FBUztRQUMxQixPQUFPLElBQUlzQyxJQUFJO1lBQ2JrRCxLQUFLbEIsS0FBTXBCLENBQUFBLGtCQUFrQjFCLEtBQUswQixrQkFBa0JyQixFQUFDLElBQUs7UUFDNUQsT0FBTztZQUNMMkQsS0FBS2xCO1FBQ1A7UUFFQSxJQUFJa0IsS0FBSyxHQUFHLE9BQU8sSUFBSTNDLEtBQUs7UUFFNUIsb0VBQW9FO1FBQ3BFMkMsS0FBS0EsS0FBS3RFLFdBQVcsSUFBSTtRQUN6QlksSUFBSTtRQUVKLGdCQUFnQjtRQUNoQixJQUFJOEgsTUFBTSxHQUFHO1lBQ1h2RCxJQUFJO1lBQ0pHLEtBQUtBLEVBQUUsQ0FBQyxFQUFFO1lBQ1ZoQjtZQUVBLGtCQUFrQjtZQUNsQixNQUFPLENBQUMxRCxJQUFJNEgsTUFBTXJELENBQUFBLEtBQU1iLE1BQU0xRCxJQUFLO2dCQUNqQytELElBQUlRLElBQUlwRixPQUFRc0YsQ0FBQUEsRUFBRSxDQUFDekUsRUFBRSxJQUFJO2dCQUN6QnVILEVBQUUsQ0FBQ3ZILEVBQUUsR0FBRytELElBQUlXLEtBQUs7Z0JBQ2pCSCxJQUFJUixJQUFJVyxLQUFLO1lBQ2Y7UUFFRixpQkFBaUI7UUFDakIsT0FBTztZQUVMLGdFQUFnRTtZQUNoRUgsSUFBSXBGLE9BQVF1RixDQUFBQSxFQUFFLENBQUMsRUFBRSxHQUFHLEtBQUs7WUFFekIsSUFBSUgsSUFBSSxHQUFHO2dCQUNURyxLQUFLb0MsZ0JBQWdCcEMsSUFBSUg7Z0JBQ3pCRSxLQUFLcUMsZ0JBQWdCckMsSUFBSUY7Z0JBQ3pCdUQsS0FBS3BELEdBQUdwRSxNQUFNO2dCQUNkc0gsS0FBS25ELEdBQUduRSxNQUFNO1lBQ2hCO1lBRUFxSCxLQUFLRztZQUNMTixNQUFNL0MsR0FBR1AsS0FBSyxDQUFDLEdBQUc0RDtZQUNsQkwsT0FBT0QsSUFBSWxILE1BQU07WUFFakIsa0RBQWtEO1lBQ2xELE1BQU9tSCxPQUFPSyxJQUFLTixHQUFHLENBQUNDLE9BQU8sR0FBRztZQUVqQ00sS0FBS3JELEdBQUdSLEtBQUs7WUFDYjZELEdBQUd0QixPQUFPLENBQUM7WUFDWG9CLE1BQU1uRCxFQUFFLENBQUMsRUFBRTtZQUVYLElBQUlBLEVBQUUsQ0FBQyxFQUFFLElBQUl2RixPQUFPLEdBQUcsRUFBRTBJO1lBRXpCLEdBQUc7Z0JBQ0R0RCxJQUFJO2dCQUVKLGlDQUFpQztnQkFDakN6RSxNQUFNa0gsUUFBUXRDLElBQUk4QyxLQUFLTSxJQUFJTDtnQkFFM0IsMEJBQTBCO2dCQUMxQixJQUFJM0gsTUFBTSxHQUFHO29CQUVYLDRCQUE0QjtvQkFDNUI0SCxPQUFPRixHQUFHLENBQUMsRUFBRTtvQkFDYixJQUFJTSxNQUFNTCxNQUFNQyxPQUFPQSxPQUFPdkksT0FBUXFJLENBQUFBLEdBQUcsQ0FBQyxFQUFFLElBQUk7b0JBRWhELHdFQUF3RTtvQkFDeEVqRCxJQUFJbUQsT0FBT0csTUFBTTtvQkFFakIsY0FBYztvQkFDZCwwQ0FBMEM7b0JBQzFDLHNEQUFzRDtvQkFDdEQsMkJBQTJCO29CQUMzQix1Q0FBdUM7b0JBQ3ZDLDBDQUEwQztvQkFDMUMsMERBQTBEO29CQUUxRCxJQUFJdEQsSUFBSSxHQUFHO3dCQUNULElBQUlBLEtBQUtwRixNQUFNb0YsSUFBSXBGLE9BQU87d0JBRTFCLG1DQUFtQzt3QkFDbkNrSSxPQUFPUCxnQkFBZ0JwQyxJQUFJSDt3QkFDM0IrQyxRQUFRRCxLQUFLL0csTUFBTTt3QkFDbkJtSCxPQUFPRCxJQUFJbEgsTUFBTTt3QkFFakIsaUNBQWlDO3dCQUNqQ1IsTUFBTWtILFFBQVFLLE1BQU1HLEtBQUtGLE9BQU9HO3dCQUVoQyx1QkFBdUI7d0JBQ3ZCLElBQUkzSCxPQUFPLEdBQUc7NEJBQ1p5RTs0QkFFQSxpQ0FBaUM7NEJBQ2pDekIsU0FBU3VFLE1BQU1TLEtBQUtSLFFBQVFTLEtBQUtyRCxJQUFJNEM7d0JBQ3ZDO29CQUNGLE9BQU87d0JBRUwsYUFBYTt3QkFDYixvRkFBb0Y7d0JBQ3BGLDRFQUE0RTt3QkFDNUUsSUFBSS9DLEtBQUssR0FBR3pFLE1BQU15RSxJQUFJO3dCQUN0QjhDLE9BQU8zQyxHQUFHUixLQUFLO29CQUNqQjtvQkFFQW9ELFFBQVFELEtBQUsvRyxNQUFNO29CQUNuQixJQUFJZ0gsUUFBUUcsTUFBTUosS0FBS1osT0FBTyxDQUFDO29CQUUvQixtQ0FBbUM7b0JBQ25DM0QsU0FBUzBFLEtBQUtILE1BQU1JO29CQUVwQix1Q0FBdUM7b0JBQ3ZDLElBQUkzSCxPQUFPLENBQUMsR0FBRzt3QkFDYjJILE9BQU9ELElBQUlsSCxNQUFNO3dCQUVqQixxQ0FBcUM7d0JBQ3JDUixNQUFNa0gsUUFBUXRDLElBQUk4QyxLQUFLTSxJQUFJTDt3QkFFM0IsK0RBQStEO3dCQUMvRCxJQUFJM0gsTUFBTSxHQUFHOzRCQUNYeUU7NEJBRUEsbUNBQW1DOzRCQUNuQ3pCLFNBQVMwRSxLQUFLTSxLQUFLTCxPQUFPTSxLQUFLckQsSUFBSStDO3dCQUNyQztvQkFDRjtvQkFFQUEsT0FBT0QsSUFBSWxILE1BQU07Z0JBQ25CLE9BQU8sSUFBSVIsUUFBUSxHQUFHO29CQUNwQnlFO29CQUNBaUQsTUFBTTt3QkFBQztxQkFBRTtnQkFDWCxFQUFLLDRCQUE0QjtnQkFFakMsOENBQThDO2dCQUM5Q0QsRUFBRSxDQUFDdkgsSUFBSSxHQUFHdUU7Z0JBRVYsd0JBQXdCO2dCQUN4QixJQUFJekUsT0FBTzBILEdBQUcsQ0FBQyxFQUFFLEVBQUU7b0JBQ2pCQSxHQUFHLENBQUNDLE9BQU8sR0FBR2hELEVBQUUsQ0FBQ2tELEdBQUcsSUFBSTtnQkFDMUIsT0FBTztvQkFDTEgsTUFBTTt3QkFBQy9DLEVBQUUsQ0FBQ2tELEdBQUc7cUJBQUM7b0JBQ2RGLE9BQU87Z0JBQ1Q7WUFFRixRQUFTLENBQUNFLE9BQU9DLE1BQU1KLEdBQUcsQ0FBQyxFQUFFLEtBQUssS0FBSyxNQUFNOUQsTUFBTTtRQUNyRDtRQUVBLGdCQUFnQjtRQUNoQixJQUFJLENBQUM2RCxFQUFFLENBQUMsRUFBRSxFQUFFQSxHQUFHMUMsS0FBSztRQUVwQjNCLEVBQUU5QyxDQUFDLEdBQUdBO1FBRU4sT0FBT1ksTUFBTWtDLEdBQUcxQyxLQUFLZ0MsS0FBS3BCLGtCQUFrQjhCLEtBQUssSUFBSVY7SUFDdkQ7QUFDRjtBQUdBOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Q0FxQkMsR0FDRCxTQUFTYSxJQUFJM0QsQ0FBQyxFQUFFZ0UsRUFBRTtJQUNoQixJQUFJc0UsYUFBYXRDLE9BQU8xRyxLQUFLaUosS0FBS2xFLEdBQUd0QixLQUNuQ3pDLElBQUksR0FDSnVFLElBQUksR0FDSnhELE9BQU9yQixFQUFFQyxXQUFXLEVBQ3BCNkMsS0FBS3pCLEtBQUs3QyxTQUFTO0lBRXJCLElBQUlrRCxrQkFBa0IxQixLQUFLLElBQUksTUFBTWdELE1BQU0vRCxxQkFBcUJ5QyxrQkFBa0IxQjtJQUVsRixhQUFhO0lBQ2IsSUFBSSxDQUFDQSxFQUFFRSxDQUFDLEVBQUUsT0FBTyxJQUFJbUIsS0FBSzdCO0lBRTFCLElBQUl3RSxNQUFNLE1BQU07UUFDZGxGLFdBQVc7UUFDWGlFLE1BQU1EO0lBQ1IsT0FBTztRQUNMQyxNQUFNaUI7SUFDUjtJQUVBSyxJQUFJLElBQUloRCxLQUFLO0lBRWIsTUFBT3JCLEVBQUVELEdBQUcsR0FBRytCLEdBQUcsQ0FBQyxLQUFNO1FBQ3ZCOUIsSUFBSUEsRUFBRXlELEtBQUssQ0FBQ1ksSUFBTyxjQUFjO1FBQ2pDUSxLQUFLO0lBQ1A7SUFFQSwrRkFBK0Y7SUFDL0ZtQixRQUFRN0csS0FBS3dELEdBQUcsQ0FBQ3RELFFBQVEsR0FBR3dGLE1BQU0xRixLQUFLUCxJQUFJLEdBQUcsSUFBSSxJQUFJO0lBQ3REbUUsT0FBT2lEO0lBQ1BzQyxjQUFjaEosTUFBTWlKLE1BQU0sSUFBSWxILEtBQUs3QjtJQUNuQzZCLEtBQUs3QyxTQUFTLEdBQUd1RTtJQUVqQixPQUFTO1FBQ1B6RCxNQUFNZ0MsTUFBTWhDLElBQUltRSxLQUFLLENBQUN6RCxJQUFJK0M7UUFDMUJ1RixjQUFjQSxZQUFZN0UsS0FBSyxDQUFDLEVBQUVuRDtRQUNsQytELElBQUlrRSxJQUFJeEUsSUFBSSxDQUFDN0MsT0FBTzVCLEtBQUtnSixhQUFhdkY7UUFFdEMsSUFBSXVCLGVBQWVELEVBQUUxRCxDQUFDLEVBQUU2RCxLQUFLLENBQUMsR0FBR3pCLFNBQVN1QixlQUFlaUUsSUFBSTVILENBQUMsRUFBRTZELEtBQUssQ0FBQyxHQUFHekIsTUFBTTtZQUM3RSxNQUFPOEIsSUFBSzBELE1BQU1qSCxNQUFNaUgsSUFBSTlFLEtBQUssQ0FBQzhFLE1BQU14RjtZQUN4QzFCLEtBQUs3QyxTQUFTLEdBQUdzRTtZQUNqQixPQUFPa0IsTUFBTSxPQUFRbEYsQ0FBQUEsV0FBVyxNQUFNd0MsTUFBTWlILEtBQUt6RixHQUFFLElBQUt5RjtRQUMxRDtRQUVBQSxNQUFNbEU7SUFDUjtBQUNGO0FBR0EsNkRBQTZEO0FBQzdELFNBQVMzQyxrQkFBa0IxQixDQUFDO0lBQzFCLElBQUlVLElBQUlWLEVBQUVVLENBQUMsR0FBR2hCLFVBQ1pxQixJQUFJZixFQUFFVyxDQUFDLENBQUMsRUFBRTtJQUVaLGtFQUFrRTtJQUNsRSxNQUFPSSxLQUFLLElBQUlBLEtBQUssR0FBSUw7SUFDekIsT0FBT0E7QUFDVDtBQUdBLFNBQVM4SCxRQUFRbkgsSUFBSSxFQUFFMkMsRUFBRSxFQUFFbEIsRUFBRTtJQUUzQixJQUFJa0IsS0FBSzNDLEtBQUt6QyxJQUFJLENBQUNvRixFQUFFLElBQUk7UUFHdkIsc0RBQXNEO1FBQ3REbEYsV0FBVztRQUNYLElBQUlnRSxJQUFJekIsS0FBSzdDLFNBQVMsR0FBR3NFO1FBQ3pCLE1BQU1FLE1BQU1qRSxlQUFlO0lBQzdCO0lBRUEsT0FBT3VDLE1BQU0sSUFBSUQsS0FBS0EsS0FBS3pDLElBQUksR0FBR29GO0FBQ3BDO0FBR0EsU0FBU21ELGNBQWN0QyxDQUFDO0lBQ3RCLElBQUk0RCxLQUFLO0lBQ1QsTUFBTzVELEtBQU00RCxNQUFNO0lBQ25CLE9BQU9BO0FBQ1Q7QUFHQTs7Ozs7O0NBTUMsR0FDRCxTQUFTeEYsR0FBRzVDLENBQUMsRUFBRTJELEVBQUU7SUFDZixJQUFJMEUsR0FBR0MsSUFBSUwsYUFBYTVILEdBQUdrSSxXQUFXTCxLQUFLbEUsR0FBR3RCLEtBQUs4RixJQUNqRHpFLElBQUksR0FDSjRCLFFBQVEsSUFDUmhHLElBQUlLLEdBQ0owRSxLQUFLL0UsRUFBRVcsQ0FBQyxFQUNSVSxPQUFPckIsRUFBRUMsV0FBVyxFQUNwQjZDLEtBQUt6QixLQUFLN0MsU0FBUztJQUVyQixlQUFlO0lBQ2Ysb0JBQW9CO0lBQ3BCLElBQUl3QixFQUFFRSxDQUFDLEdBQUcsR0FBRyxNQUFNOEMsTUFBTWpFLGVBQWdCaUIsQ0FBQUEsRUFBRUUsQ0FBQyxHQUFHLFFBQVEsV0FBVTtJQUVqRSxZQUFZO0lBQ1osSUFBSUYsRUFBRXdCLEVBQUUsQ0FBQ2hDLE1BQU0sT0FBTyxJQUFJNkIsS0FBSztJQUUvQixJQUFJMkMsTUFBTSxNQUFNO1FBQ2RsRixXQUFXO1FBQ1hpRSxNQUFNRDtJQUNSLE9BQU87UUFDTEMsTUFBTWlCO0lBQ1I7SUFFQSxJQUFJaEUsRUFBRXdCLEVBQUUsQ0FBQyxLQUFLO1FBQ1osSUFBSXdDLE1BQU0sTUFBTWxGLFdBQVc7UUFDM0IsT0FBTzBKLFFBQVFuSCxNQUFNMEI7SUFDdkI7SUFFQUEsT0FBT2lEO0lBQ1AzRSxLQUFLN0MsU0FBUyxHQUFHdUU7SUFDakIyRixJQUFJcEUsZUFBZVM7SUFDbkI0RCxLQUFLRCxFQUFFSSxNQUFNLENBQUM7SUFDZHBJLElBQUlnQixrQkFBa0IxQjtJQUV0QixJQUFJYixLQUFLWSxHQUFHLENBQUNXLEtBQUssUUFBUTtRQUV4QixzQkFBc0I7UUFDdEIsd0VBQXdFO1FBQ3hFLDZDQUE2QztRQUM3QywyRkFBMkY7UUFDM0YseUZBQXlGO1FBQ3pGLDJFQUEyRTtRQUMzRSxpQ0FBaUM7UUFFakMsNERBQTREO1FBQzVELDJEQUEyRDtRQUMzRCwrQkFBK0I7UUFDL0IsTUFBT2lJLEtBQUssS0FBS0EsTUFBTSxLQUFLQSxNQUFNLEtBQUtELEVBQUVJLE1BQU0sQ0FBQyxLQUFLLEVBQUc7WUFDdEQ5SSxJQUFJQSxFQUFFeUQsS0FBSyxDQUFDcEQ7WUFDWnFJLElBQUlwRSxlQUFldEUsRUFBRVcsQ0FBQztZQUN0QmdJLEtBQUtELEVBQUVJLE1BQU0sQ0FBQztZQUNkMUU7UUFDRjtRQUVBMUQsSUFBSWdCLGtCQUFrQjFCO1FBRXRCLElBQUkySSxLQUFLLEdBQUc7WUFDVjNJLElBQUksSUFBSXFCLEtBQUssT0FBT3FIO1lBQ3BCaEk7UUFDRixPQUFPO1lBQ0xWLElBQUksSUFBSXFCLEtBQUtzSCxLQUFLLE1BQU1ELEVBQUVsRSxLQUFLLENBQUM7UUFDbEM7SUFDRixPQUFPO1FBRUwsNEZBQTRGO1FBQzVGLHVGQUF1RjtRQUN2RixnREFBZ0Q7UUFDaERILElBQUltRSxRQUFRbkgsTUFBTTBCLE1BQU0sR0FBR0QsSUFBSVcsS0FBSyxDQUFDL0MsSUFBSTtRQUN6Q1YsSUFBSWlELEdBQUcsSUFBSTVCLEtBQUtzSCxLQUFLLE1BQU1ELEVBQUVsRSxLQUFLLENBQUMsS0FBS3pCLE1BQU1pRCxPQUFPakMsSUFBSSxDQUFDTTtRQUUxRGhELEtBQUs3QyxTQUFTLEdBQUdzRTtRQUNqQixPQUFPa0IsTUFBTSxPQUFRbEYsQ0FBQUEsV0FBVyxNQUFNd0MsTUFBTXRCLEdBQUc4QyxHQUFFLElBQUs5QztJQUN4RDtJQUVBLGtDQUFrQztJQUVsQyxpQkFBaUI7SUFDakIsbUVBQW1FO0lBQ25FLHlDQUF5QztJQUN6Q3VJLE1BQU1LLFlBQVk1SSxJQUFJa0IsT0FBT2xCLEVBQUVrRCxLQUFLLENBQUMxRCxNQUFNUSxFQUFFK0QsSUFBSSxDQUFDdkUsTUFBTXVEO0lBQ3hEOEYsS0FBS3ZILE1BQU10QixFQUFFeUQsS0FBSyxDQUFDekQsSUFBSStDO0lBQ3ZCdUYsY0FBYztJQUVkLE9BQVM7UUFDUE0sWUFBWXRILE1BQU1zSCxVQUFVbkYsS0FBSyxDQUFDb0YsS0FBSzlGO1FBQ3ZDc0IsSUFBSWtFLElBQUl4RSxJQUFJLENBQUM3QyxPQUFPMEgsV0FBVyxJQUFJdkgsS0FBS2lILGNBQWN2RjtRQUV0RCxJQUFJdUIsZUFBZUQsRUFBRTFELENBQUMsRUFBRTZELEtBQUssQ0FBQyxHQUFHekIsU0FBU3VCLGVBQWVpRSxJQUFJNUgsQ0FBQyxFQUFFNkQsS0FBSyxDQUFDLEdBQUd6QixNQUFNO1lBQzdFd0YsTUFBTUEsSUFBSTlFLEtBQUssQ0FBQztZQUVoQixrQ0FBa0M7WUFDbEMsSUFBSS9DLE1BQU0sR0FBRzZILE1BQU1BLElBQUl4RSxJQUFJLENBQUN5RSxRQUFRbkgsTUFBTTBCLE1BQU0sR0FBR0QsSUFBSVcsS0FBSyxDQUFDL0MsSUFBSTtZQUNqRTZILE1BQU1ySCxPQUFPcUgsS0FBSyxJQUFJbEgsS0FBSytDLElBQUlyQjtZQUUvQjFCLEtBQUs3QyxTQUFTLEdBQUdzRTtZQUNqQixPQUFPa0IsTUFBTSxPQUFRbEYsQ0FBQUEsV0FBVyxNQUFNd0MsTUFBTWlILEtBQUt6RixHQUFFLElBQUt5RjtRQUMxRDtRQUVBQSxNQUFNbEU7UUFDTmlFLGVBQWU7SUFDakI7QUFDRjtBQUdBOztDQUVDLEdBQ0QsU0FBU1MsYUFBYS9JLENBQUMsRUFBRXdGLEdBQUc7SUFDMUIsSUFBSTlFLEdBQUdKLEdBQUd1RztJQUVWLGlCQUFpQjtJQUNqQixJQUFJLENBQUNuRyxJQUFJOEUsSUFBSWYsT0FBTyxDQUFDLElBQUcsSUFBSyxDQUFDLEdBQUdlLE1BQU1BLElBQUl3RCxPQUFPLENBQUMsS0FBSztJQUV4RCxvQkFBb0I7SUFDcEIsSUFBSSxDQUFDMUksSUFBSWtGLElBQUl5RCxNQUFNLENBQUMsS0FBSSxJQUFLLEdBQUc7UUFFOUIsc0JBQXNCO1FBQ3RCLElBQUl2SSxJQUFJLEdBQUdBLElBQUlKO1FBQ2ZJLEtBQUssQ0FBQzhFLElBQUloQixLQUFLLENBQUNsRSxJQUFJO1FBQ3BCa0YsTUFBTUEsSUFBSTBELFNBQVMsQ0FBQyxHQUFHNUk7SUFDekIsT0FBTyxJQUFJSSxJQUFJLEdBQUc7UUFFaEIsV0FBVztRQUNYQSxJQUFJOEUsSUFBSTVFLE1BQU07SUFDaEI7SUFFQSwyQkFBMkI7SUFDM0IsSUFBS04sSUFBSSxHQUFHa0YsSUFBSTJELFVBQVUsQ0FBQzdJLE9BQU8sSUFBSyxFQUFFQTtJQUV6Qyw0QkFBNEI7SUFDNUIsSUFBS3VHLE1BQU1yQixJQUFJNUUsTUFBTSxFQUFFNEUsSUFBSTJELFVBQVUsQ0FBQ3RDLE1BQU0sT0FBTyxJQUFLLEVBQUVBO0lBQzFEckIsTUFBTUEsSUFBSWhCLEtBQUssQ0FBQ2xFLEdBQUd1RztJQUVuQixJQUFJckIsS0FBSztRQUNQcUIsT0FBT3ZHO1FBQ1BJLElBQUlBLElBQUlKLElBQUk7UUFDWk4sRUFBRVUsQ0FBQyxHQUFHeEIsVUFBVXdCLElBQUloQjtRQUNwQk0sRUFBRVcsQ0FBQyxHQUFHLEVBQUU7UUFFUixpQkFBaUI7UUFFakIsNkJBQTZCO1FBQzdCLHFFQUFxRTtRQUNyRUwsSUFBSSxDQUFDSSxJQUFJLEtBQUtoQjtRQUNkLElBQUlnQixJQUFJLEdBQUdKLEtBQUtaO1FBRWhCLElBQUlZLElBQUl1RyxLQUFLO1lBQ1gsSUFBSXZHLEdBQUdOLEVBQUVXLENBQUMsQ0FBQ3NFLElBQUksQ0FBQyxDQUFDTyxJQUFJaEIsS0FBSyxDQUFDLEdBQUdsRTtZQUM5QixJQUFLdUcsT0FBT25ILFVBQVVZLElBQUl1RyxLQUFNN0csRUFBRVcsQ0FBQyxDQUFDc0UsSUFBSSxDQUFDLENBQUNPLElBQUloQixLQUFLLENBQUNsRSxHQUFHQSxLQUFLWjtZQUM1RDhGLE1BQU1BLElBQUloQixLQUFLLENBQUNsRTtZQUNoQkEsSUFBSVosV0FBVzhGLElBQUk1RSxNQUFNO1FBQzNCLE9BQU87WUFDTE4sS0FBS3VHO1FBQ1A7UUFFQSxNQUFPdkcsS0FBTWtGLE9BQU87UUFDcEJ4RixFQUFFVyxDQUFDLENBQUNzRSxJQUFJLENBQUMsQ0FBQ087UUFFVixJQUFJMUcsWUFBYWtCLENBQUFBLEVBQUVVLENBQUMsR0FBR2QsU0FBU0ksRUFBRVUsQ0FBQyxHQUFHLENBQUNkLEtBQUksR0FBSSxNQUFNb0QsTUFBTS9ELHFCQUFxQnlCO0lBQ2xGLE9BQU87UUFFTCxRQUFRO1FBQ1JWLEVBQUVFLENBQUMsR0FBRztRQUNORixFQUFFVSxDQUFDLEdBQUc7UUFDTlYsRUFBRVcsQ0FBQyxHQUFHO1lBQUM7U0FBRTtJQUNYO0lBRUEsT0FBT1g7QUFDVDtBQUdBOztDQUVDLEdBQ0EsU0FBU3NCLE1BQU10QixDQUFDLEVBQUVnRSxFQUFFLEVBQUVzQixFQUFFO0lBQ3ZCLElBQUloRixHQUFHQyxHQUFHc0UsR0FBR1QsR0FBR2dGLElBQUlDLFNBQVN0SSxHQUFHdUksS0FDOUJ2RSxLQUFLL0UsRUFBRVcsQ0FBQztJQUVWLGlGQUFpRjtJQUNqRiwwRUFBMEU7SUFDMUUsaUNBQWlDO0lBQ2pDLGdDQUFnQztJQUNoQywyRkFBMkY7SUFDM0YsMEJBQTBCO0lBQzFCLDZFQUE2RTtJQUU3RSwyREFBMkQ7SUFDM0QsSUFBS3lELElBQUksR0FBR1MsSUFBSUUsRUFBRSxDQUFDLEVBQUUsRUFBRUYsS0FBSyxJQUFJQSxLQUFLLEdBQUlUO0lBQ3pDOUQsSUFBSTBELEtBQUtJO0lBRVQsaURBQWlEO0lBQ2pELElBQUk5RCxJQUFJLEdBQUc7UUFDVEEsS0FBS1o7UUFDTGEsSUFBSXlEO1FBQ0pqRCxJQUFJZ0UsRUFBRSxDQUFDdUUsTUFBTSxFQUFFO0lBQ2pCLE9BQU87UUFDTEEsTUFBTW5LLEtBQUsrRyxJQUFJLENBQUMsQ0FBQzVGLElBQUksS0FBS1o7UUFDMUJtRixJQUFJRSxHQUFHbkUsTUFBTTtRQUNiLElBQUkwSSxPQUFPekUsR0FBRyxPQUFPN0U7UUFDckJlLElBQUk4RCxJQUFJRSxFQUFFLENBQUN1RSxJQUFJO1FBRWYsaUNBQWlDO1FBQ2pDLElBQUtsRixJQUFJLEdBQUdTLEtBQUssSUFBSUEsS0FBSyxHQUFJVDtRQUU5QixnQ0FBZ0M7UUFDaEM5RCxLQUFLWjtRQUVMLDREQUE0RDtRQUM1RCw2REFBNkQ7UUFDN0RhLElBQUlELElBQUlaLFdBQVcwRTtJQUNyQjtJQUVBLElBQUlrQixPQUFPLEtBQUssR0FBRztRQUNqQlQsSUFBSXhGLFFBQVEsSUFBSStFLElBQUk3RCxJQUFJO1FBRXhCLDBDQUEwQztRQUMxQzZJLEtBQUtySSxJQUFJOEQsSUFBSSxLQUFLO1FBRWxCLDBEQUEwRDtRQUMxRHdFLFVBQVVyRixLQUFLLEtBQUtlLEVBQUUsQ0FBQ3VFLE1BQU0sRUFBRSxLQUFLLEtBQUssS0FBS3ZJLElBQUk4RDtRQUVsRCw4RkFBOEY7UUFDOUYsNkZBQTZGO1FBQzdGLE9BQU87UUFFUHdFLFVBQVUvRCxLQUFLLElBQ1gsQ0FBQzhELE1BQU1DLE9BQU0sS0FBTy9ELENBQUFBLE1BQU0sS0FBS0EsTUFBT3RGLENBQUFBLEVBQUVFLENBQUMsR0FBRyxJQUFJLElBQUksRUFBQyxJQUNyRGtKLEtBQUssS0FBS0EsTUFBTSxLQUFNOUQsQ0FBQUEsTUFBTSxLQUFLK0QsV0FBVy9ELE1BQU0sS0FHbEQsQ0FBRWhGLElBQUksSUFBSUMsSUFBSSxJQUFJUSxJQUFJMUIsUUFBUSxJQUFJK0UsSUFBSTdELEtBQUssSUFBSXdFLEVBQUUsQ0FBQ3VFLE1BQU0sRUFBRSxJQUFJLEtBQU0sS0FDbEVoRSxNQUFPdEYsQ0FBQUEsRUFBRUUsQ0FBQyxHQUFHLElBQUksSUFBSSxFQUFDO0lBQzlCO0lBRUEsSUFBSThELEtBQUssS0FBSyxDQUFDZSxFQUFFLENBQUMsRUFBRSxFQUFFO1FBQ3BCLElBQUlzRSxTQUFTO1lBQ1h4RSxJQUFJbkQsa0JBQWtCMUI7WUFDdEIrRSxHQUFHbkUsTUFBTSxHQUFHO1lBRVosZ0NBQWdDO1lBQ2hDb0QsS0FBS0EsS0FBS2EsSUFBSTtZQUVkLG1DQUFtQztZQUNuQ0UsRUFBRSxDQUFDLEVBQUUsR0FBRzFGLFFBQVEsSUFBSSxDQUFDSyxXQUFXc0UsS0FBS3RFLFFBQU8sSUFBS0E7WUFDakRNLEVBQUVVLENBQUMsR0FBR3hCLFVBQVUsQ0FBQzhFLEtBQUt0RSxhQUFhO1FBQ3JDLE9BQU87WUFDTHFGLEdBQUduRSxNQUFNLEdBQUc7WUFFWixRQUFRO1lBQ1JtRSxFQUFFLENBQUMsRUFBRSxHQUFHL0UsRUFBRVUsQ0FBQyxHQUFHVixFQUFFRSxDQUFDLEdBQUc7UUFDdEI7UUFFQSxPQUFPRjtJQUNUO0lBRUEsd0JBQXdCO0lBQ3hCLElBQUlNLEtBQUssR0FBRztRQUNWeUUsR0FBR25FLE1BQU0sR0FBRzBJO1FBQ1p6RSxJQUFJO1FBQ0p5RTtJQUNGLE9BQU87UUFDTHZFLEdBQUduRSxNQUFNLEdBQUcwSSxNQUFNO1FBQ2xCekUsSUFBSXhGLFFBQVEsSUFBSUssV0FBV1k7UUFFM0IsdURBQXVEO1FBQ3ZELGdEQUFnRDtRQUNoRHlFLEVBQUUsQ0FBQ3VFLElBQUksR0FBRy9JLElBQUksSUFBSSxDQUFDUSxJQUFJMUIsUUFBUSxJQUFJK0UsSUFBSTdELEtBQUtsQixRQUFRLElBQUlrQixLQUFLLEtBQUtzRSxJQUFJO0lBQ3hFO0lBRUEsSUFBSXdFLFNBQVM7UUFDWCxPQUFTO1lBRVAseURBQXlEO1lBQ3pELElBQUlDLE9BQU8sR0FBRztnQkFDWixJQUFJLENBQUN2RSxFQUFFLENBQUMsRUFBRSxJQUFJRixDQUFBQSxLQUFNcEYsTUFBTTtvQkFDeEJzRixFQUFFLENBQUMsRUFBRSxHQUFHO29CQUNSLEVBQUUvRSxFQUFFVSxDQUFDO2dCQUNQO2dCQUVBO1lBQ0YsT0FBTztnQkFDTHFFLEVBQUUsQ0FBQ3VFLElBQUksSUFBSXpFO2dCQUNYLElBQUlFLEVBQUUsQ0FBQ3VFLElBQUksSUFBSTdKLE1BQU07Z0JBQ3JCc0YsRUFBRSxDQUFDdUUsTUFBTSxHQUFHO2dCQUNaekUsSUFBSTtZQUNOO1FBQ0Y7SUFDRjtJQUVBLHlCQUF5QjtJQUN6QixJQUFLdkUsSUFBSXlFLEdBQUduRSxNQUFNLEVBQUVtRSxFQUFFLENBQUMsRUFBRXpFLEVBQUUsS0FBSyxHQUFJeUUsR0FBR0csR0FBRztJQUUxQyxJQUFJcEcsWUFBYWtCLENBQUFBLEVBQUVVLENBQUMsR0FBR2QsU0FBU0ksRUFBRVUsQ0FBQyxHQUFHLENBQUNkLEtBQUksR0FBSTtRQUM3QyxNQUFNb0QsTUFBTS9ELHFCQUFxQnlDLGtCQUFrQjFCO0lBQ3JEO0lBRUEsT0FBT0E7QUFDVDtBQUdBLFNBQVNvRCxTQUFTcEQsQ0FBQyxFQUFFSyxDQUFDO0lBQ3BCLElBQUlNLEdBQUdELEdBQUdKLEdBQUdDLEdBQUdzRSxHQUFHZ0MsS0FBSzlCLElBQUl3RSxJQUFJQyxNQUFNeEUsSUFDcEMzRCxPQUFPckIsRUFBRUMsV0FBVyxFQUNwQjZDLEtBQUt6QixLQUFLN0MsU0FBUztJQUVyQixpQ0FBaUM7SUFDakMsMkNBQTJDO0lBQzNDLElBQUksQ0FBQ3dCLEVBQUVFLENBQUMsSUFBSSxDQUFDRyxFQUFFSCxDQUFDLEVBQUU7UUFDaEIsSUFBSUcsRUFBRUgsQ0FBQyxFQUFFRyxFQUFFSCxDQUFDLEdBQUcsQ0FBQ0csRUFBRUgsQ0FBQzthQUNkRyxJQUFJLElBQUlnQixLQUFLckI7UUFDbEIsT0FBT2xCLFdBQVd3QyxNQUFNakIsR0FBR3lDLE1BQU16QztJQUNuQztJQUVBMEUsS0FBSy9FLEVBQUVXLENBQUM7SUFDUnFFLEtBQUszRSxFQUFFTSxDQUFDO0lBRVIsbURBQW1EO0lBRW5ERCxJQUFJTCxFQUFFSyxDQUFDO0lBQ1A2SSxLQUFLdkosRUFBRVUsQ0FBQztJQUNScUUsS0FBS0EsR0FBR1AsS0FBSztJQUNiSyxJQUFJMEUsS0FBSzdJO0lBRVQseUJBQXlCO0lBQ3pCLElBQUltRSxHQUFHO1FBQ0wyRSxPQUFPM0UsSUFBSTtRQUVYLElBQUkyRSxNQUFNO1lBQ1I3SSxJQUFJb0U7WUFDSkYsSUFBSSxDQUFDQTtZQUNMZ0MsTUFBTTdCLEdBQUdwRSxNQUFNO1FBQ2pCLE9BQU87WUFDTEQsSUFBSXFFO1lBQ0p0RSxJQUFJNkk7WUFDSjFDLE1BQU05QixHQUFHbkUsTUFBTTtRQUNqQjtRQUVBLHlGQUF5RjtRQUN6Riw0RkFBNEY7UUFDNUYsa0VBQWtFO1FBQ2xFTixJQUFJbkIsS0FBS2lILEdBQUcsQ0FBQ2pILEtBQUsrRyxJQUFJLENBQUNwRCxLQUFLcEQsV0FBV21ILE9BQU87UUFFOUMsSUFBSWhDLElBQUl2RSxHQUFHO1lBQ1R1RSxJQUFJdkU7WUFDSkssRUFBRUMsTUFBTSxHQUFHO1FBQ2I7UUFFQSx1Q0FBdUM7UUFDdkNELEVBQUVtRyxPQUFPO1FBQ1QsSUFBS3hHLElBQUl1RSxHQUFHdkUsS0FBTUssRUFBRXNFLElBQUksQ0FBQztRQUN6QnRFLEVBQUVtRyxPQUFPO0lBRVgsNEJBQTRCO0lBQzVCLE9BQU87UUFFTCx3REFBd0Q7UUFFeER4RyxJQUFJeUUsR0FBR25FLE1BQU07UUFDYmlHLE1BQU03QixHQUFHcEUsTUFBTTtRQUNmNEksT0FBT2xKLElBQUl1RztRQUNYLElBQUkyQyxNQUFNM0MsTUFBTXZHO1FBRWhCLElBQUtBLElBQUksR0FBR0EsSUFBSXVHLEtBQUt2RyxJQUFLO1lBQ3hCLElBQUl5RSxFQUFFLENBQUN6RSxFQUFFLElBQUkwRSxFQUFFLENBQUMxRSxFQUFFLEVBQUU7Z0JBQ2xCa0osT0FBT3pFLEVBQUUsQ0FBQ3pFLEVBQUUsR0FBRzBFLEVBQUUsQ0FBQzFFLEVBQUU7Z0JBQ3BCO1lBQ0Y7UUFDRjtRQUVBdUUsSUFBSTtJQUNOO0lBRUEsSUFBSTJFLE1BQU07UUFDUjdJLElBQUlvRTtRQUNKQSxLQUFLQztRQUNMQSxLQUFLckU7UUFDTE4sRUFBRUgsQ0FBQyxHQUFHLENBQUNHLEVBQUVILENBQUM7SUFDWjtJQUVBMkcsTUFBTTlCLEdBQUduRSxNQUFNO0lBRWYsaUNBQWlDO0lBQ2pDLG9GQUFvRjtJQUNwRixJQUFLTixJQUFJMEUsR0FBR3BFLE1BQU0sR0FBR2lHLEtBQUt2RyxJQUFJLEdBQUcsRUFBRUEsRUFBR3lFLEVBQUUsQ0FBQzhCLE1BQU0sR0FBRztJQUVsRCx1QkFBdUI7SUFDdkIsSUFBS3ZHLElBQUkwRSxHQUFHcEUsTUFBTSxFQUFFTixJQUFJdUUsR0FBSTtRQUMxQixJQUFJRSxFQUFFLENBQUMsRUFBRXpFLEVBQUUsR0FBRzBFLEVBQUUsQ0FBQzFFLEVBQUUsRUFBRTtZQUNuQixJQUFLQyxJQUFJRCxHQUFHQyxLQUFLd0UsRUFBRSxDQUFDLEVBQUV4RSxFQUFFLEtBQUssR0FBSXdFLEVBQUUsQ0FBQ3hFLEVBQUUsR0FBR2QsT0FBTztZQUNoRCxFQUFFc0YsRUFBRSxDQUFDeEUsRUFBRTtZQUNQd0UsRUFBRSxDQUFDekUsRUFBRSxJQUFJYjtRQUNYO1FBRUFzRixFQUFFLENBQUN6RSxFQUFFLElBQUkwRSxFQUFFLENBQUMxRSxFQUFFO0lBQ2hCO0lBRUEseUJBQXlCO0lBQ3pCLE1BQU95RSxFQUFFLENBQUMsRUFBRThCLElBQUksS0FBSyxHQUFJOUIsR0FBR0csR0FBRztJQUUvQix3REFBd0Q7SUFDeEQsTUFBT0gsRUFBRSxDQUFDLEVBQUUsS0FBSyxHQUFHQSxHQUFHSSxLQUFLLEdBQUksRUFBRXpFO0lBRWxDLFFBQVE7SUFDUixJQUFJLENBQUNxRSxFQUFFLENBQUMsRUFBRSxFQUFFLE9BQU8sSUFBSTFELEtBQUs7SUFFNUJoQixFQUFFTSxDQUFDLEdBQUdvRTtJQUNOMUUsRUFBRUssQ0FBQyxHQUFHQTtJQUVOLG1FQUFtRTtJQUNuRSxPQUFPNUIsV0FBV3dDLE1BQU1qQixHQUFHeUMsTUFBTXpDO0FBQ25DO0FBR0EsU0FBU3FFLFNBQVMxRSxDQUFDLEVBQUV5SixLQUFLLEVBQUV6RixFQUFFO0lBQzVCLElBQUlhLEdBQ0ZuRSxJQUFJZ0Isa0JBQWtCMUIsSUFDdEJ3RixNQUFNbEIsZUFBZXRFLEVBQUVXLENBQUMsR0FDeEJrRyxNQUFNckIsSUFBSTVFLE1BQU07SUFFbEIsSUFBSTZJLE9BQU87UUFDVCxJQUFJekYsTUFBTSxDQUFDYSxJQUFJYixLQUFLNkMsR0FBRSxJQUFLLEdBQUc7WUFDNUJyQixNQUFNQSxJQUFJc0QsTUFBTSxDQUFDLEtBQUssTUFBTXRELElBQUloQixLQUFLLENBQUMsS0FBSzJDLGNBQWN0QztRQUMzRCxPQUFPLElBQUlnQyxNQUFNLEdBQUc7WUFDbEJyQixNQUFNQSxJQUFJc0QsTUFBTSxDQUFDLEtBQUssTUFBTXRELElBQUloQixLQUFLLENBQUM7UUFDeEM7UUFFQWdCLE1BQU1BLE1BQU85RSxDQUFBQSxJQUFJLElBQUksTUFBTSxJQUFHLElBQUtBO0lBQ3JDLE9BQU8sSUFBSUEsSUFBSSxHQUFHO1FBQ2hCOEUsTUFBTSxPQUFPMkIsY0FBYyxDQUFDekcsSUFBSSxLQUFLOEU7UUFDckMsSUFBSXhCLE1BQU0sQ0FBQ2EsSUFBSWIsS0FBSzZDLEdBQUUsSUFBSyxHQUFHckIsT0FBTzJCLGNBQWN0QztJQUNyRCxPQUFPLElBQUluRSxLQUFLbUcsS0FBSztRQUNuQnJCLE9BQU8yQixjQUFjekcsSUFBSSxJQUFJbUc7UUFDN0IsSUFBSTdDLE1BQU0sQ0FBQ2EsSUFBSWIsS0FBS3RELElBQUksS0FBSyxHQUFHOEUsTUFBTUEsTUFBTSxNQUFNMkIsY0FBY3RDO0lBQ2xFLE9BQU87UUFDTCxJQUFJLENBQUNBLElBQUluRSxJQUFJLEtBQUttRyxLQUFLckIsTUFBTUEsSUFBSWhCLEtBQUssQ0FBQyxHQUFHSyxLQUFLLE1BQU1XLElBQUloQixLQUFLLENBQUNLO1FBQy9ELElBQUliLE1BQU0sQ0FBQ2EsSUFBSWIsS0FBSzZDLEdBQUUsSUFBSyxHQUFHO1lBQzVCLElBQUluRyxJQUFJLE1BQU1tRyxLQUFLckIsT0FBTztZQUMxQkEsT0FBTzJCLGNBQWN0QztRQUN2QjtJQUNGO0lBRUEsT0FBTzdFLEVBQUVFLENBQUMsR0FBRyxJQUFJLE1BQU1zRixNQUFNQTtBQUMvQjtBQUdBLGlDQUFpQztBQUNqQyxTQUFTVyxTQUFTdUQsR0FBRyxFQUFFN0MsR0FBRztJQUN4QixJQUFJNkMsSUFBSTlJLE1BQU0sR0FBR2lHLEtBQUs7UUFDcEI2QyxJQUFJOUksTUFBTSxHQUFHaUc7UUFDYixPQUFPO0lBQ1Q7QUFDRjtBQUdBLGtCQUFrQjtBQUdsQjs7O0NBR0MsR0FHRDs7OztDQUlDLEdBQ0QsU0FBUzhDLE1BQU1DLEdBQUc7SUFDaEIsSUFBSXRKLEdBQUd1SixHQUFHQztJQUVWOzs7Ozs7R0FNQyxHQUNELFNBQVNqTCxRQUFRa0wsS0FBSztRQUNwQixJQUFJL0osSUFBSSxJQUFJO1FBRVosOEJBQThCO1FBQzlCLElBQUksQ0FBRUEsQ0FBQUEsYUFBYW5CLE9BQU0sR0FBSSxPQUFPLElBQUlBLFFBQVFrTDtRQUVoRCwyRkFBMkY7UUFDM0YsMEJBQTBCO1FBQzFCL0osRUFBRUMsV0FBVyxHQUFHcEI7UUFFaEIsYUFBYTtRQUNiLElBQUlrTCxpQkFBaUJsTCxTQUFTO1lBQzVCbUIsRUFBRUUsQ0FBQyxHQUFHNkosTUFBTTdKLENBQUM7WUFDYkYsRUFBRVUsQ0FBQyxHQUFHcUosTUFBTXJKLENBQUM7WUFDYlYsRUFBRVcsQ0FBQyxHQUFHLENBQUNvSixRQUFRQSxNQUFNcEosQ0FBQyxJQUFJb0osTUFBTXZGLEtBQUssS0FBS3VGO1lBQzFDO1FBQ0Y7UUFFQSxJQUFJLE9BQU9BLFVBQVUsVUFBVTtZQUU3Qix1QkFBdUI7WUFDdkIsSUFBSUEsUUFBUSxNQUFNLEdBQUc7Z0JBQ25CLE1BQU0vRyxNQUFNaEUsa0JBQWtCK0s7WUFDaEM7WUFFQSxJQUFJQSxRQUFRLEdBQUc7Z0JBQ2IvSixFQUFFRSxDQUFDLEdBQUc7WUFDUixPQUFPLElBQUk2SixRQUFRLEdBQUc7Z0JBQ3BCQSxRQUFRLENBQUNBO2dCQUNUL0osRUFBRUUsQ0FBQyxHQUFHLENBQUM7WUFDVCxPQUFPO2dCQUNMRixFQUFFRSxDQUFDLEdBQUc7Z0JBQ05GLEVBQUVVLENBQUMsR0FBRztnQkFDTlYsRUFBRVcsQ0FBQyxHQUFHO29CQUFDO2lCQUFFO2dCQUNUO1lBQ0Y7WUFFQSxnQ0FBZ0M7WUFDaEMsSUFBSW9KLFVBQVUsQ0FBQyxDQUFDQSxTQUFTQSxRQUFRLEtBQUs7Z0JBQ3BDL0osRUFBRVUsQ0FBQyxHQUFHO2dCQUNOVixFQUFFVyxDQUFDLEdBQUc7b0JBQUNvSjtpQkFBTTtnQkFDYjtZQUNGO1lBRUEsT0FBT2hCLGFBQWEvSSxHQUFHK0osTUFBTXJGLFFBQVE7UUFDdkMsT0FBTyxJQUFJLE9BQU9xRixVQUFVLFVBQVU7WUFDcEMsTUFBTS9HLE1BQU1oRSxrQkFBa0IrSztRQUNoQztRQUVBLGNBQWM7UUFDZCxJQUFJQSxNQUFNWixVQUFVLENBQUMsT0FBTyxJQUFJO1lBQzlCWSxRQUFRQSxNQUFNdkYsS0FBSyxDQUFDO1lBQ3BCeEUsRUFBRUUsQ0FBQyxHQUFHLENBQUM7UUFDVCxPQUFPO1lBQ0xGLEVBQUVFLENBQUMsR0FBRztRQUNSO1FBRUEsSUFBSVgsVUFBVXlLLElBQUksQ0FBQ0QsUUFBUWhCLGFBQWEvSSxHQUFHK0o7YUFDdEMsTUFBTS9HLE1BQU1oRSxrQkFBa0IrSztJQUNyQztJQUVBbEwsUUFBUW9MLFNBQVMsR0FBR3BLO0lBRXBCaEIsUUFBUXFMLFFBQVEsR0FBRztJQUNuQnJMLFFBQVFzTCxVQUFVLEdBQUc7SUFDckJ0TCxRQUFRdUwsVUFBVSxHQUFHO0lBQ3JCdkwsUUFBUXdMLFdBQVcsR0FBRztJQUN0QnhMLFFBQVF5TCxhQUFhLEdBQUc7SUFDeEJ6TCxRQUFRMEwsZUFBZSxHQUFHO0lBQzFCMUwsUUFBUTJMLGVBQWUsR0FBRztJQUMxQjNMLFFBQVE0TCxlQUFlLEdBQUc7SUFDMUI1TCxRQUFRNkwsZ0JBQWdCLEdBQUc7SUFFM0I3TCxRQUFROEssS0FBSyxHQUFHQTtJQUNoQjlLLFFBQVE4TCxNQUFNLEdBQUc5TCxRQUFRK0wsR0FBRyxHQUFHRDtJQUUvQixJQUFJZixRQUFRLEtBQUssR0FBR0EsTUFBTSxDQUFDO0lBQzNCLElBQUlBLEtBQUs7UUFDUEUsS0FBSztZQUFDO1lBQWE7WUFBWTtZQUFZO1lBQVk7U0FBTztRQUM5RCxJQUFLeEosSUFBSSxHQUFHQSxJQUFJd0osR0FBR2xKLE1BQU0sRUFBRyxJQUFJLENBQUNnSixJQUFJaUIsY0FBYyxDQUFDaEIsSUFBSUMsRUFBRSxDQUFDeEosSUFBSSxHQUFHc0osR0FBRyxDQUFDQyxFQUFFLEdBQUcsSUFBSSxDQUFDQSxFQUFFO0lBQ3BGO0lBRUFoTCxRQUFROEwsTUFBTSxDQUFDZjtJQUVmLE9BQU8vSztBQUNUO0FBR0E7Ozs7Ozs7Ozs7OztDQVlDLEdBQ0QsU0FBUzhMLE9BQU9mLEdBQUc7SUFDakIsSUFBSSxDQUFDQSxPQUFPLE9BQU9BLFFBQVEsVUFBVTtRQUNuQyxNQUFNNUcsTUFBTWpFLGVBQWU7SUFDN0I7SUFDQSxJQUFJdUIsR0FBR3VKLEdBQUdpQixHQUNSaEIsS0FBSztRQUNIO1FBQWE7UUFBR3hMO1FBQ2hCO1FBQVk7UUFBRztRQUNmO1FBQVksQ0FBQyxJQUFJO1FBQUc7UUFDcEI7UUFBWTtRQUFHLElBQUk7S0FDcEI7SUFFSCxJQUFLZ0MsSUFBSSxHQUFHQSxJQUFJd0osR0FBR2xKLE1BQU0sRUFBRU4sS0FBSyxFQUFHO1FBQ2pDLElBQUksQ0FBQ3dLLElBQUlsQixHQUFHLENBQUNDLElBQUlDLEVBQUUsQ0FBQ3hKLEVBQUUsQ0FBQyxNQUFNLEtBQUssR0FBRztZQUNuQyxJQUFJcEIsVUFBVTRMLE9BQU9BLEtBQUtBLEtBQUtoQixFQUFFLENBQUN4SixJQUFJLEVBQUUsSUFBSXdLLEtBQUtoQixFQUFFLENBQUN4SixJQUFJLEVBQUUsRUFBRSxJQUFJLENBQUN1SixFQUFFLEdBQUdpQjtpQkFDakUsTUFBTTlILE1BQU1oRSxrQkFBa0I2SyxJQUFJLE9BQU9pQjtRQUNoRDtJQUNGO0lBRUEsSUFBSSxDQUFDQSxJQUFJbEIsR0FBRyxDQUFDQyxJQUFJLE9BQU8sTUFBTSxLQUFLLEdBQUc7UUFDbEMsSUFBSWlCLEtBQUszTCxLQUFLUCxJQUFJLEVBQUUsSUFBSSxDQUFDaUwsRUFBRSxHQUFHLElBQUksSUFBSSxDQUFDaUI7YUFDbEMsTUFBTTlILE1BQU1oRSxrQkFBa0I2SyxJQUFJLE9BQU9pQjtJQUNsRDtJQUVBLE9BQU8sSUFBSTtBQUNiO0FBR0Esb0RBQW9EO0FBQzdDLElBQUlqTSxVQUFVOEssTUFBTXBMLFVBQVU7QUFFckMscUJBQXFCO0FBQ3JCaUIsTUFBTSxJQUFJWCxRQUFRO0FBRWxCLGlFQUFlQSxPQUFPQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQGFnZW50aWMtdGFsZW50LXByby9mcm9udGVuZC8uLi9ub2RlX21vZHVsZXMvZGVjaW1hbC5qcy1saWdodC9kZWNpbWFsLm1qcz9hNTk3Il0sInNvdXJjZXNDb250ZW50IjpbIi8qXHJcbiAqICBkZWNpbWFsLmpzLWxpZ2h0IHYyLjUuMVxyXG4gKiAgQW4gYXJiaXRyYXJ5LXByZWNpc2lvbiBEZWNpbWFsIHR5cGUgZm9yIEphdmFTY3JpcHQuXHJcbiAqICBodHRwczovL2dpdGh1Yi5jb20vTWlrZU1jbC9kZWNpbWFsLmpzLWxpZ2h0XHJcbiAqICBDb3B5cmlnaHQgKGMpIDIwMjAgTWljaGFlbCBNY2xhdWdobGluIDxNOGNoODhsQGdtYWlsLmNvbT5cclxuICogIE1JVCBFeHBhdCBMaWNlbmNlXHJcbiAqL1xyXG5cclxuXHJcbi8vIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSAgRURJVEFCTEUgREVGQVVMVFMgIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0gLy9cclxuXHJcblxyXG4vLyBUaGUgbGltaXQgb24gdGhlIHZhbHVlIG9mIGBwcmVjaXNpb25gLCBhbmQgb24gdGhlIHZhbHVlIG9mIHRoZSBmaXJzdCBhcmd1bWVudCB0b1xyXG4vLyBgdG9EZWNpbWFsUGxhY2VzYCwgYHRvRXhwb25lbnRpYWxgLCBgdG9GaXhlZGAsIGB0b1ByZWNpc2lvbmAgYW5kIGB0b1NpZ25pZmljYW50RGlnaXRzYC5cclxudmFyIE1BWF9ESUdJVFMgPSAxZTksICAgICAgICAgICAgICAgICAgICAgICAgLy8gMCB0byAxZTlcclxuXHJcblxyXG4gIC8vIFRoZSBpbml0aWFsIGNvbmZpZ3VyYXRpb24gcHJvcGVydGllcyBvZiB0aGUgRGVjaW1hbCBjb25zdHJ1Y3Rvci5cclxuICBkZWZhdWx0cyA9IHtcclxuXHJcbiAgICAvLyBUaGVzZSB2YWx1ZXMgbXVzdCBiZSBpbnRlZ2VycyB3aXRoaW4gdGhlIHN0YXRlZCByYW5nZXMgKGluY2x1c2l2ZSkuXHJcbiAgICAvLyBNb3N0IG9mIHRoZXNlIHZhbHVlcyBjYW4gYmUgY2hhbmdlZCBkdXJpbmcgcnVuLXRpbWUgdXNpbmcgYERlY2ltYWwuY29uZmlnYC5cclxuXHJcbiAgICAvLyBUaGUgbWF4aW11bSBudW1iZXIgb2Ygc2lnbmlmaWNhbnQgZGlnaXRzIG9mIHRoZSByZXN1bHQgb2YgYSBjYWxjdWxhdGlvbiBvciBiYXNlIGNvbnZlcnNpb24uXHJcbiAgICAvLyBFLmcuIGBEZWNpbWFsLmNvbmZpZyh7IHByZWNpc2lvbjogMjAgfSk7YFxyXG4gICAgcHJlY2lzaW9uOiAyMCwgICAgICAgICAgICAgICAgICAgICAgICAgLy8gMSB0byBNQVhfRElHSVRTXHJcblxyXG4gICAgLy8gVGhlIHJvdW5kaW5nIG1vZGUgdXNlZCBieSBkZWZhdWx0IGJ5IGB0b0ludGVnZXJgLCBgdG9EZWNpbWFsUGxhY2VzYCwgYHRvRXhwb25lbnRpYWxgLFxyXG4gICAgLy8gYHRvRml4ZWRgLCBgdG9QcmVjaXNpb25gIGFuZCBgdG9TaWduaWZpY2FudERpZ2l0c2AuXHJcbiAgICAvL1xyXG4gICAgLy8gUk9VTkRfVVAgICAgICAgICAwIEF3YXkgZnJvbSB6ZXJvLlxyXG4gICAgLy8gUk9VTkRfRE9XTiAgICAgICAxIFRvd2FyZHMgemVyby5cclxuICAgIC8vIFJPVU5EX0NFSUwgICAgICAgMiBUb3dhcmRzICtJbmZpbml0eS5cclxuICAgIC8vIFJPVU5EX0ZMT09SICAgICAgMyBUb3dhcmRzIC1JbmZpbml0eS5cclxuICAgIC8vIFJPVU5EX0hBTEZfVVAgICAgNCBUb3dhcmRzIG5lYXJlc3QgbmVpZ2hib3VyLiBJZiBlcXVpZGlzdGFudCwgdXAuXHJcbiAgICAvLyBST1VORF9IQUxGX0RPV04gIDUgVG93YXJkcyBuZWFyZXN0IG5laWdoYm91ci4gSWYgZXF1aWRpc3RhbnQsIGRvd24uXHJcbiAgICAvLyBST1VORF9IQUxGX0VWRU4gIDYgVG93YXJkcyBuZWFyZXN0IG5laWdoYm91ci4gSWYgZXF1aWRpc3RhbnQsIHRvd2FyZHMgZXZlbiBuZWlnaGJvdXIuXHJcbiAgICAvLyBST1VORF9IQUxGX0NFSUwgIDcgVG93YXJkcyBuZWFyZXN0IG5laWdoYm91ci4gSWYgZXF1aWRpc3RhbnQsIHRvd2FyZHMgK0luZmluaXR5LlxyXG4gICAgLy8gUk9VTkRfSEFMRl9GTE9PUiA4IFRvd2FyZHMgbmVhcmVzdCBuZWlnaGJvdXIuIElmIGVxdWlkaXN0YW50LCB0b3dhcmRzIC1JbmZpbml0eS5cclxuICAgIC8vXHJcbiAgICAvLyBFLmcuXHJcbiAgICAvLyBgRGVjaW1hbC5yb3VuZGluZyA9IDQ7YFxyXG4gICAgLy8gYERlY2ltYWwucm91bmRpbmcgPSBEZWNpbWFsLlJPVU5EX0hBTEZfVVA7YFxyXG4gICAgcm91bmRpbmc6IDQsICAgICAgICAgICAgICAgICAgICAgICAgICAgLy8gMCB0byA4XHJcblxyXG4gICAgLy8gVGhlIGV4cG9uZW50IHZhbHVlIGF0IGFuZCBiZW5lYXRoIHdoaWNoIGB0b1N0cmluZ2AgcmV0dXJucyBleHBvbmVudGlhbCBub3RhdGlvbi5cclxuICAgIC8vIEphdmFTY3JpcHQgbnVtYmVyczogLTdcclxuICAgIHRvRXhwTmVnOiAtNywgICAgICAgICAgICAgICAgICAgICAgICAgIC8vIDAgdG8gLU1BWF9FXHJcblxyXG4gICAgLy8gVGhlIGV4cG9uZW50IHZhbHVlIGF0IGFuZCBhYm92ZSB3aGljaCBgdG9TdHJpbmdgIHJldHVybnMgZXhwb25lbnRpYWwgbm90YXRpb24uXHJcbiAgICAvLyBKYXZhU2NyaXB0IG51bWJlcnM6IDIxXHJcbiAgICB0b0V4cFBvczogIDIxLCAgICAgICAgICAgICAgICAgICAgICAgICAvLyAwIHRvIE1BWF9FXHJcblxyXG4gICAgLy8gVGhlIG5hdHVyYWwgbG9nYXJpdGhtIG9mIDEwLlxyXG4gICAgLy8gMTE1IGRpZ2l0c1xyXG4gICAgTE4xMDogJzIuMzAyNTg1MDkyOTk0MDQ1Njg0MDE3OTkxNDU0Njg0MzY0MjA3NjAxMTAxNDg4NjI4NzcyOTc2MDMzMzI3OTAwOTY3NTcyNjA5Njc3MzUyNDgwMjM1OTk3MjA1MDg5NTk4Mjk4MzQxOTY3Nzg0MDQyMjg2J1xyXG4gIH0sXHJcblxyXG5cclxuLy8gLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tIEVORCBPRiBFRElUQUJMRSBERUZBVUxUUyAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSAvL1xyXG5cclxuXHJcbiAgRGVjaW1hbCxcclxuICBleHRlcm5hbCA9IHRydWUsXHJcblxyXG4gIGRlY2ltYWxFcnJvciA9ICdbRGVjaW1hbEVycm9yXSAnLFxyXG4gIGludmFsaWRBcmd1bWVudCA9IGRlY2ltYWxFcnJvciArICdJbnZhbGlkIGFyZ3VtZW50OiAnLFxyXG4gIGV4cG9uZW50T3V0T2ZSYW5nZSA9IGRlY2ltYWxFcnJvciArICdFeHBvbmVudCBvdXQgb2YgcmFuZ2U6ICcsXHJcblxyXG4gIG1hdGhmbG9vciA9IE1hdGguZmxvb3IsXHJcbiAgbWF0aHBvdyA9IE1hdGgucG93LFxyXG5cclxuICBpc0RlY2ltYWwgPSAvXihcXGQrKFxcLlxcZCopP3xcXC5cXGQrKShlWystXT9cXGQrKT8kL2ksXHJcblxyXG4gIE9ORSxcclxuICBCQVNFID0gMWU3LFxyXG4gIExPR19CQVNFID0gNyxcclxuICBNQVhfU0FGRV9JTlRFR0VSID0gOTAwNzE5OTI1NDc0MDk5MSxcclxuICBNQVhfRSA9IG1hdGhmbG9vcihNQVhfU0FGRV9JTlRFR0VSIC8gTE9HX0JBU0UpLCAgICAvLyAxMjg2NzQyNzUwNjc3Mjg0XHJcblxyXG4gIC8vIERlY2ltYWwucHJvdG90eXBlIG9iamVjdFxyXG4gIFAgPSB7fTtcclxuXHJcblxyXG4vLyBEZWNpbWFsIHByb3RvdHlwZSBtZXRob2RzXHJcblxyXG5cclxuLypcclxuICogIGFic29sdXRlVmFsdWUgICAgICAgICAgICAgICAgICAgICAgIGFic1xyXG4gKiAgY29tcGFyZWRUbyAgICAgICAgICAgICAgICAgICAgICAgICAgY21wXHJcbiAqICBkZWNpbWFsUGxhY2VzICAgICAgICAgICAgICAgICAgICAgICBkcFxyXG4gKiAgZGl2aWRlZEJ5ICAgICAgICAgICAgICAgICAgICAgICAgICAgZGl2XHJcbiAqICBkaXZpZGVkVG9JbnRlZ2VyQnkgICAgICAgICAgICAgICAgICBpZGl2XHJcbiAqICBlcXVhbHMgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBlcVxyXG4gKiAgZXhwb25lbnRcclxuICogIGdyZWF0ZXJUaGFuICAgICAgICAgICAgICAgICAgICAgICAgIGd0XHJcbiAqICBncmVhdGVyVGhhbk9yRXF1YWxUbyAgICAgICAgICAgICAgICBndGVcclxuICogIGlzSW50ZWdlciAgICAgICAgICAgICAgICAgICAgICAgICAgIGlzaW50XHJcbiAqICBpc05lZ2F0aXZlICAgICAgICAgICAgICAgICAgICAgICAgICBpc25lZ1xyXG4gKiAgaXNQb3NpdGl2ZSAgICAgICAgICAgICAgICAgICAgICAgICAgaXNwb3NcclxuICogIGlzWmVyb1xyXG4gKiAgbGVzc1RoYW4gICAgICAgICAgICAgICAgICAgICAgICAgICAgbHRcclxuICogIGxlc3NUaGFuT3JFcXVhbFRvICAgICAgICAgICAgICAgICAgIGx0ZVxyXG4gKiAgbG9nYXJpdGhtICAgICAgICAgICAgICAgICAgICAgICAgICAgbG9nXHJcbiAqICBtaW51cyAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBzdWJcclxuICogIG1vZHVsbyAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1vZFxyXG4gKiAgbmF0dXJhbEV4cG9uZW50aWFsICAgICAgICAgICAgICAgICAgZXhwXHJcbiAqICBuYXR1cmFsTG9nYXJpdGhtICAgICAgICAgICAgICAgICAgICBsblxyXG4gKiAgbmVnYXRlZCAgICAgICAgICAgICAgICAgICAgICAgICAgICAgbmVnXHJcbiAqICBwbHVzICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBhZGRcclxuICogIHByZWNpc2lvbiAgICAgICAgICAgICAgICAgICAgICAgICAgIHNkXHJcbiAqICBzcXVhcmVSb290ICAgICAgICAgICAgICAgICAgICAgICAgICBzcXJ0XHJcbiAqICB0aW1lcyAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBtdWxcclxuICogIHRvRGVjaW1hbFBsYWNlcyAgICAgICAgICAgICAgICAgICAgIHRvZHBcclxuICogIHRvRXhwb25lbnRpYWxcclxuICogIHRvRml4ZWRcclxuICogIHRvSW50ZWdlciAgICAgICAgICAgICAgICAgICAgICAgICAgIHRvaW50XHJcbiAqICB0b051bWJlclxyXG4gKiAgdG9Qb3dlciAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcG93XHJcbiAqICB0b1ByZWNpc2lvblxyXG4gKiAgdG9TaWduaWZpY2FudERpZ2l0cyAgICAgICAgICAgICAgICAgdG9zZFxyXG4gKiAgdG9TdHJpbmdcclxuICogIHZhbHVlT2YgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHZhbFxyXG4gKi9cclxuXHJcblxyXG4vKlxyXG4gKiBSZXR1cm4gYSBuZXcgRGVjaW1hbCB3aG9zZSB2YWx1ZSBpcyB0aGUgYWJzb2x1dGUgdmFsdWUgb2YgdGhpcyBEZWNpbWFsLlxyXG4gKlxyXG4gKi9cclxuUC5hYnNvbHV0ZVZhbHVlID0gUC5hYnMgPSBmdW5jdGlvbiAoKSB7XHJcbiAgdmFyIHggPSBuZXcgdGhpcy5jb25zdHJ1Y3Rvcih0aGlzKTtcclxuICBpZiAoeC5zKSB4LnMgPSAxO1xyXG4gIHJldHVybiB4O1xyXG59O1xyXG5cclxuXHJcbi8qXHJcbiAqIFJldHVyblxyXG4gKiAgIDEgICAgaWYgdGhlIHZhbHVlIG9mIHRoaXMgRGVjaW1hbCBpcyBncmVhdGVyIHRoYW4gdGhlIHZhbHVlIG9mIGB5YCxcclxuICogIC0xICAgIGlmIHRoZSB2YWx1ZSBvZiB0aGlzIERlY2ltYWwgaXMgbGVzcyB0aGFuIHRoZSB2YWx1ZSBvZiBgeWAsXHJcbiAqICAgMCAgICBpZiB0aGV5IGhhdmUgdGhlIHNhbWUgdmFsdWVcclxuICpcclxuICovXHJcblAuY29tcGFyZWRUbyA9IFAuY21wID0gZnVuY3Rpb24gKHkpIHtcclxuICB2YXIgaSwgaiwgeGRMLCB5ZEwsXHJcbiAgICB4ID0gdGhpcztcclxuXHJcbiAgeSA9IG5ldyB4LmNvbnN0cnVjdG9yKHkpO1xyXG5cclxuICAvLyBTaWducyBkaWZmZXI/XHJcbiAgaWYgKHgucyAhPT0geS5zKSByZXR1cm4geC5zIHx8IC15LnM7XHJcblxyXG4gIC8vIENvbXBhcmUgZXhwb25lbnRzLlxyXG4gIGlmICh4LmUgIT09IHkuZSkgcmV0dXJuIHguZSA+IHkuZSBeIHgucyA8IDAgPyAxIDogLTE7XHJcblxyXG4gIHhkTCA9IHguZC5sZW5ndGg7XHJcbiAgeWRMID0geS5kLmxlbmd0aDtcclxuXHJcbiAgLy8gQ29tcGFyZSBkaWdpdCBieSBkaWdpdC5cclxuICBmb3IgKGkgPSAwLCBqID0geGRMIDwgeWRMID8geGRMIDogeWRMOyBpIDwgajsgKytpKSB7XHJcbiAgICBpZiAoeC5kW2ldICE9PSB5LmRbaV0pIHJldHVybiB4LmRbaV0gPiB5LmRbaV0gXiB4LnMgPCAwID8gMSA6IC0xO1xyXG4gIH1cclxuXHJcbiAgLy8gQ29tcGFyZSBsZW5ndGhzLlxyXG4gIHJldHVybiB4ZEwgPT09IHlkTCA/IDAgOiB4ZEwgPiB5ZEwgXiB4LnMgPCAwID8gMSA6IC0xO1xyXG59O1xyXG5cclxuXHJcbi8qXHJcbiAqIFJldHVybiB0aGUgbnVtYmVyIG9mIGRlY2ltYWwgcGxhY2VzIG9mIHRoZSB2YWx1ZSBvZiB0aGlzIERlY2ltYWwuXHJcbiAqXHJcbiAqL1xyXG5QLmRlY2ltYWxQbGFjZXMgPSBQLmRwID0gZnVuY3Rpb24gKCkge1xyXG4gIHZhciB4ID0gdGhpcyxcclxuICAgIHcgPSB4LmQubGVuZ3RoIC0gMSxcclxuICAgIGRwID0gKHcgLSB4LmUpICogTE9HX0JBU0U7XHJcblxyXG4gIC8vIFN1YnRyYWN0IHRoZSBudW1iZXIgb2YgdHJhaWxpbmcgemVyb3Mgb2YgdGhlIGxhc3Qgd29yZC5cclxuICB3ID0geC5kW3ddO1xyXG4gIGlmICh3KSBmb3IgKDsgdyAlIDEwID09IDA7IHcgLz0gMTApIGRwLS07XHJcblxyXG4gIHJldHVybiBkcCA8IDAgPyAwIDogZHA7XHJcbn07XHJcblxyXG5cclxuLypcclxuICogUmV0dXJuIGEgbmV3IERlY2ltYWwgd2hvc2UgdmFsdWUgaXMgdGhlIHZhbHVlIG9mIHRoaXMgRGVjaW1hbCBkaXZpZGVkIGJ5IGB5YCwgdHJ1bmNhdGVkIHRvXHJcbiAqIGBwcmVjaXNpb25gIHNpZ25pZmljYW50IGRpZ2l0cy5cclxuICpcclxuICovXHJcblAuZGl2aWRlZEJ5ID0gUC5kaXYgPSBmdW5jdGlvbiAoeSkge1xyXG4gIHJldHVybiBkaXZpZGUodGhpcywgbmV3IHRoaXMuY29uc3RydWN0b3IoeSkpO1xyXG59O1xyXG5cclxuXHJcbi8qXHJcbiAqIFJldHVybiBhIG5ldyBEZWNpbWFsIHdob3NlIHZhbHVlIGlzIHRoZSBpbnRlZ2VyIHBhcnQgb2YgZGl2aWRpbmcgdGhlIHZhbHVlIG9mIHRoaXMgRGVjaW1hbFxyXG4gKiBieSB0aGUgdmFsdWUgb2YgYHlgLCB0cnVuY2F0ZWQgdG8gYHByZWNpc2lvbmAgc2lnbmlmaWNhbnQgZGlnaXRzLlxyXG4gKlxyXG4gKi9cclxuUC5kaXZpZGVkVG9JbnRlZ2VyQnkgPSBQLmlkaXYgPSBmdW5jdGlvbiAoeSkge1xyXG4gIHZhciB4ID0gdGhpcyxcclxuICAgIEN0b3IgPSB4LmNvbnN0cnVjdG9yO1xyXG4gIHJldHVybiByb3VuZChkaXZpZGUoeCwgbmV3IEN0b3IoeSksIDAsIDEpLCBDdG9yLnByZWNpc2lvbik7XHJcbn07XHJcblxyXG5cclxuLypcclxuICogUmV0dXJuIHRydWUgaWYgdGhlIHZhbHVlIG9mIHRoaXMgRGVjaW1hbCBpcyBlcXVhbCB0byB0aGUgdmFsdWUgb2YgYHlgLCBvdGhlcndpc2UgcmV0dXJuIGZhbHNlLlxyXG4gKlxyXG4gKi9cclxuUC5lcXVhbHMgPSBQLmVxID0gZnVuY3Rpb24gKHkpIHtcclxuICByZXR1cm4gIXRoaXMuY21wKHkpO1xyXG59O1xyXG5cclxuXHJcbi8qXHJcbiAqIFJldHVybiB0aGUgKGJhc2UgMTApIGV4cG9uZW50IHZhbHVlIG9mIHRoaXMgRGVjaW1hbCAodGhpcy5lIGlzIHRoZSBiYXNlIDEwMDAwMDAwIGV4cG9uZW50KS5cclxuICpcclxuICovXHJcblAuZXhwb25lbnQgPSBmdW5jdGlvbiAoKSB7XHJcbiAgcmV0dXJuIGdldEJhc2UxMEV4cG9uZW50KHRoaXMpO1xyXG59O1xyXG5cclxuXHJcbi8qXHJcbiAqIFJldHVybiB0cnVlIGlmIHRoZSB2YWx1ZSBvZiB0aGlzIERlY2ltYWwgaXMgZ3JlYXRlciB0aGFuIHRoZSB2YWx1ZSBvZiBgeWAsIG90aGVyd2lzZSByZXR1cm5cclxuICogZmFsc2UuXHJcbiAqXHJcbiAqL1xyXG5QLmdyZWF0ZXJUaGFuID0gUC5ndCA9IGZ1bmN0aW9uICh5KSB7XHJcbiAgcmV0dXJuIHRoaXMuY21wKHkpID4gMDtcclxufTtcclxuXHJcblxyXG4vKlxyXG4gKiBSZXR1cm4gdHJ1ZSBpZiB0aGUgdmFsdWUgb2YgdGhpcyBEZWNpbWFsIGlzIGdyZWF0ZXIgdGhhbiBvciBlcXVhbCB0byB0aGUgdmFsdWUgb2YgYHlgLFxyXG4gKiBvdGhlcndpc2UgcmV0dXJuIGZhbHNlLlxyXG4gKlxyXG4gKi9cclxuUC5ncmVhdGVyVGhhbk9yRXF1YWxUbyA9IFAuZ3RlID0gZnVuY3Rpb24gKHkpIHtcclxuICByZXR1cm4gdGhpcy5jbXAoeSkgPj0gMDtcclxufTtcclxuXHJcblxyXG4vKlxyXG4gKiBSZXR1cm4gdHJ1ZSBpZiB0aGUgdmFsdWUgb2YgdGhpcyBEZWNpbWFsIGlzIGFuIGludGVnZXIsIG90aGVyd2lzZSByZXR1cm4gZmFsc2UuXHJcbiAqXHJcbiAqL1xyXG5QLmlzSW50ZWdlciA9IFAuaXNpbnQgPSBmdW5jdGlvbiAoKSB7XHJcbiAgcmV0dXJuIHRoaXMuZSA+IHRoaXMuZC5sZW5ndGggLSAyO1xyXG59O1xyXG5cclxuXHJcbi8qXHJcbiAqIFJldHVybiB0cnVlIGlmIHRoZSB2YWx1ZSBvZiB0aGlzIERlY2ltYWwgaXMgbmVnYXRpdmUsIG90aGVyd2lzZSByZXR1cm4gZmFsc2UuXHJcbiAqXHJcbiAqL1xyXG5QLmlzTmVnYXRpdmUgPSBQLmlzbmVnID0gZnVuY3Rpb24gKCkge1xyXG4gIHJldHVybiB0aGlzLnMgPCAwO1xyXG59O1xyXG5cclxuXHJcbi8qXHJcbiAqIFJldHVybiB0cnVlIGlmIHRoZSB2YWx1ZSBvZiB0aGlzIERlY2ltYWwgaXMgcG9zaXRpdmUsIG90aGVyd2lzZSByZXR1cm4gZmFsc2UuXHJcbiAqXHJcbiAqL1xyXG5QLmlzUG9zaXRpdmUgPSBQLmlzcG9zID0gZnVuY3Rpb24gKCkge1xyXG4gIHJldHVybiB0aGlzLnMgPiAwO1xyXG59O1xyXG5cclxuXHJcbi8qXHJcbiAqIFJldHVybiB0cnVlIGlmIHRoZSB2YWx1ZSBvZiB0aGlzIERlY2ltYWwgaXMgMCwgb3RoZXJ3aXNlIHJldHVybiBmYWxzZS5cclxuICpcclxuICovXHJcblAuaXNaZXJvID0gZnVuY3Rpb24gKCkge1xyXG4gIHJldHVybiB0aGlzLnMgPT09IDA7XHJcbn07XHJcblxyXG5cclxuLypcclxuICogUmV0dXJuIHRydWUgaWYgdGhlIHZhbHVlIG9mIHRoaXMgRGVjaW1hbCBpcyBsZXNzIHRoYW4gYHlgLCBvdGhlcndpc2UgcmV0dXJuIGZhbHNlLlxyXG4gKlxyXG4gKi9cclxuUC5sZXNzVGhhbiA9IFAubHQgPSBmdW5jdGlvbiAoeSkge1xyXG4gIHJldHVybiB0aGlzLmNtcCh5KSA8IDA7XHJcbn07XHJcblxyXG5cclxuLypcclxuICogUmV0dXJuIHRydWUgaWYgdGhlIHZhbHVlIG9mIHRoaXMgRGVjaW1hbCBpcyBsZXNzIHRoYW4gb3IgZXF1YWwgdG8gYHlgLCBvdGhlcndpc2UgcmV0dXJuIGZhbHNlLlxyXG4gKlxyXG4gKi9cclxuUC5sZXNzVGhhbk9yRXF1YWxUbyA9IFAubHRlID0gZnVuY3Rpb24gKHkpIHtcclxuICByZXR1cm4gdGhpcy5jbXAoeSkgPCAxO1xyXG59O1xyXG5cclxuXHJcbi8qXHJcbiAqIFJldHVybiB0aGUgbG9nYXJpdGhtIG9mIHRoZSB2YWx1ZSBvZiB0aGlzIERlY2ltYWwgdG8gdGhlIHNwZWNpZmllZCBiYXNlLCB0cnVuY2F0ZWQgdG9cclxuICogYHByZWNpc2lvbmAgc2lnbmlmaWNhbnQgZGlnaXRzLlxyXG4gKlxyXG4gKiBJZiBubyBiYXNlIGlzIHNwZWNpZmllZCwgcmV0dXJuIGxvZ1sxMF0oeCkuXHJcbiAqXHJcbiAqIGxvZ1tiYXNlXSh4KSA9IGxuKHgpIC8gbG4oYmFzZSlcclxuICpcclxuICogVGhlIG1heGltdW0gZXJyb3Igb2YgdGhlIHJlc3VsdCBpcyAxIHVscCAodW5pdCBpbiB0aGUgbGFzdCBwbGFjZSkuXHJcbiAqXHJcbiAqIFtiYXNlXSB7bnVtYmVyfHN0cmluZ3xEZWNpbWFsfSBUaGUgYmFzZSBvZiB0aGUgbG9nYXJpdGhtLlxyXG4gKlxyXG4gKi9cclxuUC5sb2dhcml0aG0gPSBQLmxvZyA9IGZ1bmN0aW9uIChiYXNlKSB7XHJcbiAgdmFyIHIsXHJcbiAgICB4ID0gdGhpcyxcclxuICAgIEN0b3IgPSB4LmNvbnN0cnVjdG9yLFxyXG4gICAgcHIgPSBDdG9yLnByZWNpc2lvbixcclxuICAgIHdwciA9IHByICsgNTtcclxuXHJcbiAgLy8gRGVmYXVsdCBiYXNlIGlzIDEwLlxyXG4gIGlmIChiYXNlID09PSB2b2lkIDApIHtcclxuICAgIGJhc2UgPSBuZXcgQ3RvcigxMCk7XHJcbiAgfSBlbHNlIHtcclxuICAgIGJhc2UgPSBuZXcgQ3RvcihiYXNlKTtcclxuXHJcbiAgICAvLyBsb2dbLWJdKHgpID0gTmFOXHJcbiAgICAvLyBsb2dbMF0oeCkgID0gTmFOXHJcbiAgICAvLyBsb2dbMV0oeCkgID0gTmFOXHJcbiAgICBpZiAoYmFzZS5zIDwgMSB8fCBiYXNlLmVxKE9ORSkpIHRocm93IEVycm9yKGRlY2ltYWxFcnJvciArICdOYU4nKTtcclxuICB9XHJcblxyXG4gIC8vIGxvZ1tiXSgteCkgPSBOYU5cclxuICAvLyBsb2dbYl0oMCkgPSAtSW5maW5pdHlcclxuICBpZiAoeC5zIDwgMSkgdGhyb3cgRXJyb3IoZGVjaW1hbEVycm9yICsgKHgucyA/ICdOYU4nIDogJy1JbmZpbml0eScpKTtcclxuXHJcbiAgLy8gbG9nW2JdKDEpID0gMFxyXG4gIGlmICh4LmVxKE9ORSkpIHJldHVybiBuZXcgQ3RvcigwKTtcclxuXHJcbiAgZXh0ZXJuYWwgPSBmYWxzZTtcclxuICByID0gZGl2aWRlKGxuKHgsIHdwciksIGxuKGJhc2UsIHdwciksIHdwcik7XHJcbiAgZXh0ZXJuYWwgPSB0cnVlO1xyXG5cclxuICByZXR1cm4gcm91bmQociwgcHIpO1xyXG59O1xyXG5cclxuXHJcbi8qXHJcbiAqIFJldHVybiBhIG5ldyBEZWNpbWFsIHdob3NlIHZhbHVlIGlzIHRoZSB2YWx1ZSBvZiB0aGlzIERlY2ltYWwgbWludXMgYHlgLCB0cnVuY2F0ZWQgdG9cclxuICogYHByZWNpc2lvbmAgc2lnbmlmaWNhbnQgZGlnaXRzLlxyXG4gKlxyXG4gKi9cclxuUC5taW51cyA9IFAuc3ViID0gZnVuY3Rpb24gKHkpIHtcclxuICB2YXIgeCA9IHRoaXM7XHJcbiAgeSA9IG5ldyB4LmNvbnN0cnVjdG9yKHkpO1xyXG4gIHJldHVybiB4LnMgPT0geS5zID8gc3VidHJhY3QoeCwgeSkgOiBhZGQoeCwgKHkucyA9IC15LnMsIHkpKTtcclxufTtcclxuXHJcblxyXG4vKlxyXG4gKiBSZXR1cm4gYSBuZXcgRGVjaW1hbCB3aG9zZSB2YWx1ZSBpcyB0aGUgdmFsdWUgb2YgdGhpcyBEZWNpbWFsIG1vZHVsbyBgeWAsIHRydW5jYXRlZCB0b1xyXG4gKiBgcHJlY2lzaW9uYCBzaWduaWZpY2FudCBkaWdpdHMuXHJcbiAqXHJcbiAqL1xyXG5QLm1vZHVsbyA9IFAubW9kID0gZnVuY3Rpb24gKHkpIHtcclxuICB2YXIgcSxcclxuICAgIHggPSB0aGlzLFxyXG4gICAgQ3RvciA9IHguY29uc3RydWN0b3IsXHJcbiAgICBwciA9IEN0b3IucHJlY2lzaW9uO1xyXG5cclxuICB5ID0gbmV3IEN0b3IoeSk7XHJcblxyXG4gIC8vIHggJSAwID0gTmFOXHJcbiAgaWYgKCF5LnMpIHRocm93IEVycm9yKGRlY2ltYWxFcnJvciArICdOYU4nKTtcclxuXHJcbiAgLy8gUmV0dXJuIHggaWYgeCBpcyAwLlxyXG4gIGlmICgheC5zKSByZXR1cm4gcm91bmQobmV3IEN0b3IoeCksIHByKTtcclxuXHJcbiAgLy8gUHJldmVudCByb3VuZGluZyBvZiBpbnRlcm1lZGlhdGUgY2FsY3VsYXRpb25zLlxyXG4gIGV4dGVybmFsID0gZmFsc2U7XHJcbiAgcSA9IGRpdmlkZSh4LCB5LCAwLCAxKS50aW1lcyh5KTtcclxuICBleHRlcm5hbCA9IHRydWU7XHJcblxyXG4gIHJldHVybiB4Lm1pbnVzKHEpO1xyXG59O1xyXG5cclxuXHJcbi8qXHJcbiAqIFJldHVybiBhIG5ldyBEZWNpbWFsIHdob3NlIHZhbHVlIGlzIHRoZSBuYXR1cmFsIGV4cG9uZW50aWFsIG9mIHRoZSB2YWx1ZSBvZiB0aGlzIERlY2ltYWwsXHJcbiAqIGkuZS4gdGhlIGJhc2UgZSByYWlzZWQgdG8gdGhlIHBvd2VyIHRoZSB2YWx1ZSBvZiB0aGlzIERlY2ltYWwsIHRydW5jYXRlZCB0byBgcHJlY2lzaW9uYFxyXG4gKiBzaWduaWZpY2FudCBkaWdpdHMuXHJcbiAqXHJcbiAqL1xyXG5QLm5hdHVyYWxFeHBvbmVudGlhbCA9IFAuZXhwID0gZnVuY3Rpb24gKCkge1xyXG4gIHJldHVybiBleHAodGhpcyk7XHJcbn07XHJcblxyXG5cclxuLypcclxuICogUmV0dXJuIGEgbmV3IERlY2ltYWwgd2hvc2UgdmFsdWUgaXMgdGhlIG5hdHVyYWwgbG9nYXJpdGhtIG9mIHRoZSB2YWx1ZSBvZiB0aGlzIERlY2ltYWwsXHJcbiAqIHRydW5jYXRlZCB0byBgcHJlY2lzaW9uYCBzaWduaWZpY2FudCBkaWdpdHMuXHJcbiAqXHJcbiAqL1xyXG5QLm5hdHVyYWxMb2dhcml0aG0gPSBQLmxuID0gZnVuY3Rpb24gKCkge1xyXG4gIHJldHVybiBsbih0aGlzKTtcclxufTtcclxuXHJcblxyXG4vKlxyXG4gKiBSZXR1cm4gYSBuZXcgRGVjaW1hbCB3aG9zZSB2YWx1ZSBpcyB0aGUgdmFsdWUgb2YgdGhpcyBEZWNpbWFsIG5lZ2F0ZWQsIGkuZS4gYXMgaWYgbXVsdGlwbGllZCBieVxyXG4gKiAtMS5cclxuICpcclxuICovXHJcblAubmVnYXRlZCA9IFAubmVnID0gZnVuY3Rpb24gKCkge1xyXG4gIHZhciB4ID0gbmV3IHRoaXMuY29uc3RydWN0b3IodGhpcyk7XHJcbiAgeC5zID0gLXgucyB8fCAwO1xyXG4gIHJldHVybiB4O1xyXG59O1xyXG5cclxuXHJcbi8qXHJcbiAqIFJldHVybiBhIG5ldyBEZWNpbWFsIHdob3NlIHZhbHVlIGlzIHRoZSB2YWx1ZSBvZiB0aGlzIERlY2ltYWwgcGx1cyBgeWAsIHRydW5jYXRlZCB0b1xyXG4gKiBgcHJlY2lzaW9uYCBzaWduaWZpY2FudCBkaWdpdHMuXHJcbiAqXHJcbiAqL1xyXG5QLnBsdXMgPSBQLmFkZCA9IGZ1bmN0aW9uICh5KSB7XHJcbiAgdmFyIHggPSB0aGlzO1xyXG4gIHkgPSBuZXcgeC5jb25zdHJ1Y3Rvcih5KTtcclxuICByZXR1cm4geC5zID09IHkucyA/IGFkZCh4LCB5KSA6IHN1YnRyYWN0KHgsICh5LnMgPSAteS5zLCB5KSk7XHJcbn07XHJcblxyXG5cclxuLypcclxuICogUmV0dXJuIHRoZSBudW1iZXIgb2Ygc2lnbmlmaWNhbnQgZGlnaXRzIG9mIHRoZSB2YWx1ZSBvZiB0aGlzIERlY2ltYWwuXHJcbiAqXHJcbiAqIFt6XSB7Ym9vbGVhbnxudW1iZXJ9IFdoZXRoZXIgdG8gY291bnQgaW50ZWdlci1wYXJ0IHRyYWlsaW5nIHplcm9zOiB0cnVlLCBmYWxzZSwgMSBvciAwLlxyXG4gKlxyXG4gKi9cclxuUC5wcmVjaXNpb24gPSBQLnNkID0gZnVuY3Rpb24gKHopIHtcclxuICB2YXIgZSwgc2QsIHcsXHJcbiAgICB4ID0gdGhpcztcclxuXHJcbiAgaWYgKHogIT09IHZvaWQgMCAmJiB6ICE9PSAhIXogJiYgeiAhPT0gMSAmJiB6ICE9PSAwKSB0aHJvdyBFcnJvcihpbnZhbGlkQXJndW1lbnQgKyB6KTtcclxuXHJcbiAgZSA9IGdldEJhc2UxMEV4cG9uZW50KHgpICsgMTtcclxuICB3ID0geC5kLmxlbmd0aCAtIDE7XHJcbiAgc2QgPSB3ICogTE9HX0JBU0UgKyAxO1xyXG4gIHcgPSB4LmRbd107XHJcblxyXG4gIC8vIElmIG5vbi16ZXJvLi4uXHJcbiAgaWYgKHcpIHtcclxuXHJcbiAgICAvLyBTdWJ0cmFjdCB0aGUgbnVtYmVyIG9mIHRyYWlsaW5nIHplcm9zIG9mIHRoZSBsYXN0IHdvcmQuXHJcbiAgICBmb3IgKDsgdyAlIDEwID09IDA7IHcgLz0gMTApIHNkLS07XHJcblxyXG4gICAgLy8gQWRkIHRoZSBudW1iZXIgb2YgZGlnaXRzIG9mIHRoZSBmaXJzdCB3b3JkLlxyXG4gICAgZm9yICh3ID0geC5kWzBdOyB3ID49IDEwOyB3IC89IDEwKSBzZCsrO1xyXG4gIH1cclxuXHJcbiAgcmV0dXJuIHogJiYgZSA+IHNkID8gZSA6IHNkO1xyXG59O1xyXG5cclxuXHJcbi8qXHJcbiAqIFJldHVybiBhIG5ldyBEZWNpbWFsIHdob3NlIHZhbHVlIGlzIHRoZSBzcXVhcmUgcm9vdCBvZiB0aGlzIERlY2ltYWwsIHRydW5jYXRlZCB0byBgcHJlY2lzaW9uYFxyXG4gKiBzaWduaWZpY2FudCBkaWdpdHMuXHJcbiAqXHJcbiAqL1xyXG5QLnNxdWFyZVJvb3QgPSBQLnNxcnQgPSBmdW5jdGlvbiAoKSB7XHJcbiAgdmFyIGUsIG4sIHByLCByLCBzLCB0LCB3cHIsXHJcbiAgICB4ID0gdGhpcyxcclxuICAgIEN0b3IgPSB4LmNvbnN0cnVjdG9yO1xyXG5cclxuICAvLyBOZWdhdGl2ZSBvciB6ZXJvP1xyXG4gIGlmICh4LnMgPCAxKSB7XHJcbiAgICBpZiAoIXgucykgcmV0dXJuIG5ldyBDdG9yKDApO1xyXG5cclxuICAgIC8vIHNxcnQoLXgpID0gTmFOXHJcbiAgICB0aHJvdyBFcnJvcihkZWNpbWFsRXJyb3IgKyAnTmFOJyk7XHJcbiAgfVxyXG5cclxuICBlID0gZ2V0QmFzZTEwRXhwb25lbnQoeCk7XHJcbiAgZXh0ZXJuYWwgPSBmYWxzZTtcclxuXHJcbiAgLy8gSW5pdGlhbCBlc3RpbWF0ZS5cclxuICBzID0gTWF0aC5zcXJ0KCt4KTtcclxuXHJcbiAgLy8gTWF0aC5zcXJ0IHVuZGVyZmxvdy9vdmVyZmxvdz9cclxuICAvLyBQYXNzIHggdG8gTWF0aC5zcXJ0IGFzIGludGVnZXIsIHRoZW4gYWRqdXN0IHRoZSBleHBvbmVudCBvZiB0aGUgcmVzdWx0LlxyXG4gIGlmIChzID09IDAgfHwgcyA9PSAxIC8gMCkge1xyXG4gICAgbiA9IGRpZ2l0c1RvU3RyaW5nKHguZCk7XHJcbiAgICBpZiAoKG4ubGVuZ3RoICsgZSkgJSAyID09IDApIG4gKz0gJzAnO1xyXG4gICAgcyA9IE1hdGguc3FydChuKTtcclxuICAgIGUgPSBtYXRoZmxvb3IoKGUgKyAxKSAvIDIpIC0gKGUgPCAwIHx8IGUgJSAyKTtcclxuXHJcbiAgICBpZiAocyA9PSAxIC8gMCkge1xyXG4gICAgICBuID0gJzVlJyArIGU7XHJcbiAgICB9IGVsc2Uge1xyXG4gICAgICBuID0gcy50b0V4cG9uZW50aWFsKCk7XHJcbiAgICAgIG4gPSBuLnNsaWNlKDAsIG4uaW5kZXhPZignZScpICsgMSkgKyBlO1xyXG4gICAgfVxyXG5cclxuICAgIHIgPSBuZXcgQ3RvcihuKTtcclxuICB9IGVsc2Uge1xyXG4gICAgciA9IG5ldyBDdG9yKHMudG9TdHJpbmcoKSk7XHJcbiAgfVxyXG5cclxuICBwciA9IEN0b3IucHJlY2lzaW9uO1xyXG4gIHMgPSB3cHIgPSBwciArIDM7XHJcblxyXG4gIC8vIE5ld3Rvbi1SYXBoc29uIGl0ZXJhdGlvbi5cclxuICBmb3IgKDs7KSB7XHJcbiAgICB0ID0gcjtcclxuICAgIHIgPSB0LnBsdXMoZGl2aWRlKHgsIHQsIHdwciArIDIpKS50aW1lcygwLjUpO1xyXG5cclxuICAgIGlmIChkaWdpdHNUb1N0cmluZyh0LmQpLnNsaWNlKDAsIHdwcikgPT09IChuID0gZGlnaXRzVG9TdHJpbmcoci5kKSkuc2xpY2UoMCwgd3ByKSkge1xyXG4gICAgICBuID0gbi5zbGljZSh3cHIgLSAzLCB3cHIgKyAxKTtcclxuXHJcbiAgICAgIC8vIFRoZSA0dGggcm91bmRpbmcgZGlnaXQgbWF5IGJlIGluIGVycm9yIGJ5IC0xIHNvIGlmIHRoZSA0IHJvdW5kaW5nIGRpZ2l0cyBhcmUgOTk5OSBvclxyXG4gICAgICAvLyA0OTk5LCBpLmUuIGFwcHJvYWNoaW5nIGEgcm91bmRpbmcgYm91bmRhcnksIGNvbnRpbnVlIHRoZSBpdGVyYXRpb24uXHJcbiAgICAgIGlmIChzID09IHdwciAmJiBuID09ICc0OTk5Jykge1xyXG5cclxuICAgICAgICAvLyBPbiB0aGUgZmlyc3QgaXRlcmF0aW9uIG9ubHksIGNoZWNrIHRvIHNlZSBpZiByb3VuZGluZyB1cCBnaXZlcyB0aGUgZXhhY3QgcmVzdWx0IGFzIHRoZVxyXG4gICAgICAgIC8vIG5pbmVzIG1heSBpbmZpbml0ZWx5IHJlcGVhdC5cclxuICAgICAgICByb3VuZCh0LCBwciArIDEsIDApO1xyXG5cclxuICAgICAgICBpZiAodC50aW1lcyh0KS5lcSh4KSkge1xyXG4gICAgICAgICAgciA9IHQ7XHJcbiAgICAgICAgICBicmVhaztcclxuICAgICAgICB9XHJcbiAgICAgIH0gZWxzZSBpZiAobiAhPSAnOTk5OScpIHtcclxuICAgICAgICBicmVhaztcclxuICAgICAgfVxyXG5cclxuICAgICAgd3ByICs9IDQ7XHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICBleHRlcm5hbCA9IHRydWU7XHJcblxyXG4gIHJldHVybiByb3VuZChyLCBwcik7XHJcbn07XHJcblxyXG5cclxuLypcclxuICogUmV0dXJuIGEgbmV3IERlY2ltYWwgd2hvc2UgdmFsdWUgaXMgdGhlIHZhbHVlIG9mIHRoaXMgRGVjaW1hbCB0aW1lcyBgeWAsIHRydW5jYXRlZCB0b1xyXG4gKiBgcHJlY2lzaW9uYCBzaWduaWZpY2FudCBkaWdpdHMuXHJcbiAqXHJcbiAqL1xyXG5QLnRpbWVzID0gUC5tdWwgPSBmdW5jdGlvbiAoeSkge1xyXG4gIHZhciBjYXJyeSwgZSwgaSwgaywgciwgckwsIHQsIHhkTCwgeWRMLFxyXG4gICAgeCA9IHRoaXMsXHJcbiAgICBDdG9yID0geC5jb25zdHJ1Y3RvcixcclxuICAgIHhkID0geC5kLFxyXG4gICAgeWQgPSAoeSA9IG5ldyBDdG9yKHkpKS5kO1xyXG5cclxuICAvLyBSZXR1cm4gMCBpZiBlaXRoZXIgaXMgMC5cclxuICBpZiAoIXgucyB8fCAheS5zKSByZXR1cm4gbmV3IEN0b3IoMCk7XHJcblxyXG4gIHkucyAqPSB4LnM7XHJcbiAgZSA9IHguZSArIHkuZTtcclxuICB4ZEwgPSB4ZC5sZW5ndGg7XHJcbiAgeWRMID0geWQubGVuZ3RoO1xyXG5cclxuICAvLyBFbnN1cmUgeGQgcG9pbnRzIHRvIHRoZSBsb25nZXIgYXJyYXkuXHJcbiAgaWYgKHhkTCA8IHlkTCkge1xyXG4gICAgciA9IHhkO1xyXG4gICAgeGQgPSB5ZDtcclxuICAgIHlkID0gcjtcclxuICAgIHJMID0geGRMO1xyXG4gICAgeGRMID0geWRMO1xyXG4gICAgeWRMID0gckw7XHJcbiAgfVxyXG5cclxuICAvLyBJbml0aWFsaXNlIHRoZSByZXN1bHQgYXJyYXkgd2l0aCB6ZXJvcy5cclxuICByID0gW107XHJcbiAgckwgPSB4ZEwgKyB5ZEw7XHJcbiAgZm9yIChpID0gckw7IGktLTspIHIucHVzaCgwKTtcclxuXHJcbiAgLy8gTXVsdGlwbHkhXHJcbiAgZm9yIChpID0geWRMOyAtLWkgPj0gMDspIHtcclxuICAgIGNhcnJ5ID0gMDtcclxuICAgIGZvciAoayA9IHhkTCArIGk7IGsgPiBpOykge1xyXG4gICAgICB0ID0gcltrXSArIHlkW2ldICogeGRbayAtIGkgLSAxXSArIGNhcnJ5O1xyXG4gICAgICByW2stLV0gPSB0ICUgQkFTRSB8IDA7XHJcbiAgICAgIGNhcnJ5ID0gdCAvIEJBU0UgfCAwO1xyXG4gICAgfVxyXG5cclxuICAgIHJba10gPSAocltrXSArIGNhcnJ5KSAlIEJBU0UgfCAwO1xyXG4gIH1cclxuXHJcbiAgLy8gUmVtb3ZlIHRyYWlsaW5nIHplcm9zLlxyXG4gIGZvciAoOyAhclstLXJMXTspIHIucG9wKCk7XHJcblxyXG4gIGlmIChjYXJyeSkgKytlO1xyXG4gIGVsc2Ugci5zaGlmdCgpO1xyXG5cclxuICB5LmQgPSByO1xyXG4gIHkuZSA9IGU7XHJcblxyXG4gIHJldHVybiBleHRlcm5hbCA/IHJvdW5kKHksIEN0b3IucHJlY2lzaW9uKSA6IHk7XHJcbn07XHJcblxyXG5cclxuLypcclxuICogUmV0dXJuIGEgbmV3IERlY2ltYWwgd2hvc2UgdmFsdWUgaXMgdGhlIHZhbHVlIG9mIHRoaXMgRGVjaW1hbCByb3VuZGVkIHRvIGEgbWF4aW11bSBvZiBgZHBgXHJcbiAqIGRlY2ltYWwgcGxhY2VzIHVzaW5nIHJvdW5kaW5nIG1vZGUgYHJtYCBvciBgcm91bmRpbmdgIGlmIGBybWAgaXMgb21pdHRlZC5cclxuICpcclxuICogSWYgYGRwYCBpcyBvbWl0dGVkLCByZXR1cm4gYSBuZXcgRGVjaW1hbCB3aG9zZSB2YWx1ZSBpcyB0aGUgdmFsdWUgb2YgdGhpcyBEZWNpbWFsLlxyXG4gKlxyXG4gKiBbZHBdIHtudW1iZXJ9IERlY2ltYWwgcGxhY2VzLiBJbnRlZ2VyLCAwIHRvIE1BWF9ESUdJVFMgaW5jbHVzaXZlLlxyXG4gKiBbcm1dIHtudW1iZXJ9IFJvdW5kaW5nIG1vZGUuIEludGVnZXIsIDAgdG8gOCBpbmNsdXNpdmUuXHJcbiAqXHJcbiAqL1xyXG5QLnRvRGVjaW1hbFBsYWNlcyA9IFAudG9kcCA9IGZ1bmN0aW9uIChkcCwgcm0pIHtcclxuICB2YXIgeCA9IHRoaXMsXHJcbiAgICBDdG9yID0geC5jb25zdHJ1Y3RvcjtcclxuXHJcbiAgeCA9IG5ldyBDdG9yKHgpO1xyXG4gIGlmIChkcCA9PT0gdm9pZCAwKSByZXR1cm4geDtcclxuXHJcbiAgY2hlY2tJbnQzMihkcCwgMCwgTUFYX0RJR0lUUyk7XHJcblxyXG4gIGlmIChybSA9PT0gdm9pZCAwKSBybSA9IEN0b3Iucm91bmRpbmc7XHJcbiAgZWxzZSBjaGVja0ludDMyKHJtLCAwLCA4KTtcclxuXHJcbiAgcmV0dXJuIHJvdW5kKHgsIGRwICsgZ2V0QmFzZTEwRXhwb25lbnQoeCkgKyAxLCBybSk7XHJcbn07XHJcblxyXG5cclxuLypcclxuICogUmV0dXJuIGEgc3RyaW5nIHJlcHJlc2VudGluZyB0aGUgdmFsdWUgb2YgdGhpcyBEZWNpbWFsIGluIGV4cG9uZW50aWFsIG5vdGF0aW9uIHJvdW5kZWQgdG9cclxuICogYGRwYCBmaXhlZCBkZWNpbWFsIHBsYWNlcyB1c2luZyByb3VuZGluZyBtb2RlIGByb3VuZGluZ2AuXHJcbiAqXHJcbiAqIFtkcF0ge251bWJlcn0gRGVjaW1hbCBwbGFjZXMuIEludGVnZXIsIDAgdG8gTUFYX0RJR0lUUyBpbmNsdXNpdmUuXHJcbiAqIFtybV0ge251bWJlcn0gUm91bmRpbmcgbW9kZS4gSW50ZWdlciwgMCB0byA4IGluY2x1c2l2ZS5cclxuICpcclxuICovXHJcblAudG9FeHBvbmVudGlhbCA9IGZ1bmN0aW9uIChkcCwgcm0pIHtcclxuICB2YXIgc3RyLFxyXG4gICAgeCA9IHRoaXMsXHJcbiAgICBDdG9yID0geC5jb25zdHJ1Y3RvcjtcclxuXHJcbiAgaWYgKGRwID09PSB2b2lkIDApIHtcclxuICAgIHN0ciA9IHRvU3RyaW5nKHgsIHRydWUpO1xyXG4gIH0gZWxzZSB7XHJcbiAgICBjaGVja0ludDMyKGRwLCAwLCBNQVhfRElHSVRTKTtcclxuXHJcbiAgICBpZiAocm0gPT09IHZvaWQgMCkgcm0gPSBDdG9yLnJvdW5kaW5nO1xyXG4gICAgZWxzZSBjaGVja0ludDMyKHJtLCAwLCA4KTtcclxuXHJcbiAgICB4ID0gcm91bmQobmV3IEN0b3IoeCksIGRwICsgMSwgcm0pO1xyXG4gICAgc3RyID0gdG9TdHJpbmcoeCwgdHJ1ZSwgZHAgKyAxKTtcclxuICB9XHJcblxyXG4gIHJldHVybiBzdHI7XHJcbn07XHJcblxyXG5cclxuLypcclxuICogUmV0dXJuIGEgc3RyaW5nIHJlcHJlc2VudGluZyB0aGUgdmFsdWUgb2YgdGhpcyBEZWNpbWFsIGluIG5vcm1hbCAoZml4ZWQtcG9pbnQpIG5vdGF0aW9uIHRvXHJcbiAqIGBkcGAgZml4ZWQgZGVjaW1hbCBwbGFjZXMgYW5kIHJvdW5kZWQgdXNpbmcgcm91bmRpbmcgbW9kZSBgcm1gIG9yIGByb3VuZGluZ2AgaWYgYHJtYCBpc1xyXG4gKiBvbWl0dGVkLlxyXG4gKlxyXG4gKiBBcyB3aXRoIEphdmFTY3JpcHQgbnVtYmVycywgKC0wKS50b0ZpeGVkKDApIGlzICcwJywgYnV0IGUuZy4gKC0wLjAwMDAxKS50b0ZpeGVkKDApIGlzICctMCcuXHJcbiAqXHJcbiAqIFtkcF0ge251bWJlcn0gRGVjaW1hbCBwbGFjZXMuIEludGVnZXIsIDAgdG8gTUFYX0RJR0lUUyBpbmNsdXNpdmUuXHJcbiAqIFtybV0ge251bWJlcn0gUm91bmRpbmcgbW9kZS4gSW50ZWdlciwgMCB0byA4IGluY2x1c2l2ZS5cclxuICpcclxuICogKC0wKS50b0ZpeGVkKDApIGlzICcwJywgYnV0ICgtMC4xKS50b0ZpeGVkKDApIGlzICctMCcuXHJcbiAqICgtMCkudG9GaXhlZCgxKSBpcyAnMC4wJywgYnV0ICgtMC4wMSkudG9GaXhlZCgxKSBpcyAnLTAuMCcuXHJcbiAqICgtMCkudG9GaXhlZCgzKSBpcyAnMC4wMDAnLlxyXG4gKiAoLTAuNSkudG9GaXhlZCgwKSBpcyAnLTAnLlxyXG4gKlxyXG4gKi9cclxuUC50b0ZpeGVkID0gZnVuY3Rpb24gKGRwLCBybSkge1xyXG4gIHZhciBzdHIsIHksXHJcbiAgICB4ID0gdGhpcyxcclxuICAgIEN0b3IgPSB4LmNvbnN0cnVjdG9yO1xyXG5cclxuICBpZiAoZHAgPT09IHZvaWQgMCkgcmV0dXJuIHRvU3RyaW5nKHgpO1xyXG5cclxuICBjaGVja0ludDMyKGRwLCAwLCBNQVhfRElHSVRTKTtcclxuXHJcbiAgaWYgKHJtID09PSB2b2lkIDApIHJtID0gQ3Rvci5yb3VuZGluZztcclxuICBlbHNlIGNoZWNrSW50MzIocm0sIDAsIDgpO1xyXG5cclxuICB5ID0gcm91bmQobmV3IEN0b3IoeCksIGRwICsgZ2V0QmFzZTEwRXhwb25lbnQoeCkgKyAxLCBybSk7XHJcbiAgc3RyID0gdG9TdHJpbmcoeS5hYnMoKSwgZmFsc2UsIGRwICsgZ2V0QmFzZTEwRXhwb25lbnQoeSkgKyAxKTtcclxuXHJcbiAgLy8gVG8gZGV0ZXJtaW5lIHdoZXRoZXIgdG8gYWRkIHRoZSBtaW51cyBzaWduIGxvb2sgYXQgdGhlIHZhbHVlIGJlZm9yZSBpdCB3YXMgcm91bmRlZCxcclxuICAvLyBpLmUuIGxvb2sgYXQgYHhgIHJhdGhlciB0aGFuIGB5YC5cclxuICByZXR1cm4geC5pc25lZygpICYmICF4LmlzWmVybygpID8gJy0nICsgc3RyIDogc3RyO1xyXG59O1xyXG5cclxuXHJcbi8qXHJcbiAqIFJldHVybiBhIG5ldyBEZWNpbWFsIHdob3NlIHZhbHVlIGlzIHRoZSB2YWx1ZSBvZiB0aGlzIERlY2ltYWwgcm91bmRlZCB0byBhIHdob2xlIG51bWJlciB1c2luZ1xyXG4gKiByb3VuZGluZyBtb2RlIGByb3VuZGluZ2AuXHJcbiAqXHJcbiAqL1xyXG5QLnRvSW50ZWdlciA9IFAudG9pbnQgPSBmdW5jdGlvbiAoKSB7XHJcbiAgdmFyIHggPSB0aGlzLFxyXG4gICAgQ3RvciA9IHguY29uc3RydWN0b3I7XHJcbiAgcmV0dXJuIHJvdW5kKG5ldyBDdG9yKHgpLCBnZXRCYXNlMTBFeHBvbmVudCh4KSArIDEsIEN0b3Iucm91bmRpbmcpO1xyXG59O1xyXG5cclxuXHJcbi8qXHJcbiAqIFJldHVybiB0aGUgdmFsdWUgb2YgdGhpcyBEZWNpbWFsIGNvbnZlcnRlZCB0byBhIG51bWJlciBwcmltaXRpdmUuXHJcbiAqXHJcbiAqL1xyXG5QLnRvTnVtYmVyID0gZnVuY3Rpb24gKCkge1xyXG4gIHJldHVybiArdGhpcztcclxufTtcclxuXHJcblxyXG4vKlxyXG4gKiBSZXR1cm4gYSBuZXcgRGVjaW1hbCB3aG9zZSB2YWx1ZSBpcyB0aGUgdmFsdWUgb2YgdGhpcyBEZWNpbWFsIHJhaXNlZCB0byB0aGUgcG93ZXIgYHlgLFxyXG4gKiB0cnVuY2F0ZWQgdG8gYHByZWNpc2lvbmAgc2lnbmlmaWNhbnQgZGlnaXRzLlxyXG4gKlxyXG4gKiBGb3Igbm9uLWludGVnZXIgb3IgdmVyeSBsYXJnZSBleHBvbmVudHMgcG93KHgsIHkpIGlzIGNhbGN1bGF0ZWQgdXNpbmdcclxuICpcclxuICogICB4XnkgPSBleHAoeSpsbih4KSlcclxuICpcclxuICogVGhlIG1heGltdW0gZXJyb3IgaXMgMSB1bHAgKHVuaXQgaW4gbGFzdCBwbGFjZSkuXHJcbiAqXHJcbiAqIHkge251bWJlcnxzdHJpbmd8RGVjaW1hbH0gVGhlIHBvd2VyIHRvIHdoaWNoIHRvIHJhaXNlIHRoaXMgRGVjaW1hbC5cclxuICpcclxuICovXHJcblAudG9Qb3dlciA9IFAucG93ID0gZnVuY3Rpb24gKHkpIHtcclxuICB2YXIgZSwgaywgcHIsIHIsIHNpZ24sIHlJc0ludCxcclxuICAgIHggPSB0aGlzLFxyXG4gICAgQ3RvciA9IHguY29uc3RydWN0b3IsXHJcbiAgICBndWFyZCA9IDEyLFxyXG4gICAgeW4gPSArKHkgPSBuZXcgQ3Rvcih5KSk7XHJcblxyXG4gIC8vIHBvdyh4LCAwKSA9IDFcclxuICBpZiAoIXkucykgcmV0dXJuIG5ldyBDdG9yKE9ORSk7XHJcblxyXG4gIHggPSBuZXcgQ3Rvcih4KTtcclxuXHJcbiAgLy8gcG93KDAsIHkgPiAwKSA9IDBcclxuICAvLyBwb3coMCwgeSA8IDApID0gSW5maW5pdHlcclxuICBpZiAoIXgucykge1xyXG4gICAgaWYgKHkucyA8IDEpIHRocm93IEVycm9yKGRlY2ltYWxFcnJvciArICdJbmZpbml0eScpO1xyXG4gICAgcmV0dXJuIHg7XHJcbiAgfVxyXG5cclxuICAvLyBwb3coMSwgeSkgPSAxXHJcbiAgaWYgKHguZXEoT05FKSkgcmV0dXJuIHg7XHJcblxyXG4gIHByID0gQ3Rvci5wcmVjaXNpb247XHJcblxyXG4gIC8vIHBvdyh4LCAxKSA9IHhcclxuICBpZiAoeS5lcShPTkUpKSByZXR1cm4gcm91bmQoeCwgcHIpO1xyXG5cclxuICBlID0geS5lO1xyXG4gIGsgPSB5LmQubGVuZ3RoIC0gMTtcclxuICB5SXNJbnQgPSBlID49IGs7XHJcbiAgc2lnbiA9IHgucztcclxuXHJcbiAgaWYgKCF5SXNJbnQpIHtcclxuXHJcbiAgICAvLyBwb3coeCA8IDAsIHkgbm9uLWludGVnZXIpID0gTmFOXHJcbiAgICBpZiAoc2lnbiA8IDApIHRocm93IEVycm9yKGRlY2ltYWxFcnJvciArICdOYU4nKTtcclxuXHJcbiAgLy8gSWYgeSBpcyBhIHNtYWxsIGludGVnZXIgdXNlIHRoZSAnZXhwb25lbnRpYXRpb24gYnkgc3F1YXJpbmcnIGFsZ29yaXRobS5cclxuICB9IGVsc2UgaWYgKChrID0geW4gPCAwID8gLXluIDogeW4pIDw9IE1BWF9TQUZFX0lOVEVHRVIpIHtcclxuICAgIHIgPSBuZXcgQ3RvcihPTkUpO1xyXG5cclxuICAgIC8vIE1heCBrIG9mIDkwMDcxOTkyNTQ3NDA5OTEgdGFrZXMgNTMgbG9vcCBpdGVyYXRpb25zLlxyXG4gICAgLy8gTWF4aW11bSBkaWdpdHMgYXJyYXkgbGVuZ3RoOyBsZWF2ZXMgWzI4LCAzNF0gZ3VhcmQgZGlnaXRzLlxyXG4gICAgZSA9IE1hdGguY2VpbChwciAvIExPR19CQVNFICsgNCk7XHJcblxyXG4gICAgZXh0ZXJuYWwgPSBmYWxzZTtcclxuXHJcbiAgICBmb3IgKDs7KSB7XHJcbiAgICAgIGlmIChrICUgMikge1xyXG4gICAgICAgIHIgPSByLnRpbWVzKHgpO1xyXG4gICAgICAgIHRydW5jYXRlKHIuZCwgZSk7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIGsgPSBtYXRoZmxvb3IoayAvIDIpO1xyXG4gICAgICBpZiAoayA9PT0gMCkgYnJlYWs7XHJcblxyXG4gICAgICB4ID0geC50aW1lcyh4KTtcclxuICAgICAgdHJ1bmNhdGUoeC5kLCBlKTtcclxuICAgIH1cclxuXHJcbiAgICBleHRlcm5hbCA9IHRydWU7XHJcblxyXG4gICAgcmV0dXJuIHkucyA8IDAgPyBuZXcgQ3RvcihPTkUpLmRpdihyKSA6IHJvdW5kKHIsIHByKTtcclxuICB9XHJcblxyXG4gIC8vIFJlc3VsdCBpcyBuZWdhdGl2ZSBpZiB4IGlzIG5lZ2F0aXZlIGFuZCB0aGUgbGFzdCBkaWdpdCBvZiBpbnRlZ2VyIHkgaXMgb2RkLlxyXG4gIHNpZ24gPSBzaWduIDwgMCAmJiB5LmRbTWF0aC5tYXgoZSwgayldICYgMSA/IC0xIDogMTtcclxuXHJcbiAgeC5zID0gMTtcclxuICBleHRlcm5hbCA9IGZhbHNlO1xyXG4gIHIgPSB5LnRpbWVzKGxuKHgsIHByICsgZ3VhcmQpKTtcclxuICBleHRlcm5hbCA9IHRydWU7XHJcbiAgciA9IGV4cChyKTtcclxuICByLnMgPSBzaWduO1xyXG5cclxuICByZXR1cm4gcjtcclxufTtcclxuXHJcblxyXG4vKlxyXG4gKiBSZXR1cm4gYSBzdHJpbmcgcmVwcmVzZW50aW5nIHRoZSB2YWx1ZSBvZiB0aGlzIERlY2ltYWwgcm91bmRlZCB0byBgc2RgIHNpZ25pZmljYW50IGRpZ2l0c1xyXG4gKiB1c2luZyByb3VuZGluZyBtb2RlIGByb3VuZGluZ2AuXHJcbiAqXHJcbiAqIFJldHVybiBleHBvbmVudGlhbCBub3RhdGlvbiBpZiBgc2RgIGlzIGxlc3MgdGhhbiB0aGUgbnVtYmVyIG9mIGRpZ2l0cyBuZWNlc3NhcnkgdG8gcmVwcmVzZW50XHJcbiAqIHRoZSBpbnRlZ2VyIHBhcnQgb2YgdGhlIHZhbHVlIGluIG5vcm1hbCBub3RhdGlvbi5cclxuICpcclxuICogW3NkXSB7bnVtYmVyfSBTaWduaWZpY2FudCBkaWdpdHMuIEludGVnZXIsIDEgdG8gTUFYX0RJR0lUUyBpbmNsdXNpdmUuXHJcbiAqIFtybV0ge251bWJlcn0gUm91bmRpbmcgbW9kZS4gSW50ZWdlciwgMCB0byA4IGluY2x1c2l2ZS5cclxuICpcclxuICovXHJcblAudG9QcmVjaXNpb24gPSBmdW5jdGlvbiAoc2QsIHJtKSB7XHJcbiAgdmFyIGUsIHN0cixcclxuICAgIHggPSB0aGlzLFxyXG4gICAgQ3RvciA9IHguY29uc3RydWN0b3I7XHJcblxyXG4gIGlmIChzZCA9PT0gdm9pZCAwKSB7XHJcbiAgICBlID0gZ2V0QmFzZTEwRXhwb25lbnQoeCk7XHJcbiAgICBzdHIgPSB0b1N0cmluZyh4LCBlIDw9IEN0b3IudG9FeHBOZWcgfHwgZSA+PSBDdG9yLnRvRXhwUG9zKTtcclxuICB9IGVsc2Uge1xyXG4gICAgY2hlY2tJbnQzMihzZCwgMSwgTUFYX0RJR0lUUyk7XHJcblxyXG4gICAgaWYgKHJtID09PSB2b2lkIDApIHJtID0gQ3Rvci5yb3VuZGluZztcclxuICAgIGVsc2UgY2hlY2tJbnQzMihybSwgMCwgOCk7XHJcblxyXG4gICAgeCA9IHJvdW5kKG5ldyBDdG9yKHgpLCBzZCwgcm0pO1xyXG4gICAgZSA9IGdldEJhc2UxMEV4cG9uZW50KHgpO1xyXG4gICAgc3RyID0gdG9TdHJpbmcoeCwgc2QgPD0gZSB8fCBlIDw9IEN0b3IudG9FeHBOZWcsIHNkKTtcclxuICB9XHJcblxyXG4gIHJldHVybiBzdHI7XHJcbn07XHJcblxyXG5cclxuLypcclxuICogUmV0dXJuIGEgbmV3IERlY2ltYWwgd2hvc2UgdmFsdWUgaXMgdGhlIHZhbHVlIG9mIHRoaXMgRGVjaW1hbCByb3VuZGVkIHRvIGEgbWF4aW11bSBvZiBgc2RgXHJcbiAqIHNpZ25pZmljYW50IGRpZ2l0cyB1c2luZyByb3VuZGluZyBtb2RlIGBybWAsIG9yIHRvIGBwcmVjaXNpb25gIGFuZCBgcm91bmRpbmdgIHJlc3BlY3RpdmVseSBpZlxyXG4gKiBvbWl0dGVkLlxyXG4gKlxyXG4gKiBbc2RdIHtudW1iZXJ9IFNpZ25pZmljYW50IGRpZ2l0cy4gSW50ZWdlciwgMSB0byBNQVhfRElHSVRTIGluY2x1c2l2ZS5cclxuICogW3JtXSB7bnVtYmVyfSBSb3VuZGluZyBtb2RlLiBJbnRlZ2VyLCAwIHRvIDggaW5jbHVzaXZlLlxyXG4gKlxyXG4gKi9cclxuUC50b1NpZ25pZmljYW50RGlnaXRzID0gUC50b3NkID0gZnVuY3Rpb24gKHNkLCBybSkge1xyXG4gIHZhciB4ID0gdGhpcyxcclxuICAgIEN0b3IgPSB4LmNvbnN0cnVjdG9yO1xyXG5cclxuICBpZiAoc2QgPT09IHZvaWQgMCkge1xyXG4gICAgc2QgPSBDdG9yLnByZWNpc2lvbjtcclxuICAgIHJtID0gQ3Rvci5yb3VuZGluZztcclxuICB9IGVsc2Uge1xyXG4gICAgY2hlY2tJbnQzMihzZCwgMSwgTUFYX0RJR0lUUyk7XHJcblxyXG4gICAgaWYgKHJtID09PSB2b2lkIDApIHJtID0gQ3Rvci5yb3VuZGluZztcclxuICAgIGVsc2UgY2hlY2tJbnQzMihybSwgMCwgOCk7XHJcbiAgfVxyXG5cclxuICByZXR1cm4gcm91bmQobmV3IEN0b3IoeCksIHNkLCBybSk7XHJcbn07XHJcblxyXG5cclxuLypcclxuICogUmV0dXJuIGEgc3RyaW5nIHJlcHJlc2VudGluZyB0aGUgdmFsdWUgb2YgdGhpcyBEZWNpbWFsLlxyXG4gKlxyXG4gKiBSZXR1cm4gZXhwb25lbnRpYWwgbm90YXRpb24gaWYgdGhpcyBEZWNpbWFsIGhhcyBhIHBvc2l0aXZlIGV4cG9uZW50IGVxdWFsIHRvIG9yIGdyZWF0ZXIgdGhhblxyXG4gKiBgdG9FeHBQb3NgLCBvciBhIG5lZ2F0aXZlIGV4cG9uZW50IGVxdWFsIHRvIG9yIGxlc3MgdGhhbiBgdG9FeHBOZWdgLlxyXG4gKlxyXG4gKi9cclxuUC50b1N0cmluZyA9IFAudmFsdWVPZiA9IFAudmFsID0gUC50b0pTT04gPSBQW1N5bWJvbC5mb3IoJ25vZGVqcy51dGlsLmluc3BlY3QuY3VzdG9tJyldID0gZnVuY3Rpb24gKCkge1xyXG4gIHZhciB4ID0gdGhpcyxcclxuICAgIGUgPSBnZXRCYXNlMTBFeHBvbmVudCh4KSxcclxuICAgIEN0b3IgPSB4LmNvbnN0cnVjdG9yO1xyXG5cclxuICByZXR1cm4gdG9TdHJpbmcoeCwgZSA8PSBDdG9yLnRvRXhwTmVnIHx8IGUgPj0gQ3Rvci50b0V4cFBvcyk7XHJcbn07XHJcblxyXG5cclxuLy8gSGVscGVyIGZ1bmN0aW9ucyBmb3IgRGVjaW1hbC5wcm90b3R5cGUgKFApIGFuZC9vciBEZWNpbWFsIG1ldGhvZHMsIGFuZCB0aGVpciBjYWxsZXJzLlxyXG5cclxuXHJcbi8qXHJcbiAqICBhZGQgICAgICAgICAgICAgICAgIFAubWludXMsIFAucGx1c1xyXG4gKiAgY2hlY2tJbnQzMiAgICAgICAgICBQLnRvZHAsIFAudG9FeHBvbmVudGlhbCwgUC50b0ZpeGVkLCBQLnRvUHJlY2lzaW9uLCBQLnRvc2RcclxuICogIGRpZ2l0c1RvU3RyaW5nICAgICAgUC5sb2csIFAuc3FydCwgUC5wb3csIHRvU3RyaW5nLCBleHAsIGxuXHJcbiAqICBkaXZpZGUgICAgICAgICAgICAgIFAuZGl2LCBQLmlkaXYsIFAubG9nLCBQLm1vZCwgUC5zcXJ0LCBleHAsIGxuXHJcbiAqICBleHAgICAgICAgICAgICAgICAgIFAuZXhwLCBQLnBvd1xyXG4gKiAgZ2V0QmFzZTEwRXhwb25lbnQgICBQLmV4cG9uZW50LCBQLnNkLCBQLnRvaW50LCBQLnNxcnQsIFAudG9kcCwgUC50b0ZpeGVkLCBQLnRvUHJlY2lzaW9uLFxyXG4gKiAgICAgICAgICAgICAgICAgICAgICBQLnRvU3RyaW5nLCBkaXZpZGUsIHJvdW5kLCB0b1N0cmluZywgZXhwLCBsblxyXG4gKiAgZ2V0TG4xMCAgICAgICAgICAgICBQLmxvZywgbG5cclxuICogIGdldFplcm9TdHJpbmcgICAgICAgZGlnaXRzVG9TdHJpbmcsIHRvU3RyaW5nXHJcbiAqICBsbiAgICAgICAgICAgICAgICAgIFAubG9nLCBQLmxuLCBQLnBvdywgZXhwXHJcbiAqICBwYXJzZURlY2ltYWwgICAgICAgIERlY2ltYWxcclxuICogIHJvdW5kICAgICAgICAgICAgICAgUC5hYnMsIFAuaWRpdiwgUC5sb2csIFAubWludXMsIFAubW9kLCBQLm5lZywgUC5wbHVzLCBQLnRvaW50LCBQLnNxcnQsXHJcbiAqICAgICAgICAgICAgICAgICAgICAgIFAudGltZXMsIFAudG9kcCwgUC50b0V4cG9uZW50aWFsLCBQLnRvRml4ZWQsIFAucG93LCBQLnRvUHJlY2lzaW9uLCBQLnRvc2QsXHJcbiAqICAgICAgICAgICAgICAgICAgICAgIGRpdmlkZSwgZ2V0TG4xMCwgZXhwLCBsblxyXG4gKiAgc3VidHJhY3QgICAgICAgICAgICBQLm1pbnVzLCBQLnBsdXNcclxuICogIHRvU3RyaW5nICAgICAgICAgICAgUC50b0V4cG9uZW50aWFsLCBQLnRvRml4ZWQsIFAudG9QcmVjaXNpb24sIFAudG9TdHJpbmcsIFAudmFsdWVPZlxyXG4gKiAgdHJ1bmNhdGUgICAgICAgICAgICBQLnBvd1xyXG4gKlxyXG4gKiAgVGhyb3dzOiAgICAgICAgICAgICBQLmxvZywgUC5tb2QsIFAuc2QsIFAuc3FydCwgUC5wb3csICBjaGVja0ludDMyLCBkaXZpZGUsIHJvdW5kLFxyXG4gKiAgICAgICAgICAgICAgICAgICAgICBnZXRMbjEwLCBleHAsIGxuLCBwYXJzZURlY2ltYWwsIERlY2ltYWwsIGNvbmZpZ1xyXG4gKi9cclxuXHJcblxyXG5mdW5jdGlvbiBhZGQoeCwgeSkge1xyXG4gIHZhciBjYXJyeSwgZCwgZSwgaSwgaywgbGVuLCB4ZCwgeWQsXHJcbiAgICBDdG9yID0geC5jb25zdHJ1Y3RvcixcclxuICAgIHByID0gQ3Rvci5wcmVjaXNpb247XHJcblxyXG4gIC8vIElmIGVpdGhlciBpcyB6ZXJvLi4uXHJcbiAgaWYgKCF4LnMgfHwgIXkucykge1xyXG5cclxuICAgIC8vIFJldHVybiB4IGlmIHkgaXMgemVyby5cclxuICAgIC8vIFJldHVybiB5IGlmIHkgaXMgbm9uLXplcm8uXHJcbiAgICBpZiAoIXkucykgeSA9IG5ldyBDdG9yKHgpO1xyXG4gICAgcmV0dXJuIGV4dGVybmFsID8gcm91bmQoeSwgcHIpIDogeTtcclxuICB9XHJcblxyXG4gIHhkID0geC5kO1xyXG4gIHlkID0geS5kO1xyXG5cclxuICAvLyB4IGFuZCB5IGFyZSBmaW5pdGUsIG5vbi16ZXJvIG51bWJlcnMgd2l0aCB0aGUgc2FtZSBzaWduLlxyXG5cclxuICBrID0geC5lO1xyXG4gIGUgPSB5LmU7XHJcbiAgeGQgPSB4ZC5zbGljZSgpO1xyXG4gIGkgPSBrIC0gZTtcclxuXHJcbiAgLy8gSWYgYmFzZSAxZTcgZXhwb25lbnRzIGRpZmZlci4uLlxyXG4gIGlmIChpKSB7XHJcbiAgICBpZiAoaSA8IDApIHtcclxuICAgICAgZCA9IHhkO1xyXG4gICAgICBpID0gLWk7XHJcbiAgICAgIGxlbiA9IHlkLmxlbmd0aDtcclxuICAgIH0gZWxzZSB7XHJcbiAgICAgIGQgPSB5ZDtcclxuICAgICAgZSA9IGs7XHJcbiAgICAgIGxlbiA9IHhkLmxlbmd0aDtcclxuICAgIH1cclxuXHJcbiAgICAvLyBMaW1pdCBudW1iZXIgb2YgemVyb3MgcHJlcGVuZGVkIHRvIG1heChjZWlsKHByIC8gTE9HX0JBU0UpLCBsZW4pICsgMS5cclxuICAgIGsgPSBNYXRoLmNlaWwocHIgLyBMT0dfQkFTRSk7XHJcbiAgICBsZW4gPSBrID4gbGVuID8gayArIDEgOiBsZW4gKyAxO1xyXG5cclxuICAgIGlmIChpID4gbGVuKSB7XHJcbiAgICAgIGkgPSBsZW47XHJcbiAgICAgIGQubGVuZ3RoID0gMTtcclxuICAgIH1cclxuXHJcbiAgICAvLyBQcmVwZW5kIHplcm9zIHRvIGVxdWFsaXNlIGV4cG9uZW50cy4gTm90ZTogRmFzdGVyIHRvIHVzZSByZXZlcnNlIHRoZW4gZG8gdW5zaGlmdHMuXHJcbiAgICBkLnJldmVyc2UoKTtcclxuICAgIGZvciAoOyBpLS07KSBkLnB1c2goMCk7XHJcbiAgICBkLnJldmVyc2UoKTtcclxuICB9XHJcblxyXG4gIGxlbiA9IHhkLmxlbmd0aDtcclxuICBpID0geWQubGVuZ3RoO1xyXG5cclxuICAvLyBJZiB5ZCBpcyBsb25nZXIgdGhhbiB4ZCwgc3dhcCB4ZCBhbmQgeWQgc28geGQgcG9pbnRzIHRvIHRoZSBsb25nZXIgYXJyYXkuXHJcbiAgaWYgKGxlbiAtIGkgPCAwKSB7XHJcbiAgICBpID0gbGVuO1xyXG4gICAgZCA9IHlkO1xyXG4gICAgeWQgPSB4ZDtcclxuICAgIHhkID0gZDtcclxuICB9XHJcblxyXG4gIC8vIE9ubHkgc3RhcnQgYWRkaW5nIGF0IHlkLmxlbmd0aCAtIDEgYXMgdGhlIGZ1cnRoZXIgZGlnaXRzIG9mIHhkIGNhbiBiZSBsZWZ0IGFzIHRoZXkgYXJlLlxyXG4gIGZvciAoY2FycnkgPSAwOyBpOykge1xyXG4gICAgY2FycnkgPSAoeGRbLS1pXSA9IHhkW2ldICsgeWRbaV0gKyBjYXJyeSkgLyBCQVNFIHwgMDtcclxuICAgIHhkW2ldICU9IEJBU0U7XHJcbiAgfVxyXG5cclxuICBpZiAoY2FycnkpIHtcclxuICAgIHhkLnVuc2hpZnQoY2FycnkpO1xyXG4gICAgKytlO1xyXG4gIH1cclxuXHJcbiAgLy8gUmVtb3ZlIHRyYWlsaW5nIHplcm9zLlxyXG4gIC8vIE5vIG5lZWQgdG8gY2hlY2sgZm9yIHplcm8sIGFzICt4ICsgK3kgIT0gMCAmJiAteCArIC15ICE9IDBcclxuICBmb3IgKGxlbiA9IHhkLmxlbmd0aDsgeGRbLS1sZW5dID09IDA7KSB4ZC5wb3AoKTtcclxuXHJcbiAgeS5kID0geGQ7XHJcbiAgeS5lID0gZTtcclxuXHJcbiAgcmV0dXJuIGV4dGVybmFsID8gcm91bmQoeSwgcHIpIDogeTtcclxufVxyXG5cclxuXHJcbmZ1bmN0aW9uIGNoZWNrSW50MzIoaSwgbWluLCBtYXgpIHtcclxuICBpZiAoaSAhPT0gfn5pIHx8IGkgPCBtaW4gfHwgaSA+IG1heCkge1xyXG4gICAgdGhyb3cgRXJyb3IoaW52YWxpZEFyZ3VtZW50ICsgaSk7XHJcbiAgfVxyXG59XHJcblxyXG5cclxuZnVuY3Rpb24gZGlnaXRzVG9TdHJpbmcoZCkge1xyXG4gIHZhciBpLCBrLCB3cyxcclxuICAgIGluZGV4T2ZMYXN0V29yZCA9IGQubGVuZ3RoIC0gMSxcclxuICAgIHN0ciA9ICcnLFxyXG4gICAgdyA9IGRbMF07XHJcblxyXG4gIGlmIChpbmRleE9mTGFzdFdvcmQgPiAwKSB7XHJcbiAgICBzdHIgKz0gdztcclxuICAgIGZvciAoaSA9IDE7IGkgPCBpbmRleE9mTGFzdFdvcmQ7IGkrKykge1xyXG4gICAgICB3cyA9IGRbaV0gKyAnJztcclxuICAgICAgayA9IExPR19CQVNFIC0gd3MubGVuZ3RoO1xyXG4gICAgICBpZiAoaykgc3RyICs9IGdldFplcm9TdHJpbmcoayk7XHJcbiAgICAgIHN0ciArPSB3cztcclxuICAgIH1cclxuXHJcbiAgICB3ID0gZFtpXTtcclxuICAgIHdzID0gdyArICcnO1xyXG4gICAgayA9IExPR19CQVNFIC0gd3MubGVuZ3RoO1xyXG4gICAgaWYgKGspIHN0ciArPSBnZXRaZXJvU3RyaW5nKGspO1xyXG4gIH0gZWxzZSBpZiAodyA9PT0gMCkge1xyXG4gICAgcmV0dXJuICcwJztcclxuICB9XHJcblxyXG4gIC8vIFJlbW92ZSB0cmFpbGluZyB6ZXJvcyBvZiBsYXN0IHcuXHJcbiAgZm9yICg7IHcgJSAxMCA9PT0gMDspIHcgLz0gMTA7XHJcblxyXG4gIHJldHVybiBzdHIgKyB3O1xyXG59XHJcblxyXG5cclxudmFyIGRpdmlkZSA9IChmdW5jdGlvbiAoKSB7XHJcblxyXG4gIC8vIEFzc3VtZXMgbm9uLXplcm8geCBhbmQgaywgYW5kIGhlbmNlIG5vbi16ZXJvIHJlc3VsdC5cclxuICBmdW5jdGlvbiBtdWx0aXBseUludGVnZXIoeCwgaykge1xyXG4gICAgdmFyIHRlbXAsXHJcbiAgICAgIGNhcnJ5ID0gMCxcclxuICAgICAgaSA9IHgubGVuZ3RoO1xyXG5cclxuICAgIGZvciAoeCA9IHguc2xpY2UoKTsgaS0tOykge1xyXG4gICAgICB0ZW1wID0geFtpXSAqIGsgKyBjYXJyeTtcclxuICAgICAgeFtpXSA9IHRlbXAgJSBCQVNFIHwgMDtcclxuICAgICAgY2FycnkgPSB0ZW1wIC8gQkFTRSB8IDA7XHJcbiAgICB9XHJcblxyXG4gICAgaWYgKGNhcnJ5KSB4LnVuc2hpZnQoY2FycnkpO1xyXG5cclxuICAgIHJldHVybiB4O1xyXG4gIH1cclxuXHJcbiAgZnVuY3Rpb24gY29tcGFyZShhLCBiLCBhTCwgYkwpIHtcclxuICAgIHZhciBpLCByO1xyXG5cclxuICAgIGlmIChhTCAhPSBiTCkge1xyXG4gICAgICByID0gYUwgPiBiTCA/IDEgOiAtMTtcclxuICAgIH0gZWxzZSB7XHJcbiAgICAgIGZvciAoaSA9IHIgPSAwOyBpIDwgYUw7IGkrKykge1xyXG4gICAgICAgIGlmIChhW2ldICE9IGJbaV0pIHtcclxuICAgICAgICAgIHIgPSBhW2ldID4gYltpXSA/IDEgOiAtMTtcclxuICAgICAgICAgIGJyZWFrO1xyXG4gICAgICAgIH1cclxuICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIHJldHVybiByO1xyXG4gIH1cclxuXHJcbiAgZnVuY3Rpb24gc3VidHJhY3QoYSwgYiwgYUwpIHtcclxuICAgIHZhciBpID0gMDtcclxuXHJcbiAgICAvLyBTdWJ0cmFjdCBiIGZyb20gYS5cclxuICAgIGZvciAoOyBhTC0tOykge1xyXG4gICAgICBhW2FMXSAtPSBpO1xyXG4gICAgICBpID0gYVthTF0gPCBiW2FMXSA/IDEgOiAwO1xyXG4gICAgICBhW2FMXSA9IGkgKiBCQVNFICsgYVthTF0gLSBiW2FMXTtcclxuICAgIH1cclxuXHJcbiAgICAvLyBSZW1vdmUgbGVhZGluZyB6ZXJvcy5cclxuICAgIGZvciAoOyAhYVswXSAmJiBhLmxlbmd0aCA+IDE7KSBhLnNoaWZ0KCk7XHJcbiAgfVxyXG5cclxuICByZXR1cm4gZnVuY3Rpb24gKHgsIHksIHByLCBkcCkge1xyXG4gICAgdmFyIGNtcCwgZSwgaSwgaywgcHJvZCwgcHJvZEwsIHEsIHFkLCByZW0sIHJlbUwsIHJlbTAsIHNkLCB0LCB4aSwgeEwsIHlkMCwgeUwsIHl6LFxyXG4gICAgICBDdG9yID0geC5jb25zdHJ1Y3RvcixcclxuICAgICAgc2lnbiA9IHgucyA9PSB5LnMgPyAxIDogLTEsXHJcbiAgICAgIHhkID0geC5kLFxyXG4gICAgICB5ZCA9IHkuZDtcclxuXHJcbiAgICAvLyBFaXRoZXIgMD9cclxuICAgIGlmICgheC5zKSByZXR1cm4gbmV3IEN0b3IoeCk7XHJcbiAgICBpZiAoIXkucykgdGhyb3cgRXJyb3IoZGVjaW1hbEVycm9yICsgJ0RpdmlzaW9uIGJ5IHplcm8nKTtcclxuXHJcbiAgICBlID0geC5lIC0geS5lO1xyXG4gICAgeUwgPSB5ZC5sZW5ndGg7XHJcbiAgICB4TCA9IHhkLmxlbmd0aDtcclxuICAgIHEgPSBuZXcgQ3RvcihzaWduKTtcclxuICAgIHFkID0gcS5kID0gW107XHJcblxyXG4gICAgLy8gUmVzdWx0IGV4cG9uZW50IG1heSBiZSBvbmUgbGVzcyB0aGFuIGUuXHJcbiAgICBmb3IgKGkgPSAwOyB5ZFtpXSA9PSAoeGRbaV0gfHwgMCk7ICkgKytpO1xyXG4gICAgaWYgKHlkW2ldID4gKHhkW2ldIHx8IDApKSAtLWU7XHJcblxyXG4gICAgaWYgKHByID09IG51bGwpIHtcclxuICAgICAgc2QgPSBwciA9IEN0b3IucHJlY2lzaW9uO1xyXG4gICAgfSBlbHNlIGlmIChkcCkge1xyXG4gICAgICBzZCA9IHByICsgKGdldEJhc2UxMEV4cG9uZW50KHgpIC0gZ2V0QmFzZTEwRXhwb25lbnQoeSkpICsgMTtcclxuICAgIH0gZWxzZSB7XHJcbiAgICAgIHNkID0gcHI7XHJcbiAgICB9XHJcblxyXG4gICAgaWYgKHNkIDwgMCkgcmV0dXJuIG5ldyBDdG9yKDApO1xyXG5cclxuICAgIC8vIENvbnZlcnQgcHJlY2lzaW9uIGluIG51bWJlciBvZiBiYXNlIDEwIGRpZ2l0cyB0byBiYXNlIDFlNyBkaWdpdHMuXHJcbiAgICBzZCA9IHNkIC8gTE9HX0JBU0UgKyAyIHwgMDtcclxuICAgIGkgPSAwO1xyXG5cclxuICAgIC8vIGRpdmlzb3IgPCAxZTdcclxuICAgIGlmICh5TCA9PSAxKSB7XHJcbiAgICAgIGsgPSAwO1xyXG4gICAgICB5ZCA9IHlkWzBdO1xyXG4gICAgICBzZCsrO1xyXG5cclxuICAgICAgLy8gayBpcyB0aGUgY2FycnkuXHJcbiAgICAgIGZvciAoOyAoaSA8IHhMIHx8IGspICYmIHNkLS07IGkrKykge1xyXG4gICAgICAgIHQgPSBrICogQkFTRSArICh4ZFtpXSB8fCAwKTtcclxuICAgICAgICBxZFtpXSA9IHQgLyB5ZCB8IDA7XHJcbiAgICAgICAgayA9IHQgJSB5ZCB8IDA7XHJcbiAgICAgIH1cclxuXHJcbiAgICAvLyBkaXZpc29yID49IDFlN1xyXG4gICAgfSBlbHNlIHtcclxuXHJcbiAgICAgIC8vIE5vcm1hbGlzZSB4ZCBhbmQgeWQgc28gaGlnaGVzdCBvcmRlciBkaWdpdCBvZiB5ZCBpcyA+PSBCQVNFLzJcclxuICAgICAgayA9IEJBU0UgLyAoeWRbMF0gKyAxKSB8IDA7XHJcblxyXG4gICAgICBpZiAoayA+IDEpIHtcclxuICAgICAgICB5ZCA9IG11bHRpcGx5SW50ZWdlcih5ZCwgayk7XHJcbiAgICAgICAgeGQgPSBtdWx0aXBseUludGVnZXIoeGQsIGspO1xyXG4gICAgICAgIHlMID0geWQubGVuZ3RoO1xyXG4gICAgICAgIHhMID0geGQubGVuZ3RoO1xyXG4gICAgICB9XHJcblxyXG4gICAgICB4aSA9IHlMO1xyXG4gICAgICByZW0gPSB4ZC5zbGljZSgwLCB5TCk7XHJcbiAgICAgIHJlbUwgPSByZW0ubGVuZ3RoO1xyXG5cclxuICAgICAgLy8gQWRkIHplcm9zIHRvIG1ha2UgcmVtYWluZGVyIGFzIGxvbmcgYXMgZGl2aXNvci5cclxuICAgICAgZm9yICg7IHJlbUwgPCB5TDspIHJlbVtyZW1MKytdID0gMDtcclxuXHJcbiAgICAgIHl6ID0geWQuc2xpY2UoKTtcclxuICAgICAgeXoudW5zaGlmdCgwKTtcclxuICAgICAgeWQwID0geWRbMF07XHJcblxyXG4gICAgICBpZiAoeWRbMV0gPj0gQkFTRSAvIDIpICsreWQwO1xyXG5cclxuICAgICAgZG8ge1xyXG4gICAgICAgIGsgPSAwO1xyXG5cclxuICAgICAgICAvLyBDb21wYXJlIGRpdmlzb3IgYW5kIHJlbWFpbmRlci5cclxuICAgICAgICBjbXAgPSBjb21wYXJlKHlkLCByZW0sIHlMLCByZW1MKTtcclxuXHJcbiAgICAgICAgLy8gSWYgZGl2aXNvciA8IHJlbWFpbmRlci5cclxuICAgICAgICBpZiAoY21wIDwgMCkge1xyXG5cclxuICAgICAgICAgIC8vIENhbGN1bGF0ZSB0cmlhbCBkaWdpdCwgay5cclxuICAgICAgICAgIHJlbTAgPSByZW1bMF07XHJcbiAgICAgICAgICBpZiAoeUwgIT0gcmVtTCkgcmVtMCA9IHJlbTAgKiBCQVNFICsgKHJlbVsxXSB8fCAwKTtcclxuXHJcbiAgICAgICAgICAvLyBrIHdpbGwgYmUgaG93IG1hbnkgdGltZXMgdGhlIGRpdmlzb3IgZ29lcyBpbnRvIHRoZSBjdXJyZW50IHJlbWFpbmRlci5cclxuICAgICAgICAgIGsgPSByZW0wIC8geWQwIHwgMDtcclxuXHJcbiAgICAgICAgICAvLyAgQWxnb3JpdGhtOlxyXG4gICAgICAgICAgLy8gIDEuIHByb2R1Y3QgPSBkaXZpc29yICogdHJpYWwgZGlnaXQgKGspXHJcbiAgICAgICAgICAvLyAgMi4gaWYgcHJvZHVjdCA+IHJlbWFpbmRlcjogcHJvZHVjdCAtPSBkaXZpc29yLCBrLS1cclxuICAgICAgICAgIC8vICAzLiByZW1haW5kZXIgLT0gcHJvZHVjdFxyXG4gICAgICAgICAgLy8gIDQuIGlmIHByb2R1Y3Qgd2FzIDwgcmVtYWluZGVyIGF0IDI6XHJcbiAgICAgICAgICAvLyAgICA1LiBjb21wYXJlIG5ldyByZW1haW5kZXIgYW5kIGRpdmlzb3JcclxuICAgICAgICAgIC8vICAgIDYuIElmIHJlbWFpbmRlciA+IGRpdmlzb3I6IHJlbWFpbmRlciAtPSBkaXZpc29yLCBrKytcclxuXHJcbiAgICAgICAgICBpZiAoayA+IDEpIHtcclxuICAgICAgICAgICAgaWYgKGsgPj0gQkFTRSkgayA9IEJBU0UgLSAxO1xyXG5cclxuICAgICAgICAgICAgLy8gcHJvZHVjdCA9IGRpdmlzb3IgKiB0cmlhbCBkaWdpdC5cclxuICAgICAgICAgICAgcHJvZCA9IG11bHRpcGx5SW50ZWdlcih5ZCwgayk7XHJcbiAgICAgICAgICAgIHByb2RMID0gcHJvZC5sZW5ndGg7XHJcbiAgICAgICAgICAgIHJlbUwgPSByZW0ubGVuZ3RoO1xyXG5cclxuICAgICAgICAgICAgLy8gQ29tcGFyZSBwcm9kdWN0IGFuZCByZW1haW5kZXIuXHJcbiAgICAgICAgICAgIGNtcCA9IGNvbXBhcmUocHJvZCwgcmVtLCBwcm9kTCwgcmVtTCk7XHJcblxyXG4gICAgICAgICAgICAvLyBwcm9kdWN0ID4gcmVtYWluZGVyLlxyXG4gICAgICAgICAgICBpZiAoY21wID09IDEpIHtcclxuICAgICAgICAgICAgICBrLS07XHJcblxyXG4gICAgICAgICAgICAgIC8vIFN1YnRyYWN0IGRpdmlzb3IgZnJvbSBwcm9kdWN0LlxyXG4gICAgICAgICAgICAgIHN1YnRyYWN0KHByb2QsIHlMIDwgcHJvZEwgPyB5eiA6IHlkLCBwcm9kTCk7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgIH0gZWxzZSB7XHJcblxyXG4gICAgICAgICAgICAvLyBjbXAgaXMgLTEuXHJcbiAgICAgICAgICAgIC8vIElmIGsgaXMgMCwgdGhlcmUgaXMgbm8gbmVlZCB0byBjb21wYXJlIHlkIGFuZCByZW0gYWdhaW4gYmVsb3csIHNvIGNoYW5nZSBjbXAgdG8gMVxyXG4gICAgICAgICAgICAvLyB0byBhdm9pZCBpdC4gSWYgayBpcyAxIHRoZXJlIGlzIGEgbmVlZCB0byBjb21wYXJlIHlkIGFuZCByZW0gYWdhaW4gYmVsb3cuXHJcbiAgICAgICAgICAgIGlmIChrID09IDApIGNtcCA9IGsgPSAxO1xyXG4gICAgICAgICAgICBwcm9kID0geWQuc2xpY2UoKTtcclxuICAgICAgICAgIH1cclxuXHJcbiAgICAgICAgICBwcm9kTCA9IHByb2QubGVuZ3RoO1xyXG4gICAgICAgICAgaWYgKHByb2RMIDwgcmVtTCkgcHJvZC51bnNoaWZ0KDApO1xyXG5cclxuICAgICAgICAgIC8vIFN1YnRyYWN0IHByb2R1Y3QgZnJvbSByZW1haW5kZXIuXHJcbiAgICAgICAgICBzdWJ0cmFjdChyZW0sIHByb2QsIHJlbUwpO1xyXG5cclxuICAgICAgICAgIC8vIElmIHByb2R1Y3Qgd2FzIDwgcHJldmlvdXMgcmVtYWluZGVyLlxyXG4gICAgICAgICAgaWYgKGNtcCA9PSAtMSkge1xyXG4gICAgICAgICAgICByZW1MID0gcmVtLmxlbmd0aDtcclxuXHJcbiAgICAgICAgICAgIC8vIENvbXBhcmUgZGl2aXNvciBhbmQgbmV3IHJlbWFpbmRlci5cclxuICAgICAgICAgICAgY21wID0gY29tcGFyZSh5ZCwgcmVtLCB5TCwgcmVtTCk7XHJcblxyXG4gICAgICAgICAgICAvLyBJZiBkaXZpc29yIDwgbmV3IHJlbWFpbmRlciwgc3VidHJhY3QgZGl2aXNvciBmcm9tIHJlbWFpbmRlci5cclxuICAgICAgICAgICAgaWYgKGNtcCA8IDEpIHtcclxuICAgICAgICAgICAgICBrKys7XHJcblxyXG4gICAgICAgICAgICAgIC8vIFN1YnRyYWN0IGRpdmlzb3IgZnJvbSByZW1haW5kZXIuXHJcbiAgICAgICAgICAgICAgc3VidHJhY3QocmVtLCB5TCA8IHJlbUwgPyB5eiA6IHlkLCByZW1MKTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgfVxyXG5cclxuICAgICAgICAgIHJlbUwgPSByZW0ubGVuZ3RoO1xyXG4gICAgICAgIH0gZWxzZSBpZiAoY21wID09PSAwKSB7XHJcbiAgICAgICAgICBrKys7XHJcbiAgICAgICAgICByZW0gPSBbMF07XHJcbiAgICAgICAgfSAgICAvLyBpZiBjbXAgPT09IDEsIGsgd2lsbCBiZSAwXHJcblxyXG4gICAgICAgIC8vIEFkZCB0aGUgbmV4dCBkaWdpdCwgaywgdG8gdGhlIHJlc3VsdCBhcnJheS5cclxuICAgICAgICBxZFtpKytdID0gaztcclxuXHJcbiAgICAgICAgLy8gVXBkYXRlIHRoZSByZW1haW5kZXIuXHJcbiAgICAgICAgaWYgKGNtcCAmJiByZW1bMF0pIHtcclxuICAgICAgICAgIHJlbVtyZW1MKytdID0geGRbeGldIHx8IDA7XHJcbiAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgIHJlbSA9IFt4ZFt4aV1dO1xyXG4gICAgICAgICAgcmVtTCA9IDE7XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgfSB3aGlsZSAoKHhpKysgPCB4TCB8fCByZW1bMF0gIT09IHZvaWQgMCkgJiYgc2QtLSk7XHJcbiAgICB9XHJcblxyXG4gICAgLy8gTGVhZGluZyB6ZXJvP1xyXG4gICAgaWYgKCFxZFswXSkgcWQuc2hpZnQoKTtcclxuXHJcbiAgICBxLmUgPSBlO1xyXG5cclxuICAgIHJldHVybiByb3VuZChxLCBkcCA/IHByICsgZ2V0QmFzZTEwRXhwb25lbnQocSkgKyAxIDogcHIpO1xyXG4gIH07XHJcbn0pKCk7XHJcblxyXG5cclxuLypcclxuICogUmV0dXJuIGEgbmV3IERlY2ltYWwgd2hvc2UgdmFsdWUgaXMgdGhlIG5hdHVyYWwgZXhwb25lbnRpYWwgb2YgYHhgIHRydW5jYXRlZCB0byBgc2RgXHJcbiAqIHNpZ25pZmljYW50IGRpZ2l0cy5cclxuICpcclxuICogVGF5bG9yL01hY2xhdXJpbiBzZXJpZXMuXHJcbiAqXHJcbiAqIGV4cCh4KSA9IHheMC8wISArIHheMS8xISArIHheMi8yISArIHheMy8zISArIC4uLlxyXG4gKlxyXG4gKiBBcmd1bWVudCByZWR1Y3Rpb246XHJcbiAqICAgUmVwZWF0IHggPSB4IC8gMzIsIGsgKz0gNSwgdW50aWwgfHh8IDwgMC4xXHJcbiAqICAgZXhwKHgpID0gZXhwKHggLyAyXmspXigyXmspXHJcbiAqXHJcbiAqIFByZXZpb3VzbHksIHRoZSBhcmd1bWVudCB3YXMgaW5pdGlhbGx5IHJlZHVjZWQgYnlcclxuICogZXhwKHgpID0gZXhwKHIpICogMTBeayAgd2hlcmUgciA9IHggLSBrICogbG4xMCwgayA9IGZsb29yKHggLyBsbjEwKVxyXG4gKiB0byBmaXJzdCBwdXQgciBpbiB0aGUgcmFuZ2UgWzAsIGxuMTBdLCBiZWZvcmUgZGl2aWRpbmcgYnkgMzIgdW50aWwgfHh8IDwgMC4xLCBidXQgdGhpcyB3YXNcclxuICogZm91bmQgdG8gYmUgc2xvd2VyIHRoYW4ganVzdCBkaXZpZGluZyByZXBlYXRlZGx5IGJ5IDMyIGFzIGFib3ZlLlxyXG4gKlxyXG4gKiAoTWF0aCBvYmplY3QgaW50ZWdlciBtaW4vbWF4OiBNYXRoLmV4cCg3MDkpID0gOC4yZSszMDcsIE1hdGguZXhwKC03NDUpID0gNWUtMzI0KVxyXG4gKlxyXG4gKiAgZXhwKHgpIGlzIG5vbi10ZXJtaW5hdGluZyBmb3IgYW55IGZpbml0ZSwgbm9uLXplcm8geC5cclxuICpcclxuICovXHJcbmZ1bmN0aW9uIGV4cCh4LCBzZCkge1xyXG4gIHZhciBkZW5vbWluYXRvciwgZ3VhcmQsIHBvdywgc3VtLCB0LCB3cHIsXHJcbiAgICBpID0gMCxcclxuICAgIGsgPSAwLFxyXG4gICAgQ3RvciA9IHguY29uc3RydWN0b3IsXHJcbiAgICBwciA9IEN0b3IucHJlY2lzaW9uO1xyXG5cclxuICBpZiAoZ2V0QmFzZTEwRXhwb25lbnQoeCkgPiAxNikgdGhyb3cgRXJyb3IoZXhwb25lbnRPdXRPZlJhbmdlICsgZ2V0QmFzZTEwRXhwb25lbnQoeCkpO1xyXG5cclxuICAvLyBleHAoMCkgPSAxXHJcbiAgaWYgKCF4LnMpIHJldHVybiBuZXcgQ3RvcihPTkUpO1xyXG5cclxuICBpZiAoc2QgPT0gbnVsbCkge1xyXG4gICAgZXh0ZXJuYWwgPSBmYWxzZTtcclxuICAgIHdwciA9IHByO1xyXG4gIH0gZWxzZSB7XHJcbiAgICB3cHIgPSBzZDtcclxuICB9XHJcblxyXG4gIHQgPSBuZXcgQ3RvcigwLjAzMTI1KTtcclxuXHJcbiAgd2hpbGUgKHguYWJzKCkuZ3RlKDAuMSkpIHtcclxuICAgIHggPSB4LnRpbWVzKHQpOyAgICAvLyB4ID0geCAvIDJeNVxyXG4gICAgayArPSA1O1xyXG4gIH1cclxuXHJcbiAgLy8gRXN0aW1hdGUgdGhlIHByZWNpc2lvbiBpbmNyZWFzZSBuZWNlc3NhcnkgdG8gZW5zdXJlIHRoZSBmaXJzdCA0IHJvdW5kaW5nIGRpZ2l0cyBhcmUgY29ycmVjdC5cclxuICBndWFyZCA9IE1hdGgubG9nKG1hdGhwb3coMiwgaykpIC8gTWF0aC5MTjEwICogMiArIDUgfCAwO1xyXG4gIHdwciArPSBndWFyZDtcclxuICBkZW5vbWluYXRvciA9IHBvdyA9IHN1bSA9IG5ldyBDdG9yKE9ORSk7XHJcbiAgQ3Rvci5wcmVjaXNpb24gPSB3cHI7XHJcblxyXG4gIGZvciAoOzspIHtcclxuICAgIHBvdyA9IHJvdW5kKHBvdy50aW1lcyh4KSwgd3ByKTtcclxuICAgIGRlbm9taW5hdG9yID0gZGVub21pbmF0b3IudGltZXMoKytpKTtcclxuICAgIHQgPSBzdW0ucGx1cyhkaXZpZGUocG93LCBkZW5vbWluYXRvciwgd3ByKSk7XHJcblxyXG4gICAgaWYgKGRpZ2l0c1RvU3RyaW5nKHQuZCkuc2xpY2UoMCwgd3ByKSA9PT0gZGlnaXRzVG9TdHJpbmcoc3VtLmQpLnNsaWNlKDAsIHdwcikpIHtcclxuICAgICAgd2hpbGUgKGstLSkgc3VtID0gcm91bmQoc3VtLnRpbWVzKHN1bSksIHdwcik7XHJcbiAgICAgIEN0b3IucHJlY2lzaW9uID0gcHI7XHJcbiAgICAgIHJldHVybiBzZCA9PSBudWxsID8gKGV4dGVybmFsID0gdHJ1ZSwgcm91bmQoc3VtLCBwcikpIDogc3VtO1xyXG4gICAgfVxyXG5cclxuICAgIHN1bSA9IHQ7XHJcbiAgfVxyXG59XHJcblxyXG5cclxuLy8gQ2FsY3VsYXRlIHRoZSBiYXNlIDEwIGV4cG9uZW50IGZyb20gdGhlIGJhc2UgMWU3IGV4cG9uZW50LlxyXG5mdW5jdGlvbiBnZXRCYXNlMTBFeHBvbmVudCh4KSB7XHJcbiAgdmFyIGUgPSB4LmUgKiBMT0dfQkFTRSxcclxuICAgIHcgPSB4LmRbMF07XHJcblxyXG4gIC8vIEFkZCB0aGUgbnVtYmVyIG9mIGRpZ2l0cyBvZiB0aGUgZmlyc3Qgd29yZCBvZiB0aGUgZGlnaXRzIGFycmF5LlxyXG4gIGZvciAoOyB3ID49IDEwOyB3IC89IDEwKSBlKys7XHJcbiAgcmV0dXJuIGU7XHJcbn1cclxuXHJcblxyXG5mdW5jdGlvbiBnZXRMbjEwKEN0b3IsIHNkLCBwcikge1xyXG5cclxuICBpZiAoc2QgPiBDdG9yLkxOMTAuc2QoKSkge1xyXG5cclxuXHJcbiAgICAvLyBSZXNldCBnbG9iYWwgc3RhdGUgaW4gY2FzZSB0aGUgZXhjZXB0aW9uIGlzIGNhdWdodC5cclxuICAgIGV4dGVybmFsID0gdHJ1ZTtcclxuICAgIGlmIChwcikgQ3Rvci5wcmVjaXNpb24gPSBwcjtcclxuICAgIHRocm93IEVycm9yKGRlY2ltYWxFcnJvciArICdMTjEwIHByZWNpc2lvbiBsaW1pdCBleGNlZWRlZCcpO1xyXG4gIH1cclxuXHJcbiAgcmV0dXJuIHJvdW5kKG5ldyBDdG9yKEN0b3IuTE4xMCksIHNkKTtcclxufVxyXG5cclxuXHJcbmZ1bmN0aW9uIGdldFplcm9TdHJpbmcoaykge1xyXG4gIHZhciB6cyA9ICcnO1xyXG4gIGZvciAoOyBrLS07KSB6cyArPSAnMCc7XHJcbiAgcmV0dXJuIHpzO1xyXG59XHJcblxyXG5cclxuLypcclxuICogUmV0dXJuIGEgbmV3IERlY2ltYWwgd2hvc2UgdmFsdWUgaXMgdGhlIG5hdHVyYWwgbG9nYXJpdGhtIG9mIGB4YCB0cnVuY2F0ZWQgdG8gYHNkYCBzaWduaWZpY2FudFxyXG4gKiBkaWdpdHMuXHJcbiAqXHJcbiAqICBsbihuKSBpcyBub24tdGVybWluYXRpbmcgKG4gIT0gMSlcclxuICpcclxuICovXHJcbmZ1bmN0aW9uIGxuKHksIHNkKSB7XHJcbiAgdmFyIGMsIGMwLCBkZW5vbWluYXRvciwgZSwgbnVtZXJhdG9yLCBzdW0sIHQsIHdwciwgeDIsXHJcbiAgICBuID0gMSxcclxuICAgIGd1YXJkID0gMTAsXHJcbiAgICB4ID0geSxcclxuICAgIHhkID0geC5kLFxyXG4gICAgQ3RvciA9IHguY29uc3RydWN0b3IsXHJcbiAgICBwciA9IEN0b3IucHJlY2lzaW9uO1xyXG5cclxuICAvLyBsbigteCkgPSBOYU5cclxuICAvLyBsbigwKSA9IC1JbmZpbml0eVxyXG4gIGlmICh4LnMgPCAxKSB0aHJvdyBFcnJvcihkZWNpbWFsRXJyb3IgKyAoeC5zID8gJ05hTicgOiAnLUluZmluaXR5JykpO1xyXG5cclxuICAvLyBsbigxKSA9IDBcclxuICBpZiAoeC5lcShPTkUpKSByZXR1cm4gbmV3IEN0b3IoMCk7XHJcblxyXG4gIGlmIChzZCA9PSBudWxsKSB7XHJcbiAgICBleHRlcm5hbCA9IGZhbHNlO1xyXG4gICAgd3ByID0gcHI7XHJcbiAgfSBlbHNlIHtcclxuICAgIHdwciA9IHNkO1xyXG4gIH1cclxuXHJcbiAgaWYgKHguZXEoMTApKSB7XHJcbiAgICBpZiAoc2QgPT0gbnVsbCkgZXh0ZXJuYWwgPSB0cnVlO1xyXG4gICAgcmV0dXJuIGdldExuMTAoQ3Rvciwgd3ByKTtcclxuICB9XHJcblxyXG4gIHdwciArPSBndWFyZDtcclxuICBDdG9yLnByZWNpc2lvbiA9IHdwcjtcclxuICBjID0gZGlnaXRzVG9TdHJpbmcoeGQpO1xyXG4gIGMwID0gYy5jaGFyQXQoMCk7XHJcbiAgZSA9IGdldEJhc2UxMEV4cG9uZW50KHgpO1xyXG5cclxuICBpZiAoTWF0aC5hYnMoZSkgPCAxLjVlMTUpIHtcclxuXHJcbiAgICAvLyBBcmd1bWVudCByZWR1Y3Rpb24uXHJcbiAgICAvLyBUaGUgc2VyaWVzIGNvbnZlcmdlcyBmYXN0ZXIgdGhlIGNsb3NlciB0aGUgYXJndW1lbnQgaXMgdG8gMSwgc28gdXNpbmdcclxuICAgIC8vIGxuKGFeYikgPSBiICogbG4oYSksICAgbG4oYSkgPSBsbihhXmIpIC8gYlxyXG4gICAgLy8gbXVsdGlwbHkgdGhlIGFyZ3VtZW50IGJ5IGl0c2VsZiB1bnRpbCB0aGUgbGVhZGluZyBkaWdpdHMgb2YgdGhlIHNpZ25pZmljYW5kIGFyZSA3LCA4LCA5LFxyXG4gICAgLy8gMTAsIDExLCAxMiBvciAxMywgcmVjb3JkaW5nIHRoZSBudW1iZXIgb2YgbXVsdGlwbGljYXRpb25zIHNvIHRoZSBzdW0gb2YgdGhlIHNlcmllcyBjYW5cclxuICAgIC8vIGxhdGVyIGJlIGRpdmlkZWQgYnkgdGhpcyBudW1iZXIsIHRoZW4gc2VwYXJhdGUgb3V0IHRoZSBwb3dlciBvZiAxMCB1c2luZ1xyXG4gICAgLy8gbG4oYSoxMF5iKSA9IGxuKGEpICsgYipsbigxMCkuXHJcblxyXG4gICAgLy8gbWF4IG4gaXMgMjEgKGdpdmVzIDAuOSwgMS4wIG9yIDEuMSkgKDllMTUgLyAyMSA9IDQuMmUxNCkuXHJcbiAgICAvL3doaWxlIChjMCA8IDkgJiYgYzAgIT0gMSB8fCBjMCA9PSAxICYmIGMuY2hhckF0KDEpID4gMSkge1xyXG4gICAgLy8gbWF4IG4gaXMgNiAoZ2l2ZXMgMC43IC0gMS4zKVxyXG4gICAgd2hpbGUgKGMwIDwgNyAmJiBjMCAhPSAxIHx8IGMwID09IDEgJiYgYy5jaGFyQXQoMSkgPiAzKSB7XHJcbiAgICAgIHggPSB4LnRpbWVzKHkpO1xyXG4gICAgICBjID0gZGlnaXRzVG9TdHJpbmcoeC5kKTtcclxuICAgICAgYzAgPSBjLmNoYXJBdCgwKTtcclxuICAgICAgbisrO1xyXG4gICAgfVxyXG5cclxuICAgIGUgPSBnZXRCYXNlMTBFeHBvbmVudCh4KTtcclxuXHJcbiAgICBpZiAoYzAgPiAxKSB7XHJcbiAgICAgIHggPSBuZXcgQ3RvcignMC4nICsgYyk7XHJcbiAgICAgIGUrKztcclxuICAgIH0gZWxzZSB7XHJcbiAgICAgIHggPSBuZXcgQ3RvcihjMCArICcuJyArIGMuc2xpY2UoMSkpO1xyXG4gICAgfVxyXG4gIH0gZWxzZSB7XHJcblxyXG4gICAgLy8gVGhlIGFyZ3VtZW50IHJlZHVjdGlvbiBtZXRob2QgYWJvdmUgbWF5IHJlc3VsdCBpbiBvdmVyZmxvdyBpZiB0aGUgYXJndW1lbnQgeSBpcyBhIG1hc3NpdmVcclxuICAgIC8vIG51bWJlciB3aXRoIGV4cG9uZW50ID49IDE1MDAwMDAwMDAwMDAwMDAgKDllMTUgLyA2ID0gMS41ZTE1KSwgc28gaW5zdGVhZCByZWNhbGwgdGhpc1xyXG4gICAgLy8gZnVuY3Rpb24gdXNpbmcgbG4oeCoxMF5lKSA9IGxuKHgpICsgZSpsbigxMCkuXHJcbiAgICB0ID0gZ2V0TG4xMChDdG9yLCB3cHIgKyAyLCBwcikudGltZXMoZSArICcnKTtcclxuICAgIHggPSBsbihuZXcgQ3RvcihjMCArICcuJyArIGMuc2xpY2UoMSkpLCB3cHIgLSBndWFyZCkucGx1cyh0KTtcclxuXHJcbiAgICBDdG9yLnByZWNpc2lvbiA9IHByO1xyXG4gICAgcmV0dXJuIHNkID09IG51bGwgPyAoZXh0ZXJuYWwgPSB0cnVlLCByb3VuZCh4LCBwcikpIDogeDtcclxuICB9XHJcblxyXG4gIC8vIHggaXMgcmVkdWNlZCB0byBhIHZhbHVlIG5lYXIgMS5cclxuXHJcbiAgLy8gVGF5bG9yIHNlcmllcy5cclxuICAvLyBsbih5KSA9IGxuKCgxICsgeCkvKDEgLSB4KSkgPSAyKHggKyB4XjMvMyArIHheNS81ICsgeF43LzcgKyAuLi4pXHJcbiAgLy8gd2hlcmUgeCA9ICh5IC0gMSkvKHkgKyAxKSAgICAofHh8IDwgMSlcclxuICBzdW0gPSBudW1lcmF0b3IgPSB4ID0gZGl2aWRlKHgubWludXMoT05FKSwgeC5wbHVzKE9ORSksIHdwcik7XHJcbiAgeDIgPSByb3VuZCh4LnRpbWVzKHgpLCB3cHIpO1xyXG4gIGRlbm9taW5hdG9yID0gMztcclxuXHJcbiAgZm9yICg7Oykge1xyXG4gICAgbnVtZXJhdG9yID0gcm91bmQobnVtZXJhdG9yLnRpbWVzKHgyKSwgd3ByKTtcclxuICAgIHQgPSBzdW0ucGx1cyhkaXZpZGUobnVtZXJhdG9yLCBuZXcgQ3RvcihkZW5vbWluYXRvciksIHdwcikpO1xyXG5cclxuICAgIGlmIChkaWdpdHNUb1N0cmluZyh0LmQpLnNsaWNlKDAsIHdwcikgPT09IGRpZ2l0c1RvU3RyaW5nKHN1bS5kKS5zbGljZSgwLCB3cHIpKSB7XHJcbiAgICAgIHN1bSA9IHN1bS50aW1lcygyKTtcclxuXHJcbiAgICAgIC8vIFJldmVyc2UgdGhlIGFyZ3VtZW50IHJlZHVjdGlvbi5cclxuICAgICAgaWYgKGUgIT09IDApIHN1bSA9IHN1bS5wbHVzKGdldExuMTAoQ3Rvciwgd3ByICsgMiwgcHIpLnRpbWVzKGUgKyAnJykpO1xyXG4gICAgICBzdW0gPSBkaXZpZGUoc3VtLCBuZXcgQ3RvcihuKSwgd3ByKTtcclxuXHJcbiAgICAgIEN0b3IucHJlY2lzaW9uID0gcHI7XHJcbiAgICAgIHJldHVybiBzZCA9PSBudWxsID8gKGV4dGVybmFsID0gdHJ1ZSwgcm91bmQoc3VtLCBwcikpIDogc3VtO1xyXG4gICAgfVxyXG5cclxuICAgIHN1bSA9IHQ7XHJcbiAgICBkZW5vbWluYXRvciArPSAyO1xyXG4gIH1cclxufVxyXG5cclxuXHJcbi8qXHJcbiAqIFBhcnNlIHRoZSB2YWx1ZSBvZiBhIG5ldyBEZWNpbWFsIGB4YCBmcm9tIHN0cmluZyBgc3RyYC5cclxuICovXHJcbmZ1bmN0aW9uIHBhcnNlRGVjaW1hbCh4LCBzdHIpIHtcclxuICB2YXIgZSwgaSwgbGVuO1xyXG5cclxuICAvLyBEZWNpbWFsIHBvaW50P1xyXG4gIGlmICgoZSA9IHN0ci5pbmRleE9mKCcuJykpID4gLTEpIHN0ciA9IHN0ci5yZXBsYWNlKCcuJywgJycpO1xyXG5cclxuICAvLyBFeHBvbmVudGlhbCBmb3JtP1xyXG4gIGlmICgoaSA9IHN0ci5zZWFyY2goL2UvaSkpID4gMCkge1xyXG5cclxuICAgIC8vIERldGVybWluZSBleHBvbmVudC5cclxuICAgIGlmIChlIDwgMCkgZSA9IGk7XHJcbiAgICBlICs9ICtzdHIuc2xpY2UoaSArIDEpO1xyXG4gICAgc3RyID0gc3RyLnN1YnN0cmluZygwLCBpKTtcclxuICB9IGVsc2UgaWYgKGUgPCAwKSB7XHJcblxyXG4gICAgLy8gSW50ZWdlci5cclxuICAgIGUgPSBzdHIubGVuZ3RoO1xyXG4gIH1cclxuXHJcbiAgLy8gRGV0ZXJtaW5lIGxlYWRpbmcgemVyb3MuXHJcbiAgZm9yIChpID0gMDsgc3RyLmNoYXJDb2RlQXQoaSkgPT09IDQ4OykgKytpO1xyXG5cclxuICAvLyBEZXRlcm1pbmUgdHJhaWxpbmcgemVyb3MuXHJcbiAgZm9yIChsZW4gPSBzdHIubGVuZ3RoOyBzdHIuY2hhckNvZGVBdChsZW4gLSAxKSA9PT0gNDg7KSAtLWxlbjtcclxuICBzdHIgPSBzdHIuc2xpY2UoaSwgbGVuKTtcclxuXHJcbiAgaWYgKHN0cikge1xyXG4gICAgbGVuIC09IGk7XHJcbiAgICBlID0gZSAtIGkgLSAxO1xyXG4gICAgeC5lID0gbWF0aGZsb29yKGUgLyBMT0dfQkFTRSk7XHJcbiAgICB4LmQgPSBbXTtcclxuXHJcbiAgICAvLyBUcmFuc2Zvcm0gYmFzZVxyXG5cclxuICAgIC8vIGUgaXMgdGhlIGJhc2UgMTAgZXhwb25lbnQuXHJcbiAgICAvLyBpIGlzIHdoZXJlIHRvIHNsaWNlIHN0ciB0byBnZXQgdGhlIGZpcnN0IHdvcmQgb2YgdGhlIGRpZ2l0cyBhcnJheS5cclxuICAgIGkgPSAoZSArIDEpICUgTE9HX0JBU0U7XHJcbiAgICBpZiAoZSA8IDApIGkgKz0gTE9HX0JBU0U7XHJcblxyXG4gICAgaWYgKGkgPCBsZW4pIHtcclxuICAgICAgaWYgKGkpIHguZC5wdXNoKCtzdHIuc2xpY2UoMCwgaSkpO1xyXG4gICAgICBmb3IgKGxlbiAtPSBMT0dfQkFTRTsgaSA8IGxlbjspIHguZC5wdXNoKCtzdHIuc2xpY2UoaSwgaSArPSBMT0dfQkFTRSkpO1xyXG4gICAgICBzdHIgPSBzdHIuc2xpY2UoaSk7XHJcbiAgICAgIGkgPSBMT0dfQkFTRSAtIHN0ci5sZW5ndGg7XHJcbiAgICB9IGVsc2Uge1xyXG4gICAgICBpIC09IGxlbjtcclxuICAgIH1cclxuXHJcbiAgICBmb3IgKDsgaS0tOykgc3RyICs9ICcwJztcclxuICAgIHguZC5wdXNoKCtzdHIpO1xyXG5cclxuICAgIGlmIChleHRlcm5hbCAmJiAoeC5lID4gTUFYX0UgfHwgeC5lIDwgLU1BWF9FKSkgdGhyb3cgRXJyb3IoZXhwb25lbnRPdXRPZlJhbmdlICsgZSk7XHJcbiAgfSBlbHNlIHtcclxuXHJcbiAgICAvLyBaZXJvLlxyXG4gICAgeC5zID0gMDtcclxuICAgIHguZSA9IDA7XHJcbiAgICB4LmQgPSBbMF07XHJcbiAgfVxyXG5cclxuICByZXR1cm4geDtcclxufVxyXG5cclxuXHJcbi8qXHJcbiAqIFJvdW5kIGB4YCB0byBgc2RgIHNpZ25pZmljYW50IGRpZ2l0cywgdXNpbmcgcm91bmRpbmcgbW9kZSBgcm1gIGlmIHByZXNlbnQgKHRydW5jYXRlIG90aGVyd2lzZSkuXHJcbiAqL1xyXG4gZnVuY3Rpb24gcm91bmQoeCwgc2QsIHJtKSB7XHJcbiAgdmFyIGksIGosIGssIG4sIHJkLCBkb1JvdW5kLCB3LCB4ZGksXHJcbiAgICB4ZCA9IHguZDtcclxuXHJcbiAgLy8gcmQ6IHRoZSByb3VuZGluZyBkaWdpdCwgaS5lLiB0aGUgZGlnaXQgYWZ0ZXIgdGhlIGRpZ2l0IHRoYXQgbWF5IGJlIHJvdW5kZWQgdXAuXHJcbiAgLy8gdzogdGhlIHdvcmQgb2YgeGQgd2hpY2ggY29udGFpbnMgdGhlIHJvdW5kaW5nIGRpZ2l0LCBhIGJhc2UgMWU3IG51bWJlci5cclxuICAvLyB4ZGk6IHRoZSBpbmRleCBvZiB3IHdpdGhpbiB4ZC5cclxuICAvLyBuOiB0aGUgbnVtYmVyIG9mIGRpZ2l0cyBvZiB3LlxyXG4gIC8vIGk6IHdoYXQgd291bGQgYmUgdGhlIGluZGV4IG9mIHJkIHdpdGhpbiB3IGlmIGFsbCB0aGUgbnVtYmVycyB3ZXJlIDcgZGlnaXRzIGxvbmcgKGkuZS4gaWZcclxuICAvLyB0aGV5IGhhZCBsZWFkaW5nIHplcm9zKVxyXG4gIC8vIGo6IGlmID4gMCwgdGhlIGFjdHVhbCBpbmRleCBvZiByZCB3aXRoaW4gdyAoaWYgPCAwLCByZCBpcyBhIGxlYWRpbmcgemVybykuXHJcblxyXG4gIC8vIEdldCB0aGUgbGVuZ3RoIG9mIHRoZSBmaXJzdCB3b3JkIG9mIHRoZSBkaWdpdHMgYXJyYXkgeGQuXHJcbiAgZm9yIChuID0gMSwgayA9IHhkWzBdOyBrID49IDEwOyBrIC89IDEwKSBuKys7XHJcbiAgaSA9IHNkIC0gbjtcclxuXHJcbiAgLy8gSXMgdGhlIHJvdW5kaW5nIGRpZ2l0IGluIHRoZSBmaXJzdCB3b3JkIG9mIHhkP1xyXG4gIGlmIChpIDwgMCkge1xyXG4gICAgaSArPSBMT0dfQkFTRTtcclxuICAgIGogPSBzZDtcclxuICAgIHcgPSB4ZFt4ZGkgPSAwXTtcclxuICB9IGVsc2Uge1xyXG4gICAgeGRpID0gTWF0aC5jZWlsKChpICsgMSkgLyBMT0dfQkFTRSk7XHJcbiAgICBrID0geGQubGVuZ3RoO1xyXG4gICAgaWYgKHhkaSA+PSBrKSByZXR1cm4geDtcclxuICAgIHcgPSBrID0geGRbeGRpXTtcclxuXHJcbiAgICAvLyBHZXQgdGhlIG51bWJlciBvZiBkaWdpdHMgb2Ygdy5cclxuICAgIGZvciAobiA9IDE7IGsgPj0gMTA7IGsgLz0gMTApIG4rKztcclxuXHJcbiAgICAvLyBHZXQgdGhlIGluZGV4IG9mIHJkIHdpdGhpbiB3LlxyXG4gICAgaSAlPSBMT0dfQkFTRTtcclxuXHJcbiAgICAvLyBHZXQgdGhlIGluZGV4IG9mIHJkIHdpdGhpbiB3LCBhZGp1c3RlZCBmb3IgbGVhZGluZyB6ZXJvcy5cclxuICAgIC8vIFRoZSBudW1iZXIgb2YgbGVhZGluZyB6ZXJvcyBvZiB3IGlzIGdpdmVuIGJ5IExPR19CQVNFIC0gbi5cclxuICAgIGogPSBpIC0gTE9HX0JBU0UgKyBuO1xyXG4gIH1cclxuXHJcbiAgaWYgKHJtICE9PSB2b2lkIDApIHtcclxuICAgIGsgPSBtYXRocG93KDEwLCBuIC0gaiAtIDEpO1xyXG5cclxuICAgIC8vIEdldCB0aGUgcm91bmRpbmcgZGlnaXQgYXQgaW5kZXggaiBvZiB3LlxyXG4gICAgcmQgPSB3IC8gayAlIDEwIHwgMDtcclxuXHJcbiAgICAvLyBBcmUgdGhlcmUgYW55IG5vbi16ZXJvIGRpZ2l0cyBhZnRlciB0aGUgcm91bmRpbmcgZGlnaXQ/XHJcbiAgICBkb1JvdW5kID0gc2QgPCAwIHx8IHhkW3hkaSArIDFdICE9PSB2b2lkIDAgfHwgdyAlIGs7XHJcblxyXG4gICAgLy8gVGhlIGV4cHJlc3Npb24gYHcgJSBtYXRocG93KDEwLCBuIC0gaiAtIDEpYCByZXR1cm5zIGFsbCB0aGUgZGlnaXRzIG9mIHcgdG8gdGhlIHJpZ2h0IG9mIHRoZVxyXG4gICAgLy8gZGlnaXQgYXQgKGxlZnQtdG8tcmlnaHQpIGluZGV4IGosIGUuZy4gaWYgdyBpcyA5MDg3MTQgYW5kIGogaXMgMiwgdGhlIGV4cHJlc3Npb24gd2lsbCBnaXZlXHJcbiAgICAvLyA3MTQuXHJcblxyXG4gICAgZG9Sb3VuZCA9IHJtIDwgNFxyXG4gICAgICA/IChyZCB8fCBkb1JvdW5kKSAmJiAocm0gPT0gMCB8fCBybSA9PSAoeC5zIDwgMCA/IDMgOiAyKSlcclxuICAgICAgOiByZCA+IDUgfHwgcmQgPT0gNSAmJiAocm0gPT0gNCB8fCBkb1JvdW5kIHx8IHJtID09IDYgJiZcclxuXHJcbiAgICAgICAgLy8gQ2hlY2sgd2hldGhlciB0aGUgZGlnaXQgdG8gdGhlIGxlZnQgb2YgdGhlIHJvdW5kaW5nIGRpZ2l0IGlzIG9kZC5cclxuICAgICAgICAoKGkgPiAwID8gaiA+IDAgPyB3IC8gbWF0aHBvdygxMCwgbiAtIGopIDogMCA6IHhkW3hkaSAtIDFdKSAlIDEwKSAmIDEgfHxcclxuICAgICAgICAgIHJtID09ICh4LnMgPCAwID8gOCA6IDcpKTtcclxuICB9XHJcblxyXG4gIGlmIChzZCA8IDEgfHwgIXhkWzBdKSB7XHJcbiAgICBpZiAoZG9Sb3VuZCkge1xyXG4gICAgICBrID0gZ2V0QmFzZTEwRXhwb25lbnQoeCk7XHJcbiAgICAgIHhkLmxlbmd0aCA9IDE7XHJcblxyXG4gICAgICAvLyBDb252ZXJ0IHNkIHRvIGRlY2ltYWwgcGxhY2VzLlxyXG4gICAgICBzZCA9IHNkIC0gayAtIDE7XHJcblxyXG4gICAgICAvLyAxLCAwLjEsIDAuMDEsIDAuMDAxLCAwLjAwMDEgZXRjLlxyXG4gICAgICB4ZFswXSA9IG1hdGhwb3coMTAsIChMT0dfQkFTRSAtIHNkICUgTE9HX0JBU0UpICUgTE9HX0JBU0UpO1xyXG4gICAgICB4LmUgPSBtYXRoZmxvb3IoLXNkIC8gTE9HX0JBU0UpIHx8IDA7XHJcbiAgICB9IGVsc2Uge1xyXG4gICAgICB4ZC5sZW5ndGggPSAxO1xyXG5cclxuICAgICAgLy8gWmVyby5cclxuICAgICAgeGRbMF0gPSB4LmUgPSB4LnMgPSAwO1xyXG4gICAgfVxyXG5cclxuICAgIHJldHVybiB4O1xyXG4gIH1cclxuXHJcbiAgLy8gUmVtb3ZlIGV4Y2VzcyBkaWdpdHMuXHJcbiAgaWYgKGkgPT0gMCkge1xyXG4gICAgeGQubGVuZ3RoID0geGRpO1xyXG4gICAgayA9IDE7XHJcbiAgICB4ZGktLTtcclxuICB9IGVsc2Uge1xyXG4gICAgeGQubGVuZ3RoID0geGRpICsgMTtcclxuICAgIGsgPSBtYXRocG93KDEwLCBMT0dfQkFTRSAtIGkpO1xyXG5cclxuICAgIC8vIEUuZy4gNTY3MDAgYmVjb21lcyA1NjAwMCBpZiA3IGlzIHRoZSByb3VuZGluZyBkaWdpdC5cclxuICAgIC8vIGogPiAwIG1lYW5zIGkgPiBudW1iZXIgb2YgbGVhZGluZyB6ZXJvcyBvZiB3LlxyXG4gICAgeGRbeGRpXSA9IGogPiAwID8gKHcgLyBtYXRocG93KDEwLCBuIC0gaikgJSBtYXRocG93KDEwLCBqKSB8IDApICogayA6IDA7XHJcbiAgfVxyXG5cclxuICBpZiAoZG9Sb3VuZCkge1xyXG4gICAgZm9yICg7Oykge1xyXG5cclxuICAgICAgLy8gSXMgdGhlIGRpZ2l0IHRvIGJlIHJvdW5kZWQgdXAgaW4gdGhlIGZpcnN0IHdvcmQgb2YgeGQ/XHJcbiAgICAgIGlmICh4ZGkgPT0gMCkge1xyXG4gICAgICAgIGlmICgoeGRbMF0gKz0gaykgPT0gQkFTRSkge1xyXG4gICAgICAgICAgeGRbMF0gPSAxO1xyXG4gICAgICAgICAgKyt4LmU7XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICBicmVhaztcclxuICAgICAgfSBlbHNlIHtcclxuICAgICAgICB4ZFt4ZGldICs9IGs7XHJcbiAgICAgICAgaWYgKHhkW3hkaV0gIT0gQkFTRSkgYnJlYWs7XHJcbiAgICAgICAgeGRbeGRpLS1dID0gMDtcclxuICAgICAgICBrID0gMTtcclxuICAgICAgfVxyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgLy8gUmVtb3ZlIHRyYWlsaW5nIHplcm9zLlxyXG4gIGZvciAoaSA9IHhkLmxlbmd0aDsgeGRbLS1pXSA9PT0gMDspIHhkLnBvcCgpO1xyXG5cclxuICBpZiAoZXh0ZXJuYWwgJiYgKHguZSA+IE1BWF9FIHx8IHguZSA8IC1NQVhfRSkpIHtcclxuICAgIHRocm93IEVycm9yKGV4cG9uZW50T3V0T2ZSYW5nZSArIGdldEJhc2UxMEV4cG9uZW50KHgpKTtcclxuICB9XHJcblxyXG4gIHJldHVybiB4O1xyXG59XHJcblxyXG5cclxuZnVuY3Rpb24gc3VidHJhY3QoeCwgeSkge1xyXG4gIHZhciBkLCBlLCBpLCBqLCBrLCBsZW4sIHhkLCB4ZSwgeExUeSwgeWQsXHJcbiAgICBDdG9yID0geC5jb25zdHJ1Y3RvcixcclxuICAgIHByID0gQ3Rvci5wcmVjaXNpb247XHJcblxyXG4gIC8vIFJldHVybiB5IG5lZ2F0ZWQgaWYgeCBpcyB6ZXJvLlxyXG4gIC8vIFJldHVybiB4IGlmIHkgaXMgemVybyBhbmQgeCBpcyBub24temVyby5cclxuICBpZiAoIXgucyB8fCAheS5zKSB7XHJcbiAgICBpZiAoeS5zKSB5LnMgPSAteS5zO1xyXG4gICAgZWxzZSB5ID0gbmV3IEN0b3IoeCk7XHJcbiAgICByZXR1cm4gZXh0ZXJuYWwgPyByb3VuZCh5LCBwcikgOiB5O1xyXG4gIH1cclxuXHJcbiAgeGQgPSB4LmQ7XHJcbiAgeWQgPSB5LmQ7XHJcblxyXG4gIC8vIHggYW5kIHkgYXJlIG5vbi16ZXJvIG51bWJlcnMgd2l0aCB0aGUgc2FtZSBzaWduLlxyXG5cclxuICBlID0geS5lO1xyXG4gIHhlID0geC5lO1xyXG4gIHhkID0geGQuc2xpY2UoKTtcclxuICBrID0geGUgLSBlO1xyXG5cclxuICAvLyBJZiBleHBvbmVudHMgZGlmZmVyLi4uXHJcbiAgaWYgKGspIHtcclxuICAgIHhMVHkgPSBrIDwgMDtcclxuXHJcbiAgICBpZiAoeExUeSkge1xyXG4gICAgICBkID0geGQ7XHJcbiAgICAgIGsgPSAtaztcclxuICAgICAgbGVuID0geWQubGVuZ3RoO1xyXG4gICAgfSBlbHNlIHtcclxuICAgICAgZCA9IHlkO1xyXG4gICAgICBlID0geGU7XHJcbiAgICAgIGxlbiA9IHhkLmxlbmd0aDtcclxuICAgIH1cclxuXHJcbiAgICAvLyBOdW1iZXJzIHdpdGggbWFzc2l2ZWx5IGRpZmZlcmVudCBleHBvbmVudHMgd291bGQgcmVzdWx0IGluIGEgdmVyeSBoaWdoIG51bWJlciBvZiB6ZXJvc1xyXG4gICAgLy8gbmVlZGluZyB0byBiZSBwcmVwZW5kZWQsIGJ1dCB0aGlzIGNhbiBiZSBhdm9pZGVkIHdoaWxlIHN0aWxsIGVuc3VyaW5nIGNvcnJlY3Qgcm91bmRpbmcgYnlcclxuICAgIC8vIGxpbWl0aW5nIHRoZSBudW1iZXIgb2YgemVyb3MgdG8gYE1hdGguY2VpbChwciAvIExPR19CQVNFKSArIDJgLlxyXG4gICAgaSA9IE1hdGgubWF4KE1hdGguY2VpbChwciAvIExPR19CQVNFKSwgbGVuKSArIDI7XHJcblxyXG4gICAgaWYgKGsgPiBpKSB7XHJcbiAgICAgIGsgPSBpO1xyXG4gICAgICBkLmxlbmd0aCA9IDE7XHJcbiAgICB9XHJcblxyXG4gICAgLy8gUHJlcGVuZCB6ZXJvcyB0byBlcXVhbGlzZSBleHBvbmVudHMuXHJcbiAgICBkLnJldmVyc2UoKTtcclxuICAgIGZvciAoaSA9IGs7IGktLTspIGQucHVzaCgwKTtcclxuICAgIGQucmV2ZXJzZSgpO1xyXG5cclxuICAvLyBCYXNlIDFlNyBleHBvbmVudHMgZXF1YWwuXHJcbiAgfSBlbHNlIHtcclxuXHJcbiAgICAvLyBDaGVjayBkaWdpdHMgdG8gZGV0ZXJtaW5lIHdoaWNoIGlzIHRoZSBiaWdnZXIgbnVtYmVyLlxyXG5cclxuICAgIGkgPSB4ZC5sZW5ndGg7XHJcbiAgICBsZW4gPSB5ZC5sZW5ndGg7XHJcbiAgICB4TFR5ID0gaSA8IGxlbjtcclxuICAgIGlmICh4TFR5KSBsZW4gPSBpO1xyXG5cclxuICAgIGZvciAoaSA9IDA7IGkgPCBsZW47IGkrKykge1xyXG4gICAgICBpZiAoeGRbaV0gIT0geWRbaV0pIHtcclxuICAgICAgICB4TFR5ID0geGRbaV0gPCB5ZFtpXTtcclxuICAgICAgICBicmVhaztcclxuICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIGsgPSAwO1xyXG4gIH1cclxuXHJcbiAgaWYgKHhMVHkpIHtcclxuICAgIGQgPSB4ZDtcclxuICAgIHhkID0geWQ7XHJcbiAgICB5ZCA9IGQ7XHJcbiAgICB5LnMgPSAteS5zO1xyXG4gIH1cclxuXHJcbiAgbGVuID0geGQubGVuZ3RoO1xyXG5cclxuICAvLyBBcHBlbmQgemVyb3MgdG8geGQgaWYgc2hvcnRlci5cclxuICAvLyBEb24ndCBhZGQgemVyb3MgdG8geWQgaWYgc2hvcnRlciBhcyBzdWJ0cmFjdGlvbiBvbmx5IG5lZWRzIHRvIHN0YXJ0IGF0IHlkIGxlbmd0aC5cclxuICBmb3IgKGkgPSB5ZC5sZW5ndGggLSBsZW47IGkgPiAwOyAtLWkpIHhkW2xlbisrXSA9IDA7XHJcblxyXG4gIC8vIFN1YnRyYWN0IHlkIGZyb20geGQuXHJcbiAgZm9yIChpID0geWQubGVuZ3RoOyBpID4gazspIHtcclxuICAgIGlmICh4ZFstLWldIDwgeWRbaV0pIHtcclxuICAgICAgZm9yIChqID0gaTsgaiAmJiB4ZFstLWpdID09PSAwOykgeGRbal0gPSBCQVNFIC0gMTtcclxuICAgICAgLS14ZFtqXTtcclxuICAgICAgeGRbaV0gKz0gQkFTRTtcclxuICAgIH1cclxuXHJcbiAgICB4ZFtpXSAtPSB5ZFtpXTtcclxuICB9XHJcblxyXG4gIC8vIFJlbW92ZSB0cmFpbGluZyB6ZXJvcy5cclxuICBmb3IgKDsgeGRbLS1sZW5dID09PSAwOykgeGQucG9wKCk7XHJcblxyXG4gIC8vIFJlbW92ZSBsZWFkaW5nIHplcm9zIGFuZCBhZGp1c3QgZXhwb25lbnQgYWNjb3JkaW5nbHkuXHJcbiAgZm9yICg7IHhkWzBdID09PSAwOyB4ZC5zaGlmdCgpKSAtLWU7XHJcblxyXG4gIC8vIFplcm8/XHJcbiAgaWYgKCF4ZFswXSkgcmV0dXJuIG5ldyBDdG9yKDApO1xyXG5cclxuICB5LmQgPSB4ZDtcclxuICB5LmUgPSBlO1xyXG5cclxuICAvL3JldHVybiBleHRlcm5hbCAmJiB4ZC5sZW5ndGggPj0gcHIgLyBMT0dfQkFTRSA/IHJvdW5kKHksIHByKSA6IHk7XHJcbiAgcmV0dXJuIGV4dGVybmFsID8gcm91bmQoeSwgcHIpIDogeTtcclxufVxyXG5cclxuXHJcbmZ1bmN0aW9uIHRvU3RyaW5nKHgsIGlzRXhwLCBzZCkge1xyXG4gIHZhciBrLFxyXG4gICAgZSA9IGdldEJhc2UxMEV4cG9uZW50KHgpLFxyXG4gICAgc3RyID0gZGlnaXRzVG9TdHJpbmcoeC5kKSxcclxuICAgIGxlbiA9IHN0ci5sZW5ndGg7XHJcblxyXG4gIGlmIChpc0V4cCkge1xyXG4gICAgaWYgKHNkICYmIChrID0gc2QgLSBsZW4pID4gMCkge1xyXG4gICAgICBzdHIgPSBzdHIuY2hhckF0KDApICsgJy4nICsgc3RyLnNsaWNlKDEpICsgZ2V0WmVyb1N0cmluZyhrKTtcclxuICAgIH0gZWxzZSBpZiAobGVuID4gMSkge1xyXG4gICAgICBzdHIgPSBzdHIuY2hhckF0KDApICsgJy4nICsgc3RyLnNsaWNlKDEpO1xyXG4gICAgfVxyXG5cclxuICAgIHN0ciA9IHN0ciArIChlIDwgMCA/ICdlJyA6ICdlKycpICsgZTtcclxuICB9IGVsc2UgaWYgKGUgPCAwKSB7XHJcbiAgICBzdHIgPSAnMC4nICsgZ2V0WmVyb1N0cmluZygtZSAtIDEpICsgc3RyO1xyXG4gICAgaWYgKHNkICYmIChrID0gc2QgLSBsZW4pID4gMCkgc3RyICs9IGdldFplcm9TdHJpbmcoayk7XHJcbiAgfSBlbHNlIGlmIChlID49IGxlbikge1xyXG4gICAgc3RyICs9IGdldFplcm9TdHJpbmcoZSArIDEgLSBsZW4pO1xyXG4gICAgaWYgKHNkICYmIChrID0gc2QgLSBlIC0gMSkgPiAwKSBzdHIgPSBzdHIgKyAnLicgKyBnZXRaZXJvU3RyaW5nKGspO1xyXG4gIH0gZWxzZSB7XHJcbiAgICBpZiAoKGsgPSBlICsgMSkgPCBsZW4pIHN0ciA9IHN0ci5zbGljZSgwLCBrKSArICcuJyArIHN0ci5zbGljZShrKTtcclxuICAgIGlmIChzZCAmJiAoayA9IHNkIC0gbGVuKSA+IDApIHtcclxuICAgICAgaWYgKGUgKyAxID09PSBsZW4pIHN0ciArPSAnLic7XHJcbiAgICAgIHN0ciArPSBnZXRaZXJvU3RyaW5nKGspO1xyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgcmV0dXJuIHgucyA8IDAgPyAnLScgKyBzdHIgOiBzdHI7XHJcbn1cclxuXHJcblxyXG4vLyBEb2VzIG5vdCBzdHJpcCB0cmFpbGluZyB6ZXJvcy5cclxuZnVuY3Rpb24gdHJ1bmNhdGUoYXJyLCBsZW4pIHtcclxuICBpZiAoYXJyLmxlbmd0aCA+IGxlbikge1xyXG4gICAgYXJyLmxlbmd0aCA9IGxlbjtcclxuICAgIHJldHVybiB0cnVlO1xyXG4gIH1cclxufVxyXG5cclxuXHJcbi8vIERlY2ltYWwgbWV0aG9kc1xyXG5cclxuXHJcbi8qXHJcbiAqICBjbG9uZVxyXG4gKiAgY29uZmlnL3NldFxyXG4gKi9cclxuXHJcblxyXG4vKlxyXG4gKiBDcmVhdGUgYW5kIHJldHVybiBhIERlY2ltYWwgY29uc3RydWN0b3Igd2l0aCB0aGUgc2FtZSBjb25maWd1cmF0aW9uIHByb3BlcnRpZXMgYXMgdGhpcyBEZWNpbWFsXHJcbiAqIGNvbnN0cnVjdG9yLlxyXG4gKlxyXG4gKi9cclxuZnVuY3Rpb24gY2xvbmUob2JqKSB7XHJcbiAgdmFyIGksIHAsIHBzO1xyXG5cclxuICAvKlxyXG4gICAqIFRoZSBEZWNpbWFsIGNvbnN0cnVjdG9yIGFuZCBleHBvcnRlZCBmdW5jdGlvbi5cclxuICAgKiBSZXR1cm4gYSBuZXcgRGVjaW1hbCBpbnN0YW5jZS5cclxuICAgKlxyXG4gICAqIHZhbHVlIHtudW1iZXJ8c3RyaW5nfERlY2ltYWx9IEEgbnVtZXJpYyB2YWx1ZS5cclxuICAgKlxyXG4gICAqL1xyXG4gIGZ1bmN0aW9uIERlY2ltYWwodmFsdWUpIHtcclxuICAgIHZhciB4ID0gdGhpcztcclxuXHJcbiAgICAvLyBEZWNpbWFsIGNhbGxlZCB3aXRob3V0IG5ldy5cclxuICAgIGlmICghKHggaW5zdGFuY2VvZiBEZWNpbWFsKSkgcmV0dXJuIG5ldyBEZWNpbWFsKHZhbHVlKTtcclxuXHJcbiAgICAvLyBSZXRhaW4gYSByZWZlcmVuY2UgdG8gdGhpcyBEZWNpbWFsIGNvbnN0cnVjdG9yLCBhbmQgc2hhZG93IERlY2ltYWwucHJvdG90eXBlLmNvbnN0cnVjdG9yXHJcbiAgICAvLyB3aGljaCBwb2ludHMgdG8gT2JqZWN0LlxyXG4gICAgeC5jb25zdHJ1Y3RvciA9IERlY2ltYWw7XHJcblxyXG4gICAgLy8gRHVwbGljYXRlLlxyXG4gICAgaWYgKHZhbHVlIGluc3RhbmNlb2YgRGVjaW1hbCkge1xyXG4gICAgICB4LnMgPSB2YWx1ZS5zO1xyXG4gICAgICB4LmUgPSB2YWx1ZS5lO1xyXG4gICAgICB4LmQgPSAodmFsdWUgPSB2YWx1ZS5kKSA/IHZhbHVlLnNsaWNlKCkgOiB2YWx1ZTtcclxuICAgICAgcmV0dXJuO1xyXG4gICAgfVxyXG5cclxuICAgIGlmICh0eXBlb2YgdmFsdWUgPT09ICdudW1iZXInKSB7XHJcblxyXG4gICAgICAvLyBSZWplY3QgSW5maW5pdHkvTmFOLlxyXG4gICAgICBpZiAodmFsdWUgKiAwICE9PSAwKSB7XHJcbiAgICAgICAgdGhyb3cgRXJyb3IoaW52YWxpZEFyZ3VtZW50ICsgdmFsdWUpO1xyXG4gICAgICB9XHJcblxyXG4gICAgICBpZiAodmFsdWUgPiAwKSB7XHJcbiAgICAgICAgeC5zID0gMTtcclxuICAgICAgfSBlbHNlIGlmICh2YWx1ZSA8IDApIHtcclxuICAgICAgICB2YWx1ZSA9IC12YWx1ZTtcclxuICAgICAgICB4LnMgPSAtMTtcclxuICAgICAgfSBlbHNlIHtcclxuICAgICAgICB4LnMgPSAwO1xyXG4gICAgICAgIHguZSA9IDA7XHJcbiAgICAgICAgeC5kID0gWzBdO1xyXG4gICAgICAgIHJldHVybjtcclxuICAgICAgfVxyXG5cclxuICAgICAgLy8gRmFzdCBwYXRoIGZvciBzbWFsbCBpbnRlZ2Vycy5cclxuICAgICAgaWYgKHZhbHVlID09PSB+fnZhbHVlICYmIHZhbHVlIDwgMWU3KSB7XHJcbiAgICAgICAgeC5lID0gMDtcclxuICAgICAgICB4LmQgPSBbdmFsdWVdO1xyXG4gICAgICAgIHJldHVybjtcclxuICAgICAgfVxyXG5cclxuICAgICAgcmV0dXJuIHBhcnNlRGVjaW1hbCh4LCB2YWx1ZS50b1N0cmluZygpKTtcclxuICAgIH0gZWxzZSBpZiAodHlwZW9mIHZhbHVlICE9PSAnc3RyaW5nJykge1xyXG4gICAgICB0aHJvdyBFcnJvcihpbnZhbGlkQXJndW1lbnQgKyB2YWx1ZSk7XHJcbiAgICB9XHJcblxyXG4gICAgLy8gTWludXMgc2lnbj9cclxuICAgIGlmICh2YWx1ZS5jaGFyQ29kZUF0KDApID09PSA0NSkge1xyXG4gICAgICB2YWx1ZSA9IHZhbHVlLnNsaWNlKDEpO1xyXG4gICAgICB4LnMgPSAtMTtcclxuICAgIH0gZWxzZSB7XHJcbiAgICAgIHgucyA9IDE7XHJcbiAgICB9XHJcblxyXG4gICAgaWYgKGlzRGVjaW1hbC50ZXN0KHZhbHVlKSkgcGFyc2VEZWNpbWFsKHgsIHZhbHVlKTtcclxuICAgIGVsc2UgdGhyb3cgRXJyb3IoaW52YWxpZEFyZ3VtZW50ICsgdmFsdWUpO1xyXG4gIH1cclxuXHJcbiAgRGVjaW1hbC5wcm90b3R5cGUgPSBQO1xyXG5cclxuICBEZWNpbWFsLlJPVU5EX1VQID0gMDtcclxuICBEZWNpbWFsLlJPVU5EX0RPV04gPSAxO1xyXG4gIERlY2ltYWwuUk9VTkRfQ0VJTCA9IDI7XHJcbiAgRGVjaW1hbC5ST1VORF9GTE9PUiA9IDM7XHJcbiAgRGVjaW1hbC5ST1VORF9IQUxGX1VQID0gNDtcclxuICBEZWNpbWFsLlJPVU5EX0hBTEZfRE9XTiA9IDU7XHJcbiAgRGVjaW1hbC5ST1VORF9IQUxGX0VWRU4gPSA2O1xyXG4gIERlY2ltYWwuUk9VTkRfSEFMRl9DRUlMID0gNztcclxuICBEZWNpbWFsLlJPVU5EX0hBTEZfRkxPT1IgPSA4O1xyXG5cclxuICBEZWNpbWFsLmNsb25lID0gY2xvbmU7XHJcbiAgRGVjaW1hbC5jb25maWcgPSBEZWNpbWFsLnNldCA9IGNvbmZpZztcclxuXHJcbiAgaWYgKG9iaiA9PT0gdm9pZCAwKSBvYmogPSB7fTtcclxuICBpZiAob2JqKSB7XHJcbiAgICBwcyA9IFsncHJlY2lzaW9uJywgJ3JvdW5kaW5nJywgJ3RvRXhwTmVnJywgJ3RvRXhwUG9zJywgJ0xOMTAnXTtcclxuICAgIGZvciAoaSA9IDA7IGkgPCBwcy5sZW5ndGg7KSBpZiAoIW9iai5oYXNPd25Qcm9wZXJ0eShwID0gcHNbaSsrXSkpIG9ialtwXSA9IHRoaXNbcF07XHJcbiAgfVxyXG5cclxuICBEZWNpbWFsLmNvbmZpZyhvYmopO1xyXG5cclxuICByZXR1cm4gRGVjaW1hbDtcclxufVxyXG5cclxuXHJcbi8qXHJcbiAqIENvbmZpZ3VyZSBnbG9iYWwgc2V0dGluZ3MgZm9yIGEgRGVjaW1hbCBjb25zdHJ1Y3Rvci5cclxuICpcclxuICogYG9iamAgaXMgYW4gb2JqZWN0IHdpdGggb25lIG9yIG1vcmUgb2YgdGhlIGZvbGxvd2luZyBwcm9wZXJ0aWVzLFxyXG4gKlxyXG4gKiAgIHByZWNpc2lvbiAge251bWJlcn1cclxuICogICByb3VuZGluZyAgIHtudW1iZXJ9XHJcbiAqICAgdG9FeHBOZWcgICB7bnVtYmVyfVxyXG4gKiAgIHRvRXhwUG9zICAge251bWJlcn1cclxuICpcclxuICogRS5nLiBEZWNpbWFsLmNvbmZpZyh7IHByZWNpc2lvbjogMjAsIHJvdW5kaW5nOiA0IH0pXHJcbiAqXHJcbiAqL1xyXG5mdW5jdGlvbiBjb25maWcob2JqKSB7XHJcbiAgaWYgKCFvYmogfHwgdHlwZW9mIG9iaiAhPT0gJ29iamVjdCcpIHtcclxuICAgIHRocm93IEVycm9yKGRlY2ltYWxFcnJvciArICdPYmplY3QgZXhwZWN0ZWQnKTtcclxuICB9XHJcbiAgdmFyIGksIHAsIHYsXHJcbiAgICBwcyA9IFtcclxuICAgICAgJ3ByZWNpc2lvbicsIDEsIE1BWF9ESUdJVFMsXHJcbiAgICAgICdyb3VuZGluZycsIDAsIDgsXHJcbiAgICAgICd0b0V4cE5lZycsIC0xIC8gMCwgMCxcclxuICAgICAgJ3RvRXhwUG9zJywgMCwgMSAvIDBcclxuICAgIF07XHJcblxyXG4gIGZvciAoaSA9IDA7IGkgPCBwcy5sZW5ndGg7IGkgKz0gMykge1xyXG4gICAgaWYgKCh2ID0gb2JqW3AgPSBwc1tpXV0pICE9PSB2b2lkIDApIHtcclxuICAgICAgaWYgKG1hdGhmbG9vcih2KSA9PT0gdiAmJiB2ID49IHBzW2kgKyAxXSAmJiB2IDw9IHBzW2kgKyAyXSkgdGhpc1twXSA9IHY7XHJcbiAgICAgIGVsc2UgdGhyb3cgRXJyb3IoaW52YWxpZEFyZ3VtZW50ICsgcCArICc6ICcgKyB2KTtcclxuICAgIH1cclxuICB9XHJcblxyXG4gIGlmICgodiA9IG9ialtwID0gJ0xOMTAnXSkgIT09IHZvaWQgMCkge1xyXG4gICAgICBpZiAodiA9PSBNYXRoLkxOMTApIHRoaXNbcF0gPSBuZXcgdGhpcyh2KTtcclxuICAgICAgZWxzZSB0aHJvdyBFcnJvcihpbnZhbGlkQXJndW1lbnQgKyBwICsgJzogJyArIHYpO1xyXG4gIH1cclxuXHJcbiAgcmV0dXJuIHRoaXM7XHJcbn1cclxuXHJcblxyXG4vLyBDcmVhdGUgYW5kIGNvbmZpZ3VyZSBpbml0aWFsIERlY2ltYWwgY29uc3RydWN0b3IuXHJcbmV4cG9ydCB2YXIgRGVjaW1hbCA9IGNsb25lKGRlZmF1bHRzKTtcclxuXHJcbi8vIEludGVybmFsIGNvbnN0YW50LlxyXG5PTkUgPSBuZXcgRGVjaW1hbCgxKTtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IERlY2ltYWw7XHJcbiJdLCJuYW1lcyI6WyJNQVhfRElHSVRTIiwiZGVmYXVsdHMiLCJwcmVjaXNpb24iLCJyb3VuZGluZyIsInRvRXhwTmVnIiwidG9FeHBQb3MiLCJMTjEwIiwiRGVjaW1hbCIsImV4dGVybmFsIiwiZGVjaW1hbEVycm9yIiwiaW52YWxpZEFyZ3VtZW50IiwiZXhwb25lbnRPdXRPZlJhbmdlIiwibWF0aGZsb29yIiwiTWF0aCIsImZsb29yIiwibWF0aHBvdyIsInBvdyIsImlzRGVjaW1hbCIsIk9ORSIsIkJBU0UiLCJMT0dfQkFTRSIsIk1BWF9TQUZFX0lOVEVHRVIiLCJNQVhfRSIsIlAiLCJhYnNvbHV0ZVZhbHVlIiwiYWJzIiwieCIsImNvbnN0cnVjdG9yIiwicyIsImNvbXBhcmVkVG8iLCJjbXAiLCJ5IiwiaSIsImoiLCJ4ZEwiLCJ5ZEwiLCJlIiwiZCIsImxlbmd0aCIsImRlY2ltYWxQbGFjZXMiLCJkcCIsInciLCJkaXZpZGVkQnkiLCJkaXYiLCJkaXZpZGUiLCJkaXZpZGVkVG9JbnRlZ2VyQnkiLCJpZGl2IiwiQ3RvciIsInJvdW5kIiwiZXF1YWxzIiwiZXEiLCJleHBvbmVudCIsImdldEJhc2UxMEV4cG9uZW50IiwiZ3JlYXRlclRoYW4iLCJndCIsImdyZWF0ZXJUaGFuT3JFcXVhbFRvIiwiZ3RlIiwiaXNJbnRlZ2VyIiwiaXNpbnQiLCJpc05lZ2F0aXZlIiwiaXNuZWciLCJpc1Bvc2l0aXZlIiwiaXNwb3MiLCJpc1plcm8iLCJsZXNzVGhhbiIsImx0IiwibGVzc1RoYW5PckVxdWFsVG8iLCJsdGUiLCJsb2dhcml0aG0iLCJsb2ciLCJiYXNlIiwiciIsInByIiwid3ByIiwiRXJyb3IiLCJsbiIsIm1pbnVzIiwic3ViIiwic3VidHJhY3QiLCJhZGQiLCJtb2R1bG8iLCJtb2QiLCJxIiwidGltZXMiLCJuYXR1cmFsRXhwb25lbnRpYWwiLCJleHAiLCJuYXR1cmFsTG9nYXJpdGhtIiwibmVnYXRlZCIsIm5lZyIsInBsdXMiLCJzZCIsInoiLCJzcXVhcmVSb290Iiwic3FydCIsIm4iLCJ0IiwiZGlnaXRzVG9TdHJpbmciLCJ0b0V4cG9uZW50aWFsIiwic2xpY2UiLCJpbmRleE9mIiwidG9TdHJpbmciLCJtdWwiLCJjYXJyeSIsImsiLCJyTCIsInhkIiwieWQiLCJwdXNoIiwicG9wIiwic2hpZnQiLCJ0b0RlY2ltYWxQbGFjZXMiLCJ0b2RwIiwicm0iLCJjaGVja0ludDMyIiwic3RyIiwidG9GaXhlZCIsInRvSW50ZWdlciIsInRvaW50IiwidG9OdW1iZXIiLCJ0b1Bvd2VyIiwic2lnbiIsInlJc0ludCIsImd1YXJkIiwieW4iLCJjZWlsIiwidHJ1bmNhdGUiLCJtYXgiLCJ0b1ByZWNpc2lvbiIsInRvU2lnbmlmaWNhbnREaWdpdHMiLCJ0b3NkIiwidmFsdWVPZiIsInZhbCIsInRvSlNPTiIsIlN5bWJvbCIsImZvciIsImxlbiIsInJldmVyc2UiLCJ1bnNoaWZ0IiwibWluIiwid3MiLCJpbmRleE9mTGFzdFdvcmQiLCJnZXRaZXJvU3RyaW5nIiwibXVsdGlwbHlJbnRlZ2VyIiwidGVtcCIsImNvbXBhcmUiLCJhIiwiYiIsImFMIiwiYkwiLCJwcm9kIiwicHJvZEwiLCJxZCIsInJlbSIsInJlbUwiLCJyZW0wIiwieGkiLCJ4TCIsInlkMCIsInlMIiwieXoiLCJkZW5vbWluYXRvciIsInN1bSIsImdldExuMTAiLCJ6cyIsImMiLCJjMCIsIm51bWVyYXRvciIsIngyIiwiY2hhckF0IiwicGFyc2VEZWNpbWFsIiwicmVwbGFjZSIsInNlYXJjaCIsInN1YnN0cmluZyIsImNoYXJDb2RlQXQiLCJyZCIsImRvUm91bmQiLCJ4ZGkiLCJ4ZSIsInhMVHkiLCJpc0V4cCIsImFyciIsImNsb25lIiwib2JqIiwicCIsInBzIiwidmFsdWUiLCJ0ZXN0IiwicHJvdG90eXBlIiwiUk9VTkRfVVAiLCJST1VORF9ET1dOIiwiUk9VTkRfQ0VJTCIsIlJPVU5EX0ZMT09SIiwiUk9VTkRfSEFMRl9VUCIsIlJPVU5EX0hBTEZfRE9XTiIsIlJPVU5EX0hBTEZfRVZFTiIsIlJPVU5EX0hBTEZfQ0VJTCIsIlJPVU5EX0hBTEZfRkxPT1IiLCJjb25maWciLCJzZXQiLCJoYXNPd25Qcm9wZXJ0eSIsInYiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/decimal.js-light/decimal.mjs\n");

/***/ })

};
;