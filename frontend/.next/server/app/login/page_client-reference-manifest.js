globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/login/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/../node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/../node_modules/next/dist/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/../node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/../node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../node_modules/next/dist/client/components/not-found-boundary.js":{"*":{"id":"(ssr)/../node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/../node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js":{"*":{"id":"(ssr)/../node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/providers.tsx":{"*":{"id":"(ssr)/./src/components/providers.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/../node_modules/react-hot-toast/dist/index.mjs":{"*":{"id":"(ssr)/../node_modules/react-hot-toast/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/login/page.tsx":{"*":{"id":"(ssr)/./src/app/login/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"/Users/<USER>/Documents/Agentic Talent Pro/node_modules/next/dist/client/components/app-router.js":{"id":"(app-pages-browser)/../node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/Agentic Talent Pro/node_modules/next/dist/esm/client/components/app-router.js":{"id":"(app-pages-browser)/../node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/Agentic Talent Pro/node_modules/next/dist/client/components/error-boundary.js":{"id":"(app-pages-browser)/../node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/Agentic Talent Pro/node_modules/next/dist/esm/client/components/error-boundary.js":{"id":"(app-pages-browser)/../node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/Agentic Talent Pro/node_modules/next/dist/client/components/layout-router.js":{"id":"(app-pages-browser)/../node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/Agentic Talent Pro/node_modules/next/dist/esm/client/components/layout-router.js":{"id":"(app-pages-browser)/../node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/Agentic Talent Pro/node_modules/next/dist/client/components/not-found-boundary.js":{"id":"(app-pages-browser)/../node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/Agentic Talent Pro/node_modules/next/dist/esm/client/components/not-found-boundary.js":{"id":"(app-pages-browser)/../node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/Agentic Talent Pro/node_modules/next/dist/client/components/render-from-template-context.js":{"id":"(app-pages-browser)/../node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/Agentic Talent Pro/node_modules/next/dist/esm/client/components/render-from-template-context.js":{"id":"(app-pages-browser)/../node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/Agentic Talent Pro/node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js":{"id":"(app-pages-browser)/../node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/Agentic Talent Pro/node_modules/next/dist/esm/client/components/static-generation-searchparams-bailout-provider.js":{"id":"(app-pages-browser)/../node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Documents/Agentic Talent Pro/frontend/src/components/providers.tsx":{"id":"(app-pages-browser)/./src/components/providers.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/Documents/Agentic Talent Pro/node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/../node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/Documents/Agentic Talent Pro/frontend/src/app/globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/Documents/Agentic Talent Pro/node_modules/react-hot-toast/dist/index.mjs":{"id":"(app-pages-browser)/../node_modules/react-hot-toast/dist/index.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/Documents/Agentic Talent Pro/frontend/src/app/login/page.tsx":{"id":"(app-pages-browser)/./src/app/login/page.tsx","name":"*","chunks":["app/login/page","static/chunks/app/login/page.js"],"async":false}},"entryCSSFiles":{"/Users/<USER>/Documents/Agentic Talent Pro/frontend/src/app/layout":["static/css/app/layout.css"],"/Users/<USER>/Documents/Agentic Talent Pro/frontend/src/app/page":[],"/Users/<USER>/Documents/Agentic Talent Pro/frontend/src/app/not-found":[],"/Users/<USER>/Documents/Agentic Talent Pro/frontend/src/app/login/page":[]}}