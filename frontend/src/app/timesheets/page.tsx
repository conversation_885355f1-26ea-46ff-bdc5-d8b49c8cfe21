'use client';

import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/hooks/useAuth';
import { Plus, Search, Filter, Eye, Edit, Clock, Calendar, CheckCircle } from 'lucide-react';

export default function TimesheetsPage() {
  const { hasRole } = useAuth();

  // Mock data for demonstration
  const timesheets = [
    {
      id: 1,
      weekEnding: '2024-01-14',
      resource: '<PERSON>',
      project: 'E-commerce Platform',
      totalHours: 40,
      regularHours: 40,
      overtimeHours: 0,
      status: 'SUBMITTED',
      submittedAt: '2024-01-15T09:00:00Z',
    },
    {
      id: 2,
      weekEnding: '2024-01-07',
      resource: '<PERSON>',
      project: 'E-commerce Platform',
      totalHours: 45,
      regularHours: 40,
      overtimeHours: 5,
      status: 'APPROVED',
      submittedAt: '2024-01-08T10:30:00Z',
      approvedAt: '2024-01-09T14:15:00Z',
    },
    {
      id: 3,
      weekEnding: '2024-01-07',
      resource: 'Mike Johnson',
      project: 'E-commerce Platform',
      totalHours: 38,
      regularHours: 38,
      overtimeHours: 0,
      status: 'DRAFT',
      submittedAt: null,
    },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'DRAFT': return 'status-draft';
      case 'SUBMITTED': return 'status-pending';
      case 'APPROVED': return 'status-completed';
      case 'REJECTED': return 'status-cancelled';
      default: return 'status-inactive';
    }
  };

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Timesheets</h1>
            <p className="text-gray-600">Track and manage time entries</p>
          </div>
          {hasRole(['RESOURCE']) && (
            <Button className="redwood-button-primary">
              <Plus className="h-4 w-4 mr-2" />
              New Timesheet
            </Button>
          )}
        </div>

        {/* Filters */}
        <Card className="redwood-card">
          <CardContent className="p-4">
            <div className="flex items-center space-x-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search timesheets..."
                    className="redwood-input pl-10 w-full"
                  />
                </div>
              </div>
              <select className="redwood-input">
                <option value="">All Status</option>
                <option value="DRAFT">Draft</option>
                <option value="SUBMITTED">Submitted</option>
                <option value="APPROVED">Approved</option>
                <option value="REJECTED">Rejected</option>
              </select>
              <input
                type="week"
                className="redwood-input"
                placeholder="Week ending"
              />
              <Button variant="outline">
                <Filter className="h-4 w-4 mr-2" />
                Filter
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Timesheets List */}
        <div className="grid gap-4">
          {timesheets.map((timesheet) => (
            <Card key={timesheet.id} className="redwood-card">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div>
                    <CardTitle className="text-lg text-gray-900">
                      Week Ending {timesheet.weekEnding}
                    </CardTitle>
                    <CardDescription className="mt-1">
                      {timesheet.resource} • {timesheet.project}
                    </CardDescription>
                  </div>
                  <Badge className={getStatusColor(timesheet.status)}>
                    {timesheet.status}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                  <div className="flex items-center text-sm">
                    <Clock className="h-4 w-4 text-gray-400 mr-2" />
                    <span className="text-gray-600">Total Hours:</span>
                    <span className="ml-1 font-bold text-lg">{timesheet.totalHours}h</span>
                  </div>
                  
                  <div className="flex items-center text-sm">
                    <span className="text-gray-600">Regular:</span>
                    <span className="ml-1 font-medium">{timesheet.regularHours}h</span>
                  </div>

                  <div className="flex items-center text-sm">
                    <span className="text-gray-600">Overtime:</span>
                    <span className="ml-1 font-medium">{timesheet.overtimeHours}h</span>
                  </div>

                  <div className="flex items-center text-sm">
                    <Calendar className="h-4 w-4 text-gray-400 mr-2" />
                    <span className="text-gray-600">Submitted:</span>
                    <span className="ml-1 font-medium">
                      {timesheet.submittedAt ? new Date(timesheet.submittedAt).toLocaleDateString() : 'Not submitted'}
                    </span>
                  </div>
                </div>

                {/* Time Breakdown */}
                <div className="mb-4">
                  <h4 className="text-sm font-medium text-gray-700 mb-2">Daily Breakdown</h4>
                  <div className="grid grid-cols-7 gap-2">
                    {['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'].map((day, index) => (
                      <div key={day} className="text-center">
                        <div className="text-xs text-gray-500 mb-1">{day}</div>
                        <div className="bg-gray-100 rounded p-2 text-sm font-medium">
                          {index < 5 ? '8h' : '0h'}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex space-x-2">
                  <Button variant="outline" size="sm">
                    <Eye className="h-4 w-4 mr-1" />
                    View Details
                  </Button>
                  
                  {timesheet.status === 'DRAFT' && hasRole(['RESOURCE']) && (
                    <>
                      <Button variant="outline" size="sm">
                        <Edit className="h-4 w-4 mr-1" />
                        Edit
                      </Button>
                      <Button className="redwood-button-primary" size="sm">
                        Submit for Approval
                      </Button>
                    </>
                  )}
                  
                  {timesheet.status === 'SUBMITTED' && hasRole(['ADMIN', 'PROJECT_MANAGER']) && (
                    <>
                      <Button className="bg-green-600 hover:bg-green-700 text-white" size="sm">
                        <CheckCircle className="h-4 w-4 mr-1" />
                        Approve
                      </Button>
                      <Button variant="outline" size="sm" className="text-red-600 hover:text-red-700">
                        Reject
                      </Button>
                    </>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {timesheets.length === 0 && (
          <Card className="redwood-card">
            <CardContent className="text-center py-12">
              <p className="text-gray-500 mb-4">No timesheets found</p>
              {hasRole(['RESOURCE']) && (
                <Button className="redwood-button-primary">
                  <Plus className="h-4 w-4 mr-2" />
                  Create First Timesheet
                </Button>
              )}
            </CardContent>
          </Card>
        )}
      </div>
    </DashboardLayout>
  );
}
