'use client';

import { DashboardLayout } from '@/components/layout/dashboard-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/hooks/useAuth';
import { Plus, Search, Filter, Eye, Edit, Clock, User, Calendar } from 'lucide-react';

export default function TasksPage() {
  const { hasRole } = useAuth();

  // Mock data for demonstration
  const tasks = [
    {
      id: 1,
      title: 'Design User Interface',
      description: 'Create wireframes and mockups for the new dashboard',
      status: 'IN_PROGRESS',
      priority: 'HIGH',
      assignee: '<PERSON>',
      project: 'E-commerce Platform',
      dueDate: '2024-01-15',
      estimatedHours: 16,
      actualHours: 8,
    },
    {
      id: 2,
      title: 'API Integration',
      description: 'Integrate payment gateway APIs',
      status: 'TODO',
      priority: 'MEDIUM',
      assignee: '<PERSON>',
      project: 'E-commerce Platform',
      dueDate: '2024-01-20',
      estimatedHours: 24,
      actualHours: 0,
    },
    {
      id: 3,
      title: 'Database Optimization',
      description: 'Optimize database queries for better performance',
      status: 'COMPLETED',
      priority: 'LOW',
      assignee: 'Mike <PERSON>',
      project: 'E-commerce Platform',
      dueDate: '2024-01-10',
      estimatedHours: 12,
      actualHours: 14,
    },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'TODO': return 'status-pending';
      case 'IN_PROGRESS': return 'status-active';
      case 'COMPLETED': return 'status-completed';
      case 'CANCELLED': return 'status-cancelled';
      default: return 'status-inactive';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'LOW': return 'priority-low';
      case 'MEDIUM': return 'priority-medium';
      case 'HIGH': return 'priority-high';
      case 'CRITICAL': return 'priority-critical';
      default: return 'priority-low';
    }
  };

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Tasks</h1>
            <p className="text-gray-600">Manage and track task progress</p>
          </div>
          {hasRole(['ADMIN', 'PROJECT_MANAGER']) && (
            <Button className="redwood-button-primary">
              <Plus className="h-4 w-4 mr-2" />
              New Task
            </Button>
          )}
        </div>

        {/* Filters */}
        <Card className="redwood-card">
          <CardContent className="p-4">
            <div className="flex items-center space-x-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search tasks..."
                    className="redwood-input pl-10 w-full"
                  />
                </div>
              </div>
              <select className="redwood-input">
                <option value="">All Status</option>
                <option value="TODO">To Do</option>
                <option value="IN_PROGRESS">In Progress</option>
                <option value="COMPLETED">Completed</option>
                <option value="CANCELLED">Cancelled</option>
              </select>
              <select className="redwood-input">
                <option value="">All Priority</option>
                <option value="LOW">Low</option>
                <option value="MEDIUM">Medium</option>
                <option value="HIGH">High</option>
                <option value="CRITICAL">Critical</option>
              </select>
              <Button variant="outline">
                <Filter className="h-4 w-4 mr-2" />
                Filter
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Tasks List */}
        <div className="grid gap-4">
          {tasks.map((task) => (
            <Card key={task.id} className="redwood-card">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <CardTitle className="text-lg text-gray-900">{task.title}</CardTitle>
                    <CardDescription className="mt-1">
                      {task.description}
                    </CardDescription>
                  </div>
                  <div className="flex space-x-2">
                    <Badge className={getStatusColor(task.status)}>
                      {task.status.replace('_', ' ')}
                    </Badge>
                    <Badge className={getPriorityColor(task.priority)}>
                      {task.priority}
                    </Badge>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                  <div className="flex items-center text-sm">
                    <User className="h-4 w-4 text-gray-400 mr-2" />
                    <span className="text-gray-600">Assignee:</span>
                    <span className="ml-1 font-medium">{task.assignee}</span>
                  </div>
                  
                  <div className="flex items-center text-sm">
                    <Calendar className="h-4 w-4 text-gray-400 mr-2" />
                    <span className="text-gray-600">Due:</span>
                    <span className="ml-1 font-medium">{task.dueDate}</span>
                  </div>

                  <div className="flex items-center text-sm">
                    <Clock className="h-4 w-4 text-gray-400 mr-2" />
                    <span className="text-gray-600">Hours:</span>
                    <span className="ml-1 font-medium">{task.actualHours}/{task.estimatedHours}h</span>
                  </div>

                  <div className="text-sm">
                    <span className="text-gray-600">Project:</span>
                    <span className="ml-1 font-medium">{task.project}</span>
                  </div>
                </div>

                {/* Progress Bar */}
                <div className="mb-4">
                  <div className="flex items-center justify-between text-sm mb-1">
                    <span className="text-gray-600">Progress</span>
                    <span className="font-medium">
                      {Math.round((task.actualHours / task.estimatedHours) * 100)}%
                    </span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-red-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${Math.min((task.actualHours / task.estimatedHours) * 100, 100)}%` }}
                    ></div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex space-x-2">
                  <Button variant="outline" size="sm">
                    <Eye className="h-4 w-4 mr-1" />
                    View
                  </Button>
                  {hasRole(['ADMIN', 'PROJECT_MANAGER', 'RESOURCE']) && (
                    <Button variant="outline" size="sm">
                      <Edit className="h-4 w-4 mr-1" />
                      Edit
                    </Button>
                  )}
                  {task.status !== 'COMPLETED' && hasRole(['RESOURCE']) && (
                    <Button className="redwood-button-primary" size="sm">
                      <Clock className="h-4 w-4 mr-1" />
                      Log Time
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {tasks.length === 0 && (
          <Card className="redwood-card">
            <CardContent className="text-center py-12">
              <p className="text-gray-500 mb-4">No tasks found</p>
              {hasRole(['ADMIN', 'PROJECT_MANAGER']) && (
                <Button className="redwood-button-primary">
                  <Plus className="h-4 w-4 mr-2" />
                  Create First Task
                </Button>
              )}
            </CardContent>
          </Card>
        )}
      </div>
    </DashboardLayout>
  );
}
