import axios, { AxiosError, AxiosResponse } from 'axios';
import { ApiResponse } from '@agentic-talent-pro/shared';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3003/api';

// Create axios instance
export const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    if (typeof window !== 'undefined') {
      const token = localStorage.getItem('token');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
api.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  (error: AxiosError) => {
    if (error.response?.status === 401 && typeof window !== 'undefined') {
      // Clear token and redirect to login
      localStorage.removeItem('token');
      localStorage.removeItem('refreshToken');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// API helper functions
export const apiRequest = {
  get: async <T>(url: string, params?: any): Promise<T> => {
    const response = await api.get<T>(url, { params });
    return response.data as T;
  },

  post: async <T>(url: string, data?: any): Promise<T> => {
    const response = await api.post<T>(url, data);
    return response.data as T;
  },

  put: async <T>(url: string, data?: any): Promise<T> => {
    const response = await api.put<T>(url, data);
    return response.data as T;
  },

  patch: async <T>(url: string, data?: any): Promise<T> => {
    const response = await api.patch<T>(url, data);
    return response.data as T;
  },

  delete: async <T>(url: string): Promise<T> => {
    const response = await api.delete<T>(url);
    return response.data as T;
  },
};

// Auth API
export const authApi = {
  login: (credentials: { email: string; password: string }) =>
    apiRequest.post('/auth/login', credentials),

  register: (userData: {
    email: string;
    password: string;
    firstName: string;
    lastName: string;
    role: string;
    phone?: string;
  }) => apiRequest.post('/auth/register', userData),

  refreshToken: (refreshToken: string) =>
    apiRequest.post('/auth/refresh', { refreshToken }),

  getProfile: () => apiRequest.get('/auth/profile'),

  logout: () => apiRequest.post('/auth/logout'),
};

// Contracts API
export const contractsApi = {
  getAll: (params?: any) => apiRequest.get('/contracts', params),
  getById: (id: string) => apiRequest.get(`/contracts/${id}`),
  create: (data: any) => apiRequest.post('/contracts', data),
  update: (id: string, data: any) => apiRequest.put(`/contracts/${id}`, data),
  delete: (id: string) => apiRequest.delete(`/contracts/${id}`),
};

// Projects API
export const projectsApi = {
  getAll: (params?: any) => apiRequest.get('/projects', params),
  getById: (id: string) => apiRequest.get(`/projects/${id}`),
  create: (data: any) => apiRequest.post('/projects', data),
  update: (id: string, data: any) => apiRequest.put(`/projects/${id}`, data),
  delete: (id: string) => apiRequest.delete(`/projects/${id}`),
  assignResource: (projectId: string, data: any) =>
    apiRequest.post(`/projects/${projectId}/resources`, data),
};

// Resources API
export const resourcesApi = {
  getAll: (params?: any) => apiRequest.get('/resources', params),
  getById: (id: string) => apiRequest.get(`/resources/${id}`),
  create: (data: any) => apiRequest.post('/resources', data),
  update: (id: string, data: any) => apiRequest.put(`/resources/${id}`, data),
  delete: (id: string) => apiRequest.delete(`/resources/${id}`),
  search: (criteria: any) => apiRequest.post('/resources/search', criteria),
};

// Tasks API
export const tasksApi = {
  getAll: (params?: any) => apiRequest.get('/tasks', params),
  getById: (id: string) => apiRequest.get(`/tasks/${id}`),
  create: (data: any) => apiRequest.post('/tasks', data),
  update: (id: string, data: any) => apiRequest.put(`/tasks/${id}`, data),
  delete: (id: string) => apiRequest.delete(`/tasks/${id}`),
};

// Timesheets API
export const timesheetsApi = {
  getAll: (params?: any) => apiRequest.get('/timesheets', params),
  getById: (id: string) => apiRequest.get(`/timesheets/${id}`),
  create: (data: any) => apiRequest.post('/timesheets', data),
  update: (id: string, data: any) => apiRequest.put(`/timesheets/${id}`, data),
  delete: (id: string) => apiRequest.delete(`/timesheets/${id}`),
  submit: (id: string) => apiRequest.post(`/timesheets/${id}/submit`),
  approve: (id: string) => apiRequest.post(`/timesheets/${id}/approve`),
  reject: (id: string, reason: string) =>
    apiRequest.post(`/timesheets/${id}/reject`, { reason }),
};

// Invoices API
export const invoicesApi = {
  getAll: (params?: any) => apiRequest.get('/invoices', params),
  getById: (id: string) => apiRequest.get(`/invoices/${id}`),
  generate: (data: any) => apiRequest.post('/invoices/generate', data),
  update: (id: string, data: any) => apiRequest.put(`/invoices/${id}`, data),
  delete: (id: string) => apiRequest.delete(`/invoices/${id}`),
};

// Vendors API
export const vendorsApi = {
  getAll: (params?: any) => apiRequest.get('/vendors', params),
  getById: (id: string) => apiRequest.get(`/vendors/${id}`),
  create: (data: any) => apiRequest.post('/vendors', data),
  update: (id: string, data: any) => apiRequest.put(`/vendors/${id}`, data),
  delete: (id: string) => apiRequest.delete(`/vendors/${id}`),
};

// Skills API
export const skillsApi = {
  getAll: (params?: any) => apiRequest.get('/skills', params),
  getById: (id: string) => apiRequest.get(`/skills/${id}`),
  create: (data: any) => apiRequest.post('/skills', data),
  update: (id: string, data: any) => apiRequest.put(`/skills/${id}`, data),
  delete: (id: string) => apiRequest.delete(`/skills/${id}`),
};

// Users API
export const usersApi = {
  getAll: (params?: any) => apiRequest.get('/users', params),
  getById: (id: string) => apiRequest.get(`/users/${id}`),
  updateStatus: (id: string, status: string) =>
    apiRequest.patch(`/users/${id}/status`, { status }),
};

// Dashboard API
export const dashboardApi = {
  getStats: () => apiRequest.get('/dashboard/stats'),
  getCharts: () => apiRequest.get('/dashboard/charts'),
};
