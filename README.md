# Agentic Talent Pro

A comprehensive production-ready application for Contract Management, Project Management, HRMS, and Billing Management.

## Features

### 🔐 Contract Management
- CRUD operations for contracts
- Contract templates and workflows
- Client management
- Contract analytics

### 📊 Project Management
- Project creation from contracts
- Resource planning with skills matching
- Task management and assignment
- Resource allocation and utilization tracking

### 👥 Manpower Solution (HRMS)
- Workforce management with comprehensive employee profiles
- Recruitment and hiring workflows
- Resource utilization tracking
- Calendar-based timesheet management
- Approval workflows
- Vendor onboarding and management

### 💰 Billing Management
- Automated invoice generation
- Payroll processing
- Contract-based billing
- Penalties and arrears management

## Technology Stack

- **Frontend**: Next.js 14, React 18, TypeScript, Tailwind CSS
- **Backend**: Node.js, Express, TypeScript
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: NextAuth.js
- **UI Components**: shadcn/ui
- **Calendar**: FullCalendar
- **Charts**: Recharts

## Quick Start

### Prerequisites
- Node.js 18+
- PostgreSQL 14+
- npm 9+

### Installation

1. Clone the repository
```bash
git clone <repository-url>
cd agentic-talent-pro
```

2. Install dependencies
```bash
npm install
```

3. Set up environment variables
```bash
cp backend/.env.example backend/.env
cp frontend/.env.example frontend/.env.local
```

4. Set up the database
```bash
npm run db:migrate
npm run db:seed
```

5. Start the development servers
```bash
npm run dev
```

The application will be available at:
- Frontend: http://localhost:3000
- Backend API: http://localhost:3001
- Database Studio: http://localhost:5555 (run `npm run db:studio`)

## Project Structure

```
agentic-talent-pro/
├── frontend/          # Next.js frontend application
├── backend/           # Express.js backend API
├── shared/            # Shared TypeScript types and utilities
├── docs/              # API documentation
└── database/          # Database migrations and seeds
```

## Development

### Running Tests
```bash
npm run test
```

### Linting
```bash
npm run lint
```

### Database Operations
```bash
npm run db:migrate     # Run migrations
npm run db:generate    # Generate Prisma client
npm run db:studio      # Open Prisma Studio
npm run db:seed        # Seed database with sample data
```

## Deployment

### Production Build
```bash
npm run build
```

### Environment Variables
See `.env.example` files in backend and frontend directories for required environment variables.

## API Documentation

API documentation is available at `/api/docs` when running the backend server.

## License

Private - All rights reserved
